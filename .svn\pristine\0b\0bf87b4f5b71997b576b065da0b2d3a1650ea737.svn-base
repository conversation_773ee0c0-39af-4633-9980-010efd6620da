/**
* @file     edgeosunitif_in.h
* @brief    unitif module for sevice
* <AUTHOR>
* @date     2020_10_20
* @version  1.0
* @copyright V1.0  Copyright(C) 2020 NVR All rights reserved.
*/

#ifndef _EDGEOSUNITIF_IN_H_
#define _EDGEOSUNITIF_IN_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "debuglog.h"

#define UNITIFERR(args, ...)   DebugLogPrint(DEBUG_LOG_MOD_UNITIF, LOG_LEVEL_ERR, "%s[L:%d] "args,_NVRFUN_,__LINE__,##__VA_ARGS__);
#define UNITIFIMP(args, ...)   DebugLogPrint(DEBUG_LOG_MOD_UNITIF, LOG_LEVEL_IMP, "%s[L:%d] "args,_NVRFUN_,__LINE__,##__VA_ARGS__);
#define UNITIFDBG(args, ...)   DebugLogPrint(DEBUG_LOG_MOD_UNITIF, LOG_LEVEL_DEBUG, "%s[L:%d] "args,_NVRFUN_,__LINE__,##__VA_ARGS__);
#define UNITIFFRQ(args, ...)   DebugLogPrint(DEBUG_LOG_MOD_UNITIF, LOG_LEVEL_TEMP, "%s[L:%d] "args,_NVRFUN_,__LINE__,##__VA_ARGS__);


#define UNITIFFLASHERR(args, ...)       LogFlash(FLASH_LOG_ERR, "EDGEOSUNITIF", args, ##__VA_ARGS__);
#define UNITIFFLASHNOTICE(args, ...)    LogFlash(FLASH_LOG_NOTICE, "EDGEOSUNITIF", args, ##__VA_ARGS__);
#define UNIFIFMEMNOTICE(args, ...)      LogMem(MEM_LOG_NOTICE_CORE, DEBUG_LOG_MOD_UNITIF, "EDGEOSUNITIF", args, ##__VA_ARGS__);
#define UNITIFMEMAPI(args, ...)         LogApi(MEM_LOG_API, DEBUG_LOG_MOD_UNITIF, "EDGEOSUNITIF", args, ##__VA_ARGS__); 

#define UNITIF_ASSERT(p) \
if (NULL == p)			\
{	\
	UNITIFERR("[%s]%s assert failed(line:%d)\n", __FILE__,__FUNCTION__, __LINE__);	\
	return NVR_ERR__ASSERT;												\
}	\




#ifdef __cplusplus
}
#endif


#endif

























