#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <errno.h>

#include "vslls.h"
#include "dm.h"
#include "netsvc.h"
#include "raid.h"

static int local_start_net_svc(struct net_svc *netsvc)
{
	return SUCCESS;
}

static int local_stop_net_svc(struct net_svc *netsvc, int force)
{
	return SUCCESS;
}

static int local_set_allow_initiator_ip(struct net_svc *netsvc, const char *initiator_ipaddr)
{
	return SUCCESS;
}

static int local_set_allow_target_ip(struct net_svc *netsvc, const char *target_ipaddr)
{
	return SUCCESS;
}

static int local_get_netsvc_info(struct net_svc *netsvc, struct netsvc_info *info)
{
	return SUCCESS;
}

static int local_create_netsvc(struct net_svc *netsvc)
{
	return SUCCESS;
}

static int local_delete_netsvc(struct net_svc *netsvc)
{
	return SUCCESS;
}

static int local_init_per_netsvc(struct net_svc *netsvc)
{
	netsvc->type = netsvc->net_per->type;

	return SUCCESS;
}

static int local_free_per_netsvc(struct net_svc *netsvc)
{
	return SUCCESS;
}

static int local_service_start()
{
	return SUCCESS;
}

static int local_service_stop()
{
	return SUCCESS;
}

static int local_service_status()
{
	return SUCCESS;
}

static struct net_pers_op local_netsvc_op = {
	.set_allow_initiator_ip = local_set_allow_initiator_ip,
	.set_allow_target_ip = local_set_allow_target_ip,

	.start_net_svc = local_start_net_svc,
	.stop_net_svc = local_stop_net_svc,
	.create_netsvc = local_create_netsvc,
	.delete_netsvc = local_delete_netsvc,

	.init_per_netsvc = local_init_per_netsvc,
	.free_per_netsvc = local_free_per_netsvc,

	.get_netsvc_info = local_get_netsvc_info,

	.service_start = local_service_start,
	.service_stop = local_service_stop,
	.service_status = local_service_status,

	.type = LV_TYPE_LOCAL,
};

void local_netsvc_init()
{
	register_net_svc(&local_netsvc_op);
}

