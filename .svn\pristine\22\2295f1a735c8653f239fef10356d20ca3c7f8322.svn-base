

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := mapp


## Define debugging symbols
DEBUG = 0
#FSANITIZE = 1
LINUX_COMPILER = _SSC339G_
CFLAGS += -D_SSC339G_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables

## Object files that compose the target(s)

OBJS := $(SRC_DIR)/mobileapp\
        $(SRC_DIR)/mapperror\
		$(SRC_DIR)/mappstream\
		$(SRC_DIR)/mapprec\
		$(SRC_DIR)/mappnotice\
		$(SRC_DIR)/mappcfg\


## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/tutk\
		$(CURDIR)/../../../10-common/include/cbb/\
		$(CURDIR)/../../../10-common/include/cbb/curl\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../nvrrec/include\
		$(CURDIR)/../../nvrpui/include\
		$(CURDIR)/../../nvrsys/include\
		





INSTALL_LIB_PATH = ../../../10-common/lib/release/ssc339g/tutk/applib
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


