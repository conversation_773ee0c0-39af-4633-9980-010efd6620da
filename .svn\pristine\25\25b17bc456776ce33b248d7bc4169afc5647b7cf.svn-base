#ifndef __MAPPERROR_h__
#define __MAPPERROR_h__


#ifdef __cplusplus
extern "C" {
#endif






/**
 *@brief  IOTC Errorno to string
 *@param[in]  int nErrorNo  IOTC errorno,see IOTCCommon.h
 *@param[OUT] char *pchErrStr  Errorno corresponds to errorstring
 *@return
 *@ref
 *@see
 *@note
 */
void MappIotcErrToStr(int nErrorNo, char *pchErrStr);
/**
 *@brief  AV module Errorno to string
 *@param[in]  int nErrorNo  AV errorno,See AVCommon.h
 *@param[OUT] char *pchErrStr Errorno corresponds to errorstring
 *@return
 *@ref
 *@see
 *@note
 */
void MappAvErrToStr(int nErrorNo, char *pchErrStr);

#ifdef __cplusplus
}
#endif ///<__cplusplus
#endif

