path="../../10-common/version/compileinfo/nvrlib_netra8107.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_dev_linux for netra8107           =
echo ==============================================

echo "============compile libnvrdev netra8107============">>../$path

make -e DEBUG=0 -f makefile_netra8107 clean
make -e DEBUG=0 -f makefile_netra8107 2>>../$path

cp -L -r -f libnvrdev.so ../../../10-common/lib/release/netra8107/

cd ..
