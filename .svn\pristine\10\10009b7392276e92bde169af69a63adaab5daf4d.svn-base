###
### Copyright (c) 2004 Keda Telecom, Inc.
###

#########################################################################
###
###  DESCRIPTION:
###    Common definitions for all Makefiles in OSP linux project.
###
#########################################################################

TOP := ..

COMM_DIR := ./

SRC_DIR := $(TOP)/src


## Name and type of the target for this Makefile

SO_TARGET      := cgiextapp

## Define debugging symbols
DEBUG = 0
#LINUX_COMPILER= _ARM_HIS3536_
PWLIB_SUPPORT = 0
USE_OSP = 0


CFLAGS += -D_LINUX -Wall -D_SKYLATE_ -DLESS_SECURITY -fno-omit-frame-pointer

OBJS := $(SRC_DIR)/cgiextapp                       	\
        $(SRC_DIR)/cgiextapp_webservice            	\
        $(SRC_DIR)/cgiextapp_config                	\
        $(SRC_DIR)/cgiextapp_tool                  	\
        $(SRC_DIR)/cgiextapp_handler_framework     	\
	    $(SRC_DIR)/cgiextapp_context	            \
		$(SRC_DIR)/cgiextapp_xml_helper				\
		$(SRC_DIR)/cgiextapp_enum_str_conv	        \
		$(SRC_DIR)/cgiextapp_getcap_handler	        \
		$(SRC_DIR)/cgiextapp_plugin_handler	        \

## Libraries to include in shared object file
        
#LIBS :=  

## Add driver-specific include directory to the search path
##ARC_LIBS += 
INC_PATH += ../../../10-common/include/cbb/appclt         \
            ../../../10-common/include/cbb/protobuf       \
            ../../../10-common/include/cbb/osp            \
            ../../../10-common/include/cbb/mediaswitch		\
			../../../10-common/include/cbb/mxml           \
		    ../../../10-common/include/cbb/debuglog       \
			../../../10-common/include/cbb/goahead/linux  \
			../../../10-common/include/cbb			      \
            ../../../10-common/include/hal                \
            ../../../10-common/include/hal/drvlib_64         \
            ../../../10-common/include/hal/netcbb         \
            ../../../10-common/include/hal/ispctrl        \
            ../../../10-common/include/hal/mediactrl      \
            ../../../10-common/include/system             \
            ../../../10-common/include/service            \
            ../../../10-common/include/app                \
            ../include
            

LIB_PATH := ../../../10-common/lib/release/ubuntu_64

LIBS := goaheadhelper	\
	go         \
        mxml       \
        appbase    \
		debuglog   \
        nvrusrmgr  \
		nvrmpu     \
		nvrnetwork

INSTALL_LIB_PATH = ../../../10-common/lib/release/ubuntu_64/applib

include $(COMM_DIR)/makelib.mk

