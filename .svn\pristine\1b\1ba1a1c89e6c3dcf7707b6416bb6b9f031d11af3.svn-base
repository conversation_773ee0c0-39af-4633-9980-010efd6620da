

TOP := ../

COMM_DIR := ./

SRC_DIR := $(TOP)/src
PROTO_DIR := $(TOP)/ProtoFile

## Name and type of the target for this Makefile

SO_TARGET      := pubsecapp

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER= _HIS3516DV300_
PWLIB_SUPPORT = 0

CFLAGS += -D_LINUX -DLITTLE_ENDIAN   -D__I18N__
OBJS := $(SRC_DIR)/pubsecapp \
		$(SRC_DIR)/pubsecapp_config \
		$(SRC_DIR)/pubsecapp_msg \
		$(SRC_DIR)/pubsecapp_tool \
		$(PROTO_DIR)/pubsecapp.pb-c
		
        

## Libraries to include in shared object file
   

## Add driver-specific include directory to the search path
##ARC_LIBS += 
INC_PATH += ../../../10-common/include/app					\
			../../../10-common/include/system				\
			../../../10-common/include/cbb					\
			../../../10-common/include/cbb/cjson			\
			../../../10-common/include/cbb/mediactrl		\
			../../../10-common/include/cbb/mediaswitch		\
			../../../10-common/include/cbb/debuglog			\
			../../../10-common/include/cbb/protobuf			\
			../../../10-common/include/cbb/osp				\
			../../../10-common/include/service				\
			../ProtoFile									\
			../include			

INSTALL_LIB_PATH = ../../../10-common/lib/release/his3516dv300/i18n/applib
				
LIB_PATH := ../../../10-common/lib/release/his3516dv300

LIBS :=	pubsecstack appbase nvrsys go osp cjson debuglog

include $(COMM_DIR)/../../makelib.mk

