path="../../10-common/version/compileinfo/nvrlib_ce3226.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_chipsupgrade_linux for ce3226         =
echo ==============================================

echo "============compile nvrchipsupgrade ce3226============">>../$path

make -e DEBUG=0 -f makefile_ce3226 clean
make -e DEBUG=0 -f makefile_ce3226 2>>../$path

cp -L -r -f nvrchipsupgrade ../../../10-common/version/release/ce3226/public/

echo "============compile chipsinfotool ce3226============">>../$path

make -e DEBUG=0 -f makefile_chipsinfo_ce3226 clean
make -e DEBUG=0 -f makefile_chipsinfo_ce3226 2>>../$path

cp -L -r -f chipsinfotool ../../../10-common/version/release/ce3226/public/

cd ..
