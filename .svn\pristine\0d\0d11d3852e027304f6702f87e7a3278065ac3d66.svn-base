

TOP := ..

COMM_DIR := ../../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := dmsrv


## Define debugging symbols
DEBUG = 1
LINUX_COMPILER = _AX603A_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/dmsrv \
		$(SRC_DIR)/dmsrvmgr \
		$(SRC_DIR)/dmsrv_sdcard\
		$(SRC_DIR)/dmsrv_raid\
		$(SRC_DIR)/dmsrv_nvriscsi\
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../../../10-common/include/service \
		$(CURDIR)/../../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../../10-common/include/system\
		$(CURDIR)/../../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../../10-common/include/cbb/openssl\
		$(CURDIR)/../../../../10-common/include/cbb/kdssl-ext\
		$(CURDIR)/../../../../40-service/nvrcap/include\
		$(CURDIR)/../../../airp/icnlude\
		../../common \
		../../../common \

CFLAGS += -D_AX603A_


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../../10-common/lib/release/ax603a
include $(COMM_DIR)/common.mk


