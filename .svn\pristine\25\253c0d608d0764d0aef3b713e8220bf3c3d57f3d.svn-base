path="../../10-common/version/compileinfo/nvrsrv_rk3568.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_update_linux for rk3568         =
echo ==============================================

echo "============compile nvrupdate rk3568============">>../$path

make -e DEBUG=0 -f makefile_nvrupdate_rk3568 clean
make -e DEBUG=0 -f makefile_nvrupdate_rk3568 2>>../$path

cp -L -r -f nvrupdate ../../../10-common/version/release/rk3568/public/


echo "============compile nvrupproc rk3568============">>../$path

make -e DEBUG=0 -f makefile_nvrupproc_rk3568 clean
make -e DEBUG=0 -f makefile_nvrupproc_rk3568 2>>../$path

cp -L -r -f nvrupproc ../../../10-common/version/release/rk3568/public/


echo "============compile nvrupdate_cgi rk3568============">>../$path

make -e DEBUG=0 -f makefile_nvrupdate_cgi_rk3568 clean
make -e DEBUG=0 -f makefile_nvrupdate_cgi_rk3568 2>>../$path

cp -L -r -f nvrupdate_cgi ../../../10-common/version/release/rk3568/public/

cd ..
