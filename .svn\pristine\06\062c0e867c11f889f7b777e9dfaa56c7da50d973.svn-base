

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := edgeossdk
ARC_TARGET            := edgeossdk

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _AX620A_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables -D__arm__
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/edgeossdk
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrrec/include \
		$(CURDIR)/../../lcamclt/include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/system \
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/ipcmediactrl \
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/hal/drvlib_64 \
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch \
		$(CURDIR)/../../../10-common/include/cbb/wmf \
		$(CURDIR)/../../../10-common/include/cbb/ispctrl \
		$(CURDIR)/../../../10-common/include/cbb/appclt \
		$(CURDIR)/../../../10-common/include/cbb/charconversion \
		$(CURDIR)/../../../10-common/include/app \
		$(CURDIR)/../../../10-common/include/cbb/freetype \
		$(CURDIR)/../../../10-common/include/cbb/freetype/freetype \
		$(CURDIR)/../../../10-common/include/cbb/debuglog
CFLAGS += -D_AX620A_


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/ax620a
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


