#ifndef __MAPPNOTICE_h__
#define __MAPPNOTICE_h__


#ifdef __cplusplus
extern "C" {
#endif

#include "mapp.h"

/**
 *@brief  init
 *@param
 *@return
 *@ref
 *@see
 *@note
 */

int MappNoticeInit();
/**
 *@brief  push notifaction
 *@param  [in]int nEventType  event type
 *@param  [in]char *pBuf  dev name NVR or D1/2...
 *@return
 *@ref
 *@see
 *@note
 */

int MappNoticePush(int nEventType,char *pBuf);

/**
 *@brief  query alarm event
 *@param  
 *@param  
 *@return
 *@ref
 *@see
 *@note
 */
int MappNtyQueryAlarmEvent(int SID, int avIndex, char *buf);
/**
 *@brief  get event list enable cfg
 *@param  
 *@param  
 *@return
 *@ref
 *@see
 *@note
 */
int MappNtyGetEventListEnableCfg(int SID, int avIndex, char *buf);
/**
 *@brief  set event list enable cfg
 *@param  
 *@param  
 *@return
 *@ref
 *@see
 *@note
 */
int MappNtySetEventListEnableCfg(int SID, int avIndex, char *buf);



int MappNoticeTest(int nType);








#ifdef __cplusplus
}
#endif ///<__cplusplus
#endif

