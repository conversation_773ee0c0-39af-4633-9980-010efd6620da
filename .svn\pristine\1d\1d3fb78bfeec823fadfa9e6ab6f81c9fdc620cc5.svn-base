

COMM_DIR := ./

SRC_DIR := ../source

## Name and type of the target for this Makefile

SO_TARGET      := pdnsapp

## Define debugging symbols
DEBUG = 1
LINUX_COMPILER= _HIS3559A_
PWLIB_SUPPORT = 0

CFLAGS += -D_LINUX -Wall -D_ARM_HIS3559A_ -DUSE_GETADDRINFO

OBJS := $(SRC_DIR)/pdnsapp \
        $(SRC_DIR)/pdnsapp_context \
		$(SRC_DIR)/pdnsapp_ws \
        $(SRC_DIR)/pdnsapp_tool \
		$(SRC_DIR)/pdnsapp_ctrltask \
		$(SRC_DIR)/pdnsapp_shareaction \
		$(SRC_DIR)/pdnsapp_tcptask \
		$(SRC_DIR)/pdnsapp_crossplat_nvrv7 \
		../ProtoFile/pdnsapp.pb-c
        
	
## Libraries to include in shared object file
        
## Add driver-specific include directory to the search path
##ARC_LIBS += 
INC_PATH += ../../../10-common/include/cbb               \
			../../../10-common/include/cbb/mxml               \
            ../../../10-common/include/hal/mediactrl     \
            ../../../10-common/include/system            \
            ../../../10-common/include/service           \
            ../../../10-common/include/app              \
			../../../10-common/include/cbb/libwebsockets \
			../../../10-common/include/cbb/debuglog \
			../../../10-common/include/cbb/protobuf \
			../../../10-common/include/cbb/openssl			\
			../../../10-common/include/cbb/libwebsockets/config_file/linux \
			../../../10-common/include/app/lwshelper \
			../../../10-common/include/cbb/osp \
            ../include \
			../../../10-common/include/cbb/pdns \
			../ProtoFile

INSTALL_LIB_PATH = ../../../10-common/lib/release/his3559a/applib

LIB_PATH := ../../../10-common/lib/release/his3559a
LIBS := lwshelper websockets appbase pdns

include $(COMM_DIR)/makelib.mk

