#ifndef _DISK_H_
#define _DISK_H_

#define DISK_STATUS_OK		0
#define DISK_STATUS_FAILED	1
#define DISK_MAY_FAIL		2

struct smart_attr {

	struct smart_info si;

	struct list_head list;
};

struct cps {
	u64 lba;
	struct list_head list;
};

struct disk_status {
	/* health */
	u32 health;
	
	/* fail tendency */
	u32 err_cnt;
	u32 fail_tendency;
	u32 smart_enabled;
	u32 bad_block_nr;
	
	u32 raid_status;	
	u32 raid_bb_cnt;
	/* ATA smart attr list */
	struct list_head smart_attr_list;
	struct list_head cps_list;
};

struct disk_log {
	u8 log_addr;
	u8 features;
	u8 page;
	u8 reserved;
	void *data;
	u32 nsectors;
};

struct disk_func {
	/* get basic info, vendor, model, fw_ver, serial_no, size*/
	int (*get_base_info)(char *, struct disk_info *);
	/* get disk status */
	int (*get_status)(char *, struct disk_status *);

	int (*get_selfcheck_status)(char *, struct selfcheck_info *);

	int (*enable_smart)(char *);

	int (*disable_smart)(char *);

	int (*start_disk_selfcheck)(char *);

	int (*stop_disk_selfcheck)(char *);
	
	int (*read_log)(int fd, struct disk_log *dl);

	int (*get_disk_cpsnum)(struct disk_status *ds, u32 *num);
};

struct disk {
	/* disk basic info */
	struct disk_info disk_usr_info;
	
	/* disk status */
	struct disk_status status;

	/* enclosure info */
	u32 enc_id;

	/* type: scsi/ata */
	u32 type;

	/* disk slot id */
	u32 slot_id;

	struct list_head list;

	/* funcs */
	struct disk_func *dfunc;

};

extern void disk_init();

extern struct disk *disk_alloc();

extern void disk_free(struct disk *disk);

extern void add_disk(struct disk *disk);

extern void del_disk(struct disk *disk);

extern int del_disk_by_name(char *name, struct disk **d);

extern int new_disk(struct disk *disk);

extern void release_all_disk();

extern int update_disk_status_internal(u32 slot_id, u32 enc_id, struct disk_status *ds);

extern int general_get_slot_health(u32 slot_id, u32 enc_id, u32 *health);

extern int general_get_disk_info(u32 slot_id, u32 enc_id, struct disk_info *di);

extern int general_get_disk_cps_list(u32 slot_id, u32 enc_id, u64 *buff, u32 buff_size, u32 *num);

extern int general_get_disk_smart_info(u32 slot_id, u32 enc_id, struct smart_info *si);

extern int general_get_disk_smart_attr_nr(u32 slot_id, u32 enc_id, u8 *smart_attr_nr);

extern int general_get_disk_bad_block_nr(u32 slot_id, u32 enc_id, u32 *bad_block_nr);

extern int general_get_disk_smart_enabled(u32 slot_id, u32 enc_id, u32 *smart_enabled);

extern int general_set_disk_raid_status(u32 slot_id, u32 enc_id, u32 raid_status);

extern int general_get_disk_raid_status(u32 slot_id, u32 enc_id, u32 *raid_status);

extern int general_set_disk_raid_bb_cnt(u32 slot_id, u32 enc_id, u32 raid_bb_cnt);

extern int general_get_disk_raid_bb_cnt(u32 slot_id, u32 enc_id, u32 *raid_bb_cnt);

extern int get_disk_type_internal(struct disk *disk);

extern int get_vendor_spec_cap(struct disk_info *di);

extern int get_cps_list_internal(const struct disk_info *di, struct disk_func *df, struct disk_status *ds);

extern int disk_func_init(struct disk *disk); 

extern int get_disk_info_and_func(u32 slot_id, u32 enc_id, struct disk_info *di, struct disk_func *df);

extern int general_set_disk_health_detect_status(u32 slot_id, u32 enc_id, u32 enable);

#endif
