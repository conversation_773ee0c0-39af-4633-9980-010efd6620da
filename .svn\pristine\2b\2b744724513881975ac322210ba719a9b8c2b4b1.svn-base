#!/bin/bash
#set -x
path="../../../10-common/version/compileinfo/nvrlib_ssr621q.txt"
date>>$path

module_name=$(basename $PWD)
proj_dir=prj_linux

echo ==============================================
echo =      ${module_name}_linux for ssr621q =
echo ==============================================

echo "============compile lib$module_name ssr621q============">>$path

make -C $proj_dir -e DEBUG=1 -f makefile_ssr621q clean all 2>&1 1>/dev/null |tee -a $path


