###
### Copyright (c) 2004 Keda Telecom, Inc.
###

#########################################################################
###
###  DESCRIPTION:
###    Common definitions for all Makefiles in OSP linux project.
###
#########################################################################

TOP := ..

COMM_DIR := ./

SRC_DIR := $(TOP)/src
WS_SRC_DIR := $(TOP)/websocket/source


## Name and type of the target for this Makefile

SO_TARGET      := cgiapp

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER= _ARM_HIS3536_
PWLIB_SUPPORT = 0
USE_OSP = 0


CFLAGS += -D_LINUX -Wall -D_HIS3536_ -DLESS_SECURITY -fno-omit-frame-pointer -DHAVE_WEBSOCKET

OBJS := $(SRC_DIR)/cgiapp                       \
        $(SRC_DIR)/cgiapp_webservice            \
        $(SRC_DIR)/cgiapp_config                \
        $(SRC_DIR)/cgiapp_tool                  \
        $(SRC_DIR)/cgiapp_handler_framework     \
	    $(SRC_DIR)/cgiapp_auth	                \
	    $(SRC_DIR)/cgiapp_context	            \
		$(SRC_DIR)/cgiapp_system_handler        \
		$(SRC_DIR)/cgiapp_network_handler       \
		$(SRC_DIR)/cgiapp_chnmange_handler		\
		$(SRC_DIR)/cgiapp_disk_handler	    	\
		$(SRC_DIR)/cgiapp_xml_helper			\
		$(SRC_DIR)/cgiapp_enum_str_conv	        \
		$(SRC_DIR)/cgiapp_security_handler	    \
		$(SRC_DIR)/cgiapp_log_handler	        \
		$(SRC_DIR)/cgiapp_getcap_handler	    \
		$(SRC_DIR)/cgiapp_event_handler	        \
		$(SRC_DIR)/cgiapp_device_handler	    \
		$(SRC_DIR)/cgiapp_record_handler        \
		$(SRC_DIR)/cgiapp_echo_handler			\
		$(SRC_DIR)/cgiapp_ais_handler			\
		$(SRC_DIR)/cgiapp_authgetdata_handler 	\
		$(SRC_DIR)/cgiapp_devext_handler 		\
		$(WS_SRC_DIR)/cgiapp_websocket			\
		$(WS_SRC_DIR)/cgiapp_websocket_context	\
		$(WS_SRC_DIR)/cgiapp_ws_rtspover		\
		$(WS_SRC_DIR)/cgiapp_ws_msg_handler

## Libraries to include in shared object file
        
#LIBS :=  

## Add driver-specific include directory to the search path
##ARC_LIBS += 
INC_PATH += ../../../10-common/include/cbb/appclt         \
            ../../../10-common/include/cbb/protobuf       \
            ../../../10-common/include/cbb/osp            \
            ../../../10-common/include/cbb/mediaswitch		\
			../../../10-common/include/cbb/mxml           \
			../../../10-common/include/cbb/cjson          \
		    ../../../10-common/include/cbb/debuglog       \
			../../../10-common/include/cbb/goahead/linux  \
			../../../10-common/include/cbb/openssl  	  \
			../../../10-common/include/cbb/libwebsockets  \
			../../../10-common/include/cbb/libwebsockets/config_file/linux  \
			../../../10-common/include/cbb			      \
            ../../../10-common/include/hal                \
            ../../../10-common/include/hal/drvlib         \
            ../../../10-common/include/hal/netcbb         \
            ../../../10-common/include/hal/ispctrl        \
            ../../../10-common/include/hal/mediactrl      \
            ../../../10-common/include/system             \
            ../../../10-common/include/service            \
            ../../../10-common/include/app                \
			../../../10-common/include/app/lwshelper      \
            ../include									\
			../websocket/include
            

LIB_PATH := ../../../10-common/lib/release/his3536

LIBS := lwshelper \
        websockets \
		goaheadhelper	\
	go         \
        mxml       \
        cjson	   \
        appbase    \
		debuglog   \
        nvrusrmgr  \
		nvrmpu     \
		nvrnetwork \
		nvrftp

INSTALL_LIB_PATH = ../../../10-common/lib/release/his3536/applib

include $(COMM_DIR)/makelib.mk

