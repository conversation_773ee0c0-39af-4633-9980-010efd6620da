

TOP := ..

COMM_DIR := ../../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := dmbasic


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _AX603A_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/dmbasic \

	
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += \
		$(CURDIR)/../../common \
		$(CURDIR)/../../dmsrv_in/include\
		$(CURDIR)/../../../common\
		$(CURDIR)/../include \
		$(CURDIR)/../../../../10-common/include/service \
		$(CURDIR)/../../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../../10-common/include/system\
		$(CURDIR)/../../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../../10-common/include/hal/udm \
		$(CURDIR)/../../../../10-common/include/cbb/osp \

CFLAGS += -D_AX603A_


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../../10-common/lib/release/ax603a/dmsrv

LDFLAGS += -Wl,--whole-archive -ludm -lblkid -luuid -L../../../../10-common/lib/release/ax603a/ -Wl,--no-whole-archive

include $(COMM_DIR)/common.mk


