
TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrupproc


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3519AV100_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
#CFLAGS += -fno-stack-protector
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrupproc\
## Libraries to include in shared object file

LIB_PATH += $(TOP)/../../10-common/lib/release/his3519av100
#2019-08-24版本libdrv.a，不链接pthread会编不过，24号之前的没问题，目前不知道原因
SLIBS += drv 
#SLIBS += pthread c 
LIBS += pthread
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/system\
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp
CFLAGS += -D_HIS3519AV100_


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif

#no upx program maybe main Segmentation fault
DO_UPX = 0


INSTALL_APP_PATH := ../../../10-common/version/release/his3519av100/public

include $(COMM_DIR)/common.mk