

TOP := ..

COMM_DIR := ../../../common/

SRC_DIR := $(TOP)/src

## Name and type of the target for this Makefile
#ARC_TARGET      := vstorage
SO_TARGET       := vstorage
## Define debugging symbols
DEBUG = 0
LINUX_COMPILER=_RK3568_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables 
## Object files that compose the target(s)

OBJS :=	$(SRC_DIR)/stor_lowware/task	\
	$(SRC_DIR)/stor_lowware/misc	\
	$(SRC_DIR)/stor_lowware/enclosure	\
	$(SRC_DIR)/stor_lowware/disk	\
	$(SRC_DIR)/stor_lowware/storage_sys	\
	$(SRC_DIR)/stor_lowware/disk_classify	\
	$(SRC_DIR)/stor_lowware/ata_disk	\
	$(SRC_DIR)/stor_lowware/scsi_disk	\
	$(SRC_DIR)/stor_lowware/sat	\
	$(SRC_DIR)/stor_lowware/dev_if	\
	$(SRC_DIR)/stor_lowware/base		\
	$(SRC_DIR)/storage_devs/sys_init	\
	$(SRC_DIR)/storage_devs/vs200c_sys	\
	$(SRC_DIR)/storage_devs/vs200d_l_sys	\
	$(SRC_DIR)/storage_devs/vs200d_sys  \
	$(SRC_DIR)/storage_devs/vs200g_l_sys \
	$(SRC_DIR)/storage_devs/vs200g_sys \
	$(SRC_DIR)/storage_devs/vs400a_1037_sys \
	$(SRC_DIR)/storage_devs/vs400a_sys \
	$(SRC_DIR)/storage_devs/vs400_sys 	\
	$(SRC_DIR)/stor_midware/raid	\
	$(SRC_DIR)/stor_midware/raid0	\
	$(SRC_DIR)/stor_midware/raid10	\
	$(SRC_DIR)/stor_midware/raid1456	\
	$(SRC_DIR)/stor_midware/lvm	\
	$(SRC_DIR)/stor_midware/netsvc	\
	$(SRC_DIR)/stor_midware/iscsi_tgtadm \
	$(SRC_DIR)/stor_midware/local_netsvc \
	$(SRC_DIR)/stor_midware/nas_netsvc \
	$(SRC_DIR)/log \
	$(SRC_DIR)/task \
	$(SRC_DIR)/storage_pool \
    $(SRC_DIR)/stor_interface/dm_interface


## Libraries to include in shared object file

ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(TOP)/include ../include

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif

INSTALL_LIB_PATH = ../../../../10-common/lib/release/rk3568

include $(COMM_DIR)/common.mk
