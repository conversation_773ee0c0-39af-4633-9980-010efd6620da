#include "vslls.h"
#include "dm.h"
#include "storage_sys.h"
#include "sys_init.h"
#include <string.h>

static struct scsi_lun scsi_lun_table[1][16] = {
	{
		{2,0,0,0},
		{6,0,3,0},
		{7,0,3,0},
		{8,0,3,0},
		{3,0,0,0},
		{6,0,2,0},
		{7,0,2,0},
		{8,0,2,0},
		{4,0,0,0},
		{6,0,1,0},
		{7,0,1,0},
		{8,0,1,0},
		{5,0,0,0},
		{6,0,0,0},
		{7,0,0,0},
		{8,0,0,0}
	}
};

static int vs200c_get_scsi_lun_by_location(u32 slot_id, u32 enc_id, struct scsi_lun *slun)
{
	if(!slun)
		goto err;
	if(enc_id >= 1)
		goto err;
	if(slot_id >= 16)
		goto err;

	memcpy(slun, &scsi_lun_table[enc_id][slot_id], sizeof(struct scsi_lun));	
	return 0;
err:
	return -1;
}

static int vs200c_get_location_by_scsi_lun(struct scsi_lun *slun, u32 *slot_id, u32 *enc_id)
{
	u32 i, j;
	int cmp;

	if(!slun || !slot_id || !enc_id)
		goto err;

	for(i = 0; i < 1; i++){
		for(j = 0; j < 16; j++){
			cmp = memcmp(slun, &scsi_lun_table[i][j], sizeof(struct scsi_lun));
			if(!cmp)
				break;
		}
		if(!cmp)
			break;
	}
		
	if(cmp)
		goto err;

	*slot_id = j;
	*enc_id = i;	

	return 0;
err:
	return -1;
}

/* misc: cpu,memory,led,nic... */
static int vs200c_get_coretemp_path(u32 id, char *sys_path)
{
	if(!sys_path)
		return -ERR_SYSTEM_ERROR;
	strcpy(sys_path, "/sys/class/hwmon/hwmon1/device/temp");	
	sys_path[strlen(sys_path)] = '2' + id;
	strcat(sys_path, "_input");
	return SUCCESS;
}
static int vs200c_get_power_info(u32 enc_id, u32 id, struct power_info *power)
{
	if(!power)
		return -ERR_PARAMETER;	
	return SUCCESS;
}

static int vs200c_get_fan_info(u32 enc_id, u32 id, struct fan_info *fan)
{
	if(!fan)
		return -ERR_PARAMETER;
	return SUCCESS;
}
static int vs200c_set_alarm_blink_conf(u32 enc_id, u8 blink)
{
	return SUCCESS;	
}		
static int vs200c_get_alarm_blink_cap(u32 enc_id, u8 *blink)
{
	if(!blink)
		return -ERR_PARAMETER;
		
	return SUCCESS;
}		
static int vs200c_get_mute_status(u32 enc_id, u32 *state)	
{
	if(!state)
		return -ERR_PARAMETER;
	return SUCCESS;
}		
static int vs200c_set_alarm_led_status(u32 enc_id, u32 state)
{
	return SUCCESS;
}		
static int vs200c_set_power_led_status(u32 enc_id, u32 state)
{
	return SUCCESS;
}		
static int vs200c_set_buzzer_status(u32 enc_id, u32 state)
{
	return SUCCESS;
}		

static int vs200c_get_power_nr(u32 enc_id, u32 *power_nr)
{
	*power_nr = 1;
	return SUCCESS;
}

static int vs200c_get_power_smbus_addr(u32 enc_id, u32 id, u8 *smbus_addr)
{
	return SUCCESS;
}

static int vs200c_get_fan_nr(u32 enc_id, u32 *fan_nr)
{
	*fan_nr = 1;
	return SUCCESS;
}

static int vs200c_get_fan_template(u32 enc_id, u32 id, struct fan_func_template *fft)
{
	return -ERR_SYSTEM_ERROR;
}

static struct storage_func_template vs200c_func = {
	/* device personal functions */
	.get_scsi_lun_by_location	= vs200c_get_scsi_lun_by_location,
	.get_location_by_scsi_lun	= vs200c_get_location_by_scsi_lun,

	/* misc: cpu,memory,led,nic... */
	.get_coretemp_path		= vs200c_get_coretemp_path,
	.get_power_smbus_addr		= vs200c_get_power_smbus_addr,
	.get_power_nr			= vs200c_get_power_nr,
	.get_fan_nr			= vs200c_get_fan_nr,
	.get_fan_template		= vs200c_get_fan_template,
#if 0
	.get_power_info			= vs200c_get_power_info,
	.get_fan_info			= vs200c_get_fan_info,
#endif
	.set_alarm_blink_conf		= vs200c_set_alarm_blink_conf,	
	.get_alarm_blink_cap		= vs200c_get_alarm_blink_cap,	
	.get_mute_status		= vs200c_get_mute_status,	
	.set_alarm_led_status		= vs200c_set_alarm_led_status,
	.set_power_led_status		= vs200c_set_power_led_status,
	.set_buzzer_status		= vs200c_set_buzzer_status,
}; 

int vs200c_sys_init(struct storage_sys_template *sys)
{
	if(sys->type != SYS_TYPE_VS200C)
		return -ERR_SYSTEM_ERROR;

	sys->max_enclosure_nr = 1;
	sys->enclosure_slot_nr = 16;
	sys->nic_nr = 3;
	sys->core_nr = 2;
	sys->func = &vs200c_func;
	

	return SUCCESS;
}
