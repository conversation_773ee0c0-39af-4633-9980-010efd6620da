#!/bin/bash
#set -x
path="../../../10-common/version/compileinfo/nvrlib_ax603a.txt"
date>>$path

module_name=$(basename $PWD)
proj_dir=prj_linux

echo ==============================================
echo =      ${module_name}_linux for ax603a           =
echo ==============================================

echo "============compile lib$module_name ax603a============">>$path

make -C $proj_dir -e DEBUG=1 -f makefile_ax603a clean all 2>&1 1>/dev/null |tee -a $path


