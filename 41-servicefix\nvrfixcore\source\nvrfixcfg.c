/**
* @file 	nvrFixcfg.c
* @brief    nvr 隔离网关编解码器参数配置
* <AUTHOR>
* @date 	2021-2-1
* @version  1.0
* @copyright V1.0  Copyright(C) 2021 NVR All rights reserved.
*/
#include "nvrcfg.h"
#include "nvrcap_in.h"
#include "nvrfixcfg.pb-c.h"
#include "nvrfixalg.pb-c.h"
#include "nvrfixcore.h"
#include "nvrfixcfg.h"
#include "nvrsys_in.h"
#include "nvrsystem.h"
#include "nvrfixdev.h"

#define FIX_CFG                 "NvrFixCfg"
#define FIX_ALG_CFG             "NvrFixAlgCfg"

#define NVR_MAX_PATHSCAN_PRESET_NUM 32   ///>路径巡航每条路径支持的最大预置位个数

static NVRSTATUS NvrFixCfgPtzDefaultInit();
static NVRSTATUS NvrFixDefaultCfgInit();
static NVRSTATUS NvrFixAlgCfgInit();



static TNvrFixCfg      		    g_tNvrFixCfg;  					///<媒控配置
static TNvrFixProfeIntelCfg     g_tnvrFixProIntelCfg;           ///<枪机专业知能配置
static SEMHANDLE                g_hNvrFixCfgRWSem;              ///<媒体控制参数读写信号量
static SEMHANDLE                g_hNvrFixAlgCfgRWSem;              ///<媒体控制参数读写信号量

void NvrFixTPbFixSmokeFireCfgInit(TPbFixSmokeFireCfg *pt)
{
    if (pt)
    {
        TPbFixSmokeFireCfg t = TPB_FIX_SMOKE_FIRE_CFG__INIT;
        memcpy(pt, &t, sizeof(t));
    }
}

void NvrFixTPbFixAlgEventContactInit(TPbFixAlgEventContact  *pt)
{
    if (pt)
    {
        TPbFixAlgEventContact t = TPB_FIX_ALG_EVENT_CONTACT__INIT;
        memcpy(pt, &t, sizeof(t));
    }
}

void NvrFixTPbFixSmokeFireOtherParamInit(TPbFixSmokeFireOtherParam  *pt)
{
    if (pt)
    {
        TPbFixSmokeFireOtherParam t = TPB_FIX_SMOKE_FIRE_OTHER_PARAM__INIT;
        memcpy(pt, &t, sizeof(t));
    }
}

void NvrFixTPbFixSmokeFirePlanParamInit(TPbFixSmokeFirePlanParam  *pt)
{
    if (pt)
    {
        TPbFixSmokeFirePlanParam t = TPB_FIX_SMOKE_FIRE_PLAN_PARAM__INIT;
        memcpy(pt, &t, sizeof(t));
    }
}

void NvrFixTPbFixPloygonInit(TPbFixPloygon *pt)
{
    if (pt)
    {
        TPbFixPloygon t = TPB_FIX_PLOYGON__INIT;
        memcpy(pt, &t, sizeof(t));
    }
}

void NvrFixTPbFixPointInit(TPbFixPoint *pt)
{
    if (pt)
    {
        TPbFixPoint t = TPB_FIX_POINT__INIT;
        memcpy(pt, &t, sizeof(t));
    }
}

NVRSTATUS NvrFixCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple)
{
    FIX_ASSERT(ptPbcSimple);
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 dwBufLen = 0;
	u32 i = 0, j = 0, k = 0,m = 0;
	TPbFixCfg tPbFixCfg = TPB_FIX_CFG__INIT;
	TPbFixIspCfg tPbFixIspCfg = TPB_FIX_ISP_CFG__INIT;
	TPbFixPtzCfg tPbFixPtzCfg = TPB_FIX_PTZ_CFG__INIT;
	TPbFixMcCfg  tPbFixMcCfg  = TPB_FIX_MC_CFG__INIT;	
	TPbFixIspCfg atPbFixIspCfg[NVR_MAX_LCAM_CHN_NUM];
	TPbFixPtzCfg atPbFixPtzCfg[NVR_MAX_PTZ_NUM];
	TPbFixMcCfg atPbFixMcCfg[NVR_MAX_LCAM_CHN_NUM];
	TPbFixIspCfg *aptPbFixIspCfg[NVR_MAX_LCAM_CHN_NUM] = {NULL};
	TPbFixPtzCfg *aptPbFixPtzCfg[NVR_MAX_PTZ_NUM] = {NULL};
	TPbFixMcCfg *aptPbFixMcCfg[NVR_MAX_LCAM_CHN_NUM] = {NULL};
		
	///<isp配置
	tPbFixCfg.n_fix_isp = NVR_MAX_LCAM_CHN_NUM;
	tPbFixCfg.fix_isp = aptPbFixIspCfg;
	for(i = 0; i < tPbFixCfg.n_fix_isp; i++)
	{
		atPbFixIspCfg[i] = tPbFixIspCfg;		///<初始化
		
		aptPbFixIspCfg[i] = &atPbFixIspCfg[i];

		///<zf参数
		aptPbFixIspCfg[i]->has_zf_day_zoom = TRUE;
		aptPbFixIspCfg[i]->zf_day_zoom = g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_DAY].dwZoomPosition;
		aptPbFixIspCfg[i]->has_zf_day_focus = TRUE;
		aptPbFixIspCfg[i]->zf_day_focus = g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_DAY].dwFocus;
		aptPbFixIspCfg[i]->has_zf_night_zoom = TRUE;
		aptPbFixIspCfg[i]->zf_night_zoom = g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_NIGHT].dwZoomPosition;
		aptPbFixIspCfg[i]->has_zf_night_focus = TRUE;
		aptPbFixIspCfg[i]->zf_night_focus = g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_NIGHT].dwFocus;
		
		//手动聚焦参数
		aptPbFixIspCfg[i]->has_manual_focus = TRUE;
		aptPbFixIspCfg[i]->manual_focus = g_tNvrFixCfg.tIspCfg[i].dwManualFocus;

		PRINTDBG("[%u]dwManualFocus :%u,zf Day z%u-f%u,Night z%u-f%u\n",i,\
								g_tNvrFixCfg.tIspCfg[i].dwManualFocus,\
								g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_DAY].dwZoomPosition,\
								g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_DAY].dwFocus,\
								g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_NIGHT].dwZoomPosition,\
								g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_NIGHT].dwFocus);
	}

	//<ptz配置
	tPbFixCfg.n_fix_ptz = 1;
	tPbFixCfg.fix_ptz = aptPbFixPtzCfg;

	atPbFixPtzCfg[0] = tPbFixPtzCfg;
	aptPbFixPtzCfg[0] = &atPbFixPtzCfg[0];
	 ///子结构体	
	 ///<1,云台控制参数
	 TPbNvrPtzCtrlPrm tPbPtzCtrlPrm = TPB_NVR_PTZ_CTRL_PRM__INIT;
	 aptPbFixPtzCfg[0]->ptz_ctrl_param = &tPbPtzCtrlPrm;
	 tPbPtzCtrlPrm.has_protocal_type = TRUE;
	 tPbPtzCtrlPrm.protocal_type = g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm.byProtocalType;
	 tPbPtzCtrlPrm.has_address = TRUE;
	 tPbPtzCtrlPrm.address = g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm.byAddress;
	 tPbPtzCtrlPrm.has_extra_address = TRUE;
	 tPbPtzCtrlPrm.extra_address = g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm.byExtraAddress;

	 ///<2,云台状态参数
	 TPbNvrPtzBasicState tPbNvrPtzBasicState = TPB_NVR_PTZ_BASIC_STATE__INIT;
	 aptPbFixPtzCfg[0]->ptz_state = &tPbNvrPtzBasicState;
	 tPbNvrPtzBasicState.has_depth_rate_spd = TRUE;
	 tPbNvrPtzBasicState.depth_rate_spd = g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDepthrateSpd;
	 tPbNvrPtzBasicState.has_scan_speed_value = TRUE;
	 tPbNvrPtzBasicState.scan_speed_value = g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwScanSpeedValue;
	 tPbNvrPtzBasicState.has_preset_spd_value = TRUE;
	 tPbNvrPtzBasicState.preset_spd_value=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwPreSetSpdValue;
	 tPbNvrPtzBasicState.has_ezoom_speed_value = TRUE;
	 tPbNvrPtzBasicState.ezoom_speed_value=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eZoomSpeedValue;
	 tPbNvrPtzBasicState.has_ept_osd_mode = TRUE;
	 tPbNvrPtzBasicState.ept_osd_mode=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.ePTOsdMode ;
	 tPbNvrPtzBasicState.ptz_soft_ver = (s8*)g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.achPtzSoftVer;
	 tPbNvrPtzBasicState.has_vid_enc_freeze = TRUE;
	 tPbNvrPtzBasicState.vid_enc_freeze=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwVidEncFreeze ;
	 tPbNvrPtzBasicState.has_priority_type = TRUE;
	 tPbNvrPtzBasicState.priority_type=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPriorityType ;
	 tPbNvrPtzBasicState.has_pri_delay_time = TRUE;
	 tPbNvrPtzBasicState.pri_delay_time=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPriDelayTime ;
	 tPbNvrPtzBasicState.has_infared_state = TRUE;
	 tPbNvrPtzBasicState.infared_state=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwInfaredState ;
	 tPbNvrPtzBasicState.has_einfared_mode = TRUE;
	 tPbNvrPtzBasicState.einfared_mode=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eInfaredMode ;
	 tPbNvrPtzBasicState.has_infared_sens = TRUE;
	 tPbNvrPtzBasicState.infared_sens =g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwInfaredSens ;
	 tPbNvrPtzBasicState.has_infared_value = TRUE;
	 tPbNvrPtzBasicState.infared_value=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwInfaredValue ;
	 tPbNvrPtzBasicState.has_near_infared_value = TRUE;
	 tPbNvrPtzBasicState.near_infared_value=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwNearInfaredValue ;
	 tPbNvrPtzBasicState.has_e_laser_switch= TRUE;
	 tPbNvrPtzBasicState.e_laser_switch=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eLaserSwitch ;
	 tPbNvrPtzBasicState.has_e_laser_mode = TRUE;
	 tPbNvrPtzBasicState.e_laser_mode=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eLaserMode ;
	 tPbNvrPtzBasicState.has_laser_dist = TRUE;
	 tPbNvrPtzBasicState.laser_dist=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserDist ;
	 tPbNvrPtzBasicState.has_laser_intensity = TRUE;
	 tPbNvrPtzBasicState.laser_intensity=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserIntensity ;
	 tPbNvrPtzBasicState.has_laser_centrad_mode = TRUE;
	 tPbNvrPtzBasicState.laser_centrad_mode=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserCentradMode ;
	 tPbNvrPtzBasicState.has_wiper_state = TRUE;
	 tPbNvrPtzBasicState.wiper_state=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwWiperState;
	 tPbNvrPtzBasicState.has_defrost_state = TRUE;
	 tPbNvrPtzBasicState.defrost_state=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDefrostState;
	 tPbNvrPtzBasicState.has_pos_limit_display = TRUE;
	 tPbNvrPtzBasicState.pos_limit_display=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwPosLimitDisplay ;
	 tPbNvrPtzBasicState.has_evertica_range = TRUE;
	 tPbNvrPtzBasicState.evertica_range=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eVerticaRange ;
	 tPbNvrPtzBasicState.has_laser_centrad_speed = TRUE;
	 tPbNvrPtzBasicState.laser_centrad_speed=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserCentradSpeed ;
	 tPbNvrPtzBasicState.has_demist_state = TRUE;
	 tPbNvrPtzBasicState.demist_state=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDeMistState ;
	 tPbNvrPtzBasicState.has_demist_time = TRUE;
	 tPbNvrPtzBasicState.demist_time=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDeMistTime ;
	 tPbNvrPtzBasicState.has_sl_state = TRUE;
	 tPbNvrPtzBasicState.sl_state=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwSLState ;
	 tPbNvrPtzBasicState.has_esl_mode = TRUE;
	 tPbNvrPtzBasicState.esl_mode=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eSLMode ;
	 tPbNvrPtzBasicState.has_sl_value = TRUE;
	 tPbNvrPtzBasicState.sl_value=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwSLValue ;
	 tPbNvrPtzBasicState.has_power_on_mode = TRUE;
	 tPbNvrPtzBasicState.power_on_mode=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPowerOnMode ;
	 tPbNvrPtzBasicState.has_led_main_switch = TRUE;
	 tPbNvrPtzBasicState.led_main_switch=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLedMainSwitch;
	 tPbNvrPtzBasicState.has_efan_demister_mode = TRUE;
	 tPbNvrPtzBasicState.efan_demister_mode=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eFanDemisterMode ;
	 tPbNvrPtzBasicState.has_power_off_mode = TRUE;
	 tPbNvrPtzBasicState.power_off_mode=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPowerOffMode ;
	 tPbNvrPtzBasicState.has_car_mode = TRUE;
	 tPbNvrPtzBasicState.car_mode=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byCarMode;
	 tPbNvrPtzBasicState.has_ext_wifi_switch = TRUE;
	 tPbNvrPtzBasicState.ext_wifi_switch = g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byExtWifiSwitch;
	 tPbNvrPtzBasicState.has_ptz_id = TRUE;
	 tPbNvrPtzBasicState.ptz_id=g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPtzId ;
	 tPbNvrPtzBasicState.has_manu_limit_pos = TRUE;
	 tPbNvrPtzBasicState.manu_limit_pos = g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwManuLimitPos;
	 tPbNvrPtzBasicState.has_scan_limit_pos = TRUE;
	 tPbNvrPtzBasicState.scan_limit_pos = g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwScanLimitPos;


	 ///<3,云台状态参数
	 TPbNvrPowerOffRsmCfg tPowerOffRsmCfg = TPB_NVR_POWER_OFF_RSM_CFG__INIT;
	 aptPbFixPtzCfg[0]->poweroff_rsm_cfg = &tPowerOffRsmCfg;
	 tPowerOffRsmCfg.has_enable = TRUE;
	 tPowerOffRsmCfg.enable = g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.bEnable;
	 tPowerOffRsmCfg.has_resume_mode = TRUE;
	 tPowerOffRsmCfg.resume_mode = g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.eResumeMode;
	 tPowerOffRsmCfg.n_param = NVR_DEV_POWEROFF_MODE_NUM;
	
	 tPowerOffRsmCfg.param = (FixUint32 *)g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.adwParam;
	 
	 ///<4,预置位信息
	 TPbNvrPresetInfo tPresetInfo = TPB_NVR_PRESET_INFO__INIT;
	 TPbNvrPresetInfo atPresetInfo[NVR_PUI_PRESET_MAX_NUM];
	 TPbNvrPresetInfo *aptPresetInfo[NVR_PUI_PRESET_MAX_NUM] = {NULL};

	 aptPbFixPtzCfg[0]->preset_info = aptPresetInfo;
	 aptPbFixPtzCfg[0]->n_preset_info = NVR_PUI_PRESET_MAX_NUM;
	 for(i = 0;i<NVR_PUI_PRESET_MAX_NUM;i++)
	 {
		atPresetInfo[i] = tPresetInfo;
		aptPresetInfo[i] = &atPresetInfo[i];

		aptPresetInfo[i]->has_is_set = TRUE;
		aptPresetInfo[i]->is_set = g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].bIsSet;
		aptPresetInfo[i]->has_foucs_pos = TRUE;
		aptPresetInfo[i]->foucs_pos= g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].nFoucsPos;
		aptPresetInfo[i]->has_zoom_pos = TRUE;
		aptPresetInfo[i]->zoom_pos= g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].nZoomPos;
		aptPresetInfo[i]->has_speed = TRUE;
		aptPresetInfo[i]->speed= g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].dwSpeed;
		aptPresetInfo[i]->alias = (s8*)g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].abyAlias;
		aptPresetInfo[i]->has_alias_len = TRUE;
		aptPresetInfo[i]->alias_len= g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].dwAliasLen;
		aptPresetInfo[i]->has_h_pos = TRUE;
		aptPresetInfo[i]->h_pos= g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].wHPos;
		aptPresetInfo[i]->has_w_vpos = TRUE;
		aptPresetInfo[i]->w_vpos= g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].wVPos;
		aptPresetInfo[i]->has_special = TRUE;
		aptPresetInfo[i]->special= g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].bSpecial;
	 }
	 ///<5,路径巡航信息
	 TPbNvrPathCrsInfo tPathCrsInfo = TPB_NVR_PATH_CRS_INFO__INIT;
	 TPbNvrPathCrsInfo atPathCrsInfo[NVR_MAX_PATHCRUISE_NUM];
	 TPbNvrPathCrsInfo *aptPathCrsInfo[NVR_MAX_PATHCRUISE_NUM] = {NULL};

	 TPbNvrPathCrsPresetInfo tCrsPresetInfo = TPB_NVR_PATH_CRS_PRESET_INFO__INIT;
	 TPbNvrPathCrsPresetInfo atCrsPresetInfo[NVR_MAX_PATHCRUISE_NUM][NVR_MAX_PATHSCAN_PRESET_NUM];
	 TPbNvrPathCrsPresetInfo *aptCrsPresetInfo[NVR_MAX_PATHCRUISE_NUM][NVR_MAX_PATHSCAN_PRESET_NUM] = {NULL};

	 aptPbFixPtzCfg[0]->n_path_crs_info = NVR_MAX_PATHCRUISE_NUM;
	 aptPbFixPtzCfg[0]->path_crs_info = aptPathCrsInfo;
	 for(i = 0;i<NVR_MAX_PATHCRUISE_NUM;i++)
	 {
		atPathCrsInfo[i] = tPathCrsInfo;
		aptPathCrsInfo[i] = &atPathCrsInfo[i];
		atPathCrsInfo[i].has_preset_cnt = TRUE;
		atPathCrsInfo[i].preset_cnt = g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].wPresetCnt;
		atPathCrsInfo[i].n_pathcrs_preset_info = atPathCrsInfo[i].preset_cnt;
		atPathCrsInfo[i].pathcrs_preset_info = aptCrsPresetInfo[i];
		for(j = 0;j<atPathCrsInfo[i].preset_cnt;j++)
		{
			atCrsPresetInfo[i][j] = tCrsPresetInfo;
			aptCrsPresetInfo[i][j] = &atCrsPresetInfo[i][j];
			atCrsPresetInfo[i][j].has_is_set = TRUE;
			atCrsPresetInfo[i][j].is_set = g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].bIsSet;
			atCrsPresetInfo[i][j].has_preset_id = TRUE;
			atCrsPresetInfo[i][j].preset_id= g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].wPrenum;
			atCrsPresetInfo[i][j].has_stay_time = TRUE;
			atCrsPresetInfo[i][j].stay_time= g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].wStaytime;
			atCrsPresetInfo[i][j].has_alias_len = TRUE;
			atCrsPresetInfo[i][j].alias_len = g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].dwAliasLen;
			atCrsPresetInfo[i][j].alias = (s8*)g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].abyAlias;
		}
	 }
	 ///<6,守望功能参数
	 TPbNvrWatchOnParam tWatchOnParam = TPB_NVR_WATCH_ON_PARAM__INIT;
	 aptPbFixPtzCfg[0]->watch_on_param = &tWatchOnParam;
	 tWatchOnParam.has_enable = TRUE;
	 tWatchOnParam.enable = g_tNvrFixCfg.tPztCfg[0].tWatchOn.bEnable;
	 tWatchOnParam.has_wait_time_ec = TRUE;
	 tWatchOnParam.wait_time_ec = g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwWaitTimeSec;
	 tWatchOnParam.has_task_type = TRUE;
	 tWatchOnParam.task_type = g_tNvrFixCfg.tPztCfg[0].tWatchOn.byTaskType;
	 tWatchOnParam.has_preset_id = TRUE;
	 tWatchOnParam.preset_id = g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwPresetID;
	 tWatchOnParam.has_sync_scan_id = TRUE;
	 tWatchOnParam.sync_scan_id = g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwSyncScanID;
	 tWatchOnParam.has_path_cruise_id = TRUE;
	 tWatchOnParam.path_cruise_id = g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwPathCruiseID;

	 ///<7,定时任务参数
	 TPbNvrTmingTaskParam tTmingTaskParam = TPB_NVR_TMING_TASK_PARAM__INIT;
	 TPbNvrTmingTaskHead  tTmingTaskHead = TPB_NVR_TMING_TASK_HEAD__INIT;

	 aptPbFixPtzCfg[0]->timing_task_param = &tTmingTaskParam;
	 tTmingTaskParam.timing_task_head = &tTmingTaskHead;

	 TPbNvrEvdayParam tNvrEvdayParam = TPB_NVR_EVDAY_PARAM__INIT;
	 TPbNvrEvdayParam atNvrEvdayParam[NVR_WEEK_DAY];
	 TPbNvrEvdayParam *aptNvrEvdayParam[NVR_WEEK_DAY] = {NULL};

	 TPbNvrTmingInfo tTmingInfo = TPB_NVR_TMING_INFO__INIT;
	 TPbNvrTmingInfo atTmingInfo[NVR_WEEK_DAY][NVR_DEV_MAX_TMINGTASK_PERIOD];
	 TPbNvrTmingInfo *aptTmingInfo[NVR_WEEK_DAY][NVR_DEV_MAX_TMINGTASK_PERIOD] = {NULL};

	 tTmingTaskHead.has_enable = TRUE;
	 tTmingTaskHead.enable = g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.bEnable;
	 tTmingTaskHead.has_resume_time = TRUE;
	 tTmingTaskHead.resume_time= g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.dwResumeTime;
	 tTmingTaskHead.has_data_addr = TRUE;
	 tTmingTaskHead.data_addr= g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.dwDataAddr;
	 tTmingTaskHead.has_time_period_num = TRUE;
	 tTmingTaskHead.time_period_num= g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.dwTimePeriodNum;
	 tTmingTaskHead.has_tming_info_size = TRUE;
	 tTmingTaskHead.tming_info_size= g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.dwTmingInfoSize;
	 tTmingTaskParam.evday_param = aptNvrEvdayParam;
	 tTmingTaskParam.n_evday_param = NVR_WEEK_DAY;

	 for(i = 0;i<NVR_WEEK_DAY;i++)
	 {
		 atNvrEvdayParam[i] = tNvrEvdayParam;
		 aptNvrEvdayParam[i] = &atNvrEvdayParam[i];
		 atNvrEvdayParam[i].n_tming_info = NVR_DEV_MAX_TMINGTASK_PERIOD;
		 atNvrEvdayParam[i].tming_info = aptTmingInfo[i];
		for(j = 0;j < NVR_DEV_MAX_TMINGTASK_PERIOD;j++)
		{
			atTmingInfo[i][j] = tTmingInfo;
			aptTmingInfo[i][j] = &atTmingInfo[i][j];
			atTmingInfo[i][j].has_is_enable = TRUE;
			atTmingInfo[i][j].is_enable= g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].bIsEnable;
			atTmingInfo[i][j].has_start_time = TRUE;
			atTmingInfo[i][j].start_time= g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].nStartTime;
			atTmingInfo[i][j].has_end_time = TRUE;
			atTmingInfo[i][j].end_time = g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].nEndTime;
			atTmingInfo[i][j].has_task_type = TRUE;
			atTmingInfo[i][j].task_type= g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].eTaskType;
			atTmingInfo[i][j].has_task_param = TRUE;
			atTmingInfo[i][j].task_param= g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].byTaskParam;
			
		}
	 }

	 ///<8,限位参数配置
	 TPbNvrPtzLimitState tLimitState = TPB_NVR_PTZ_LIMIT_STATE__INIT;
	 aptPbFixPtzCfg[0]->limit_state_param = &tLimitState;
	 tLimitState.has_manu_limit_pos = TRUE;
	 tLimitState.manu_limit_pos = g_tNvrFixCfg.tPztCfg[0].tLimitState.dwManuLimitPos;
	 tLimitState.has_scan_limit_pos = TRUE;
	 tLimitState.scan_limit_pos = g_tNvrFixCfg.tPztCfg[0].tLimitState.dwScanLimitPos;
	 ///<9,花样扫描参数配置
	 TPbNvrSyncScanInfo tSyncScanInfo = TPB_NVR_SYNC_SCAN_INFO__INIT;
	 aptPbFixPtzCfg[0]->sync_scan_info_param = &tSyncScanInfo;
	 tSyncScanInfo.has_e_state = TRUE;
	 tSyncScanInfo.e_state = g_tNvrFixCfg.tPztCfg[0].tSyncScanInfo.eState;
	 tSyncScanInfo.has_path_num = TRUE;
	 tSyncScanInfo.path_num = g_tNvrFixCfg.tPztCfg[0].tSyncScanInfo.byPathNum;
	 tSyncScanInfo.n_exist_flag = NVR_MAX_SYNCSCAN_PATH_NUM;
	 tSyncScanInfo.exist_flag = (FixUint32 *)g_tNvrFixCfg.tPztCfg[0].tSyncScanInfo.abyExist;
	 ///<10,方位图参数配置
	 TPbNvrCooParam tCooParam = TPB_NVR_COO_PARAM__INIT;
	 aptPbFixPtzCfg[0]->cool_param = &tCooParam;
	 tCooParam.has_x_pos = TRUE;
	 tCooParam.x_pos = g_tNvrFixCfg.tPztCfg[0].tCooParam.nX;
	  tCooParam.has_y_pos = TRUE;
	 tCooParam.y_pos = g_tNvrFixCfg.tPztCfg[0].tCooParam.nY;

	 ///<11,除雾配置
	 TPbNvrDeMistParam tDeMistParam = TPB_NVR_DE_MIST_PARAM__INIT;
	 aptPbFixPtzCfg[0]->demist_parm = &tDeMistParam;
	 tDeMistParam.has_demist_mode = TRUE;
	 tDeMistParam.demist_mode = g_tNvrFixCfg.tPztCfg[0].tDeMistCfg.eDeMistMode;
	 tDeMistParam.has_demist_time = TRUE;
	 tDeMistParam.demist_time = g_tNvrFixCfg.tPztCfg[0].tDeMistCfg.dwDeMistTime;

	///<mc配置
	tPbFixCfg.n_fix_mc = NVR_MAX_LCAM_CHN_NUM;
	tPbFixCfg.fix_mc = aptPbFixMcCfg;
	for(i = 0; i < tPbFixCfg.n_fix_isp; i++)
	{
		atPbFixMcCfg[i] = tPbFixMcCfg;		///<初始化
		
		aptPbFixMcCfg[i] = &atPbFixMcCfg[i];

		///<叠加参数,只用第一个
		aptPbFixMcCfg[0]->has_smallmap_pos_size = TRUE;
		aptPbFixMcCfg[0]->smallmap_pos_size = g_tNvrFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosSize;
		aptPbFixMcCfg[0]->has_smallmap_pos_type = TRUE;
		aptPbFixMcCfg[0]->smallmap_pos_type = g_tNvrFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosType;
	}

	///<intel配置
	TPbFixIntelCfg tPbFixIntelCfg  = TPB_FIX_INTEL_CFG__INIT;	
	TPbFixIntelCfg atPbFixIntelCfg[NVR_MAX_INTEL_NUM];
	TPbFixIntelCfg *aptPbFixIntelCfg[NVR_MAX_INTEL_NUM] = {NULL};
	TPbFixTherMeasTempCfg tPbTempCfg = TPB_FIX_THER_MEAS_TEMP_CFG__INIT;
	TPbFixTherMeasTempInfo tPbTempInfo = TPB_FIX_THER_MEAS_TEMP_INFO__INIT;
	TPbFixTherMeasTempInfo atPbTempInfo[NVR_MAX_INTEL_NUM][NVR_CAP_THER_MAX_SMART_PLAN_NUM];
	TPbFixTherMeasTempInfo *aptPbTempInfo[NVR_MAX_INTEL_NUM][NVR_CAP_THER_MAX_SMART_PLAN_NUM] = {NULL};
	TPbFixTherMeasTempArea tPbTempArea = TPB_FIX_THER_MEAS_TEMP_AREA__INIT;
	TPbFixTherMeasTempArea atPbTempArea[NVR_MAX_INTEL_NUM][NVR_CAP_THER_MAX_SMART_PLAN_NUM][NVR_CAP_THER_MAX_TYPE_AREA_NUM];
	TPbFixTherMeasTempArea *aptPbTempArea[NVR_MAX_INTEL_NUM][NVR_CAP_THER_MAX_SMART_PLAN_NUM][NVR_CAP_THER_MAX_TYPE_AREA_NUM] = {NULL};
	TPbNvrIntelPoint tPbTempPoint = TPB_NVR_INTEL_POINT__INIT;
	TPbNvrIntelPoint atPbTempPoint[NVR_MAX_INTEL_NUM][NVR_CAP_THER_MAX_SMART_PLAN_NUM][NVR_CAP_THER_MAX_TYPE_AREA_NUM][NVR_CAP_THER_MAX_REGION_NUM];
	TPbNvrIntelPoint *aptPbTempPoint[NVR_MAX_INTEL_NUM][NVR_CAP_THER_MAX_SMART_PLAN_NUM][NVR_CAP_THER_MAX_TYPE_AREA_NUM][NVR_CAP_THER_MAX_REGION_NUM] = {NULL};
	TPbNvrEventContact tPbTempContact = TPB_NVR_EVENT_CONTACT__INIT;
	
	tPbFixCfg.n_fix_intel = NVR_MAX_INTEL_NUM;
	tPbFixCfg.fix_intel = aptPbFixIntelCfg;
	for(i = 0; i < tPbFixCfg.n_fix_intel; i++)
	{
		atPbFixIntelCfg[i] = tPbFixIntelCfg;		///<初始化
		
		aptPbFixIntelCfg[i] = &atPbFixIntelCfg[i];
		
		///<测温参数
		aptPbFixIntelCfg[i]->thermeas_temp = &tPbTempCfg;
		
		tPbTempCfg.has_enable = TRUE;
		tPbTempCfg.enable = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.bEnable;
		tPbTempCfg.has_cursel = TRUE;
		tPbTempCfg.cursel = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel;

		///<测温联动
		tPbTempCfg.link_alarm = &tPbTempContact;
		tPbTempContact.has_post_center = TRUE;
		tPbTempContact.post_center = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm.byPostCenter;
		
		tPbTempContact.has_osd_show = TRUE;
		tPbTempContact.osd_show = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm.byOsdShow;

		tPbTempContact.has_focus = TRUE;
		tPbTempContact.focus = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm.byFocus;

		///<测温方案
		tPbTempCfg.n_temp_info = NVR_CAP_THER_MAX_SMART_PLAN_NUM;
		tPbTempCfg.temp_info = aptPbTempInfo[i];
		for(j = 0;j < tPbTempCfg.n_temp_info;j++)
		{
			atPbTempInfo[i][j] = tPbTempInfo;		///<初始化
		
			aptPbTempInfo[i][j] = &atPbTempInfo[i][j];

			aptPbTempInfo[i][j]->has_temp_format = TRUE;
			aptPbTempInfo[i][j]->temp_format = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].eTherTempFormat;

			aptPbTempInfo[i][j]->has_presetid = TRUE;
			aptPbTempInfo[i][j]->presetid = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].wPresetId;

			aptPbTempInfo[i][j]->has_emissivity = TRUE;
			aptPbTempInfo[i][j]->emissivity = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].fEmissivity;

			aptPbTempInfo[i][j]->has_sensitivity = TRUE;
			aptPbTempInfo[i][j]->sensitivity = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].dwSensitivity;

			aptPbTempInfo[i][j]->has_thertemptype = TRUE;
			aptPbTempInfo[i][j]->thertemptype = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].eTherTempType;

			aptPbTempInfo[i][j]->n_temp_area = NVR_CAP_THER_MAX_TYPE_AREA_NUM;
			aptPbTempInfo[i][j]->temp_area = aptPbTempArea[i][j];
			for(k = 0;k < aptPbTempInfo[i][j]->n_temp_area;k++)
			{
				atPbTempArea[i][j][k] = tPbTempArea;		///<初始化
		
				aptPbTempArea[i][j][k] = &atPbTempArea[i][j][k];

				aptPbTempArea[i][j][k]->has_enable = TRUE;
				aptPbTempArea[i][j][k]->enable = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].bEnable;

				aptPbTempArea[i][j][k]->has_alarm_rule = TRUE;
				aptPbTempArea[i][j][k]->alarm_rule = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].eTherTempAlarmRule;

				aptPbTempArea[i][j][k]->has_alarm_temp = TRUE;
				aptPbTempArea[i][j][k]->alarm_temp = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].nAlarmTemp;

				aptPbTempArea[i][j][k]->has_alarm_tol = TRUE;
				aptPbTempArea[i][j][k]->alarm_tol = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].byAlarmTol;

				aptPbTempArea[i][j][k]->n_tregion = NVR_CAP_THER_MAX_REGION_NUM;
				aptPbTempArea[i][j][k]->tregion = aptPbTempPoint[i][j][k];
				for(m = 0;m < aptPbTempArea[i][j][k]->n_tregion;m++)
				{
					atPbTempPoint[i][j][k][m] = tPbTempPoint;		///<初始化
		
					aptPbTempPoint[i][j][k][m] = &atPbTempPoint[i][j][k][m];

					aptPbTempPoint[i][j][k][m]->has_pos_x = TRUE;
					aptPbTempPoint[i][j][k][m]->pos_x = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].tRegion[m].wStartX;

					aptPbTempPoint[i][j][k][m]->has_pos_y = TRUE;	
					aptPbTempPoint[i][j][k][m]->pos_y = g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].tRegion[m].wStartY;
				}
			}
		}
	}

	do
    {
        dwBufLen = tpb_fix_cfg__get_packed_size(&tPbFixCfg);

        PRINTDBG("get pack size :%u \n", dwBufLen);

        if (dwBufLen > 0)
        {
            ///<为ptPbcSimple->data申请内存，外部释放
            ptPbcSimple->data = NVRALLOC(dwBufLen);
            if(NULL == ptPbcSimple->data)
            {
               PRINTERR("malloc pack buffer failed.\n");
               eRet =  NVR_ERR__ERROR;
               break;
            }

            ///<序列化到buffer中
            ptPbcSimple->len = tpb_fix_cfg__pack(&tPbFixCfg, ptPbcSimple->data);

            if(dwBufLen != ptPbcSimple->len)
            {
                PRINTERR("pack buffer failed, pack len:%d \n", ptPbcSimple->len);
                eRet =  NVR_ERR__ERROR;
                break;
            }
        }
    
    }while (0);

	return eRet;
}


NVRSTATUS NvrFixAlgCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple)
{
    FIX_ASSERT(ptPbcSimple);
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwBufLen = 0;
    u32 i = 0, j = 0, k = 0,m = 0;
    TPbFixProfeIntelCfg tPbFixCfg = TPB_FIX_PROFE_INTEL_CFG__INIT;

    TNvrPointerNode *ptNodeHead = NULL;     ///<保存malloc出来的指针队列头结点

    TPbFixSmokeFireCfg *ptPbSmokeFireCfg = NULL;
    TPbFixAlgEventContact *ptPbSmokeFireContact = NULL;
    TPbFixSmokeFireOtherParam *ptPbSmokeFireOther = NULL;
    TPbFixSmokeFirePlanParam *ptPbSmokeFirePlan = NULL;
    TPbFixPloygon *ptPbPloygon = NULL;
    TPbFixPoint *ptPboint = NULL;
    PRINTDBG("mm@  %d to smoke fire\n", __LINE__);
    ///<烟火识别

    do
    {
        tPbFixCfg.n_smoke_fire = NVR_MAX_LCAM_CHN_NUM;
        tPbFixCfg.smoke_fire = (TPbFixSmokeFireCfg **)NVRALLOC( tPbFixCfg.n_smoke_fire * sizeof(TPbFixSmokeFireCfg *));
        if (NULL == tPbFixCfg.smoke_fire)
        {
            PRINTERR("malloc TPbFixSmokeFireCfg * failed\n");
            eRet = NVR_ERR__MALLOC_FAILED;
            break;
        }
        NvrSrvRecordPointer(&ptNodeHead, (void *)tPbFixCfg.smoke_fire);
        for (i = 0; i < tPbFixCfg.n_smoke_fire; i++)
        {
            tPbFixCfg.smoke_fire[i] = (TPbFixSmokeFireCfg *)NVRALLOC( sizeof(TPbFixSmokeFireCfg));
            if (NULL == tPbFixCfg.smoke_fire[i])
            {
                PRINTERR("malloc TPbFixSmokeFireCfg failed\n");
                eRet = NVR_ERR__MALLOC_FAILED;
                break;
            }
            NvrSrvRecordPointer(&ptNodeHead, (void *)tPbFixCfg.smoke_fire[i]);
            ptPbSmokeFireCfg = tPbFixCfg.smoke_fire[i];
            NvrFixTPbFixSmokeFireCfgInit(ptPbSmokeFireCfg);      ///<初始化protobuf结构体
            ptPbSmokeFireCfg->has_enable = TRUE;
            ptPbSmokeFireCfg->enable = g_tnvrFixProIntelCfg.atSmokefire[i].bEnable;
            PRINTDBG("mm@  %d to smoke fire\n", __LINE__);
            ///<烟火识别联动
            ptPbSmokeFireCfg->link_alarm = (TPbFixAlgEventContact *)NVRALLOC( sizeof(TPbFixAlgEventContact));
            if (NULL == ptPbSmokeFireCfg->link_alarm)
            {
                PRINTERR("malloc TPbNvrEventContact failed\n");
                eRet = NVR_ERR__MALLOC_FAILED;
                break;
            }
            NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbSmokeFireCfg->link_alarm);
            ptPbSmokeFireContact = ptPbSmokeFireCfg->link_alarm;
            NvrFixTPbFixAlgEventContactInit(ptPbSmokeFireContact);      ///<初始化protobuf结构体
            ptPbSmokeFireContact->has_post_center = TRUE;
            ptPbSmokeFireContact->post_center = g_tnvrFixProIntelCfg.atSmokefire[i].tLinkAlarm.byPostCenter;
            ptPbSmokeFireContact->has_osd_show = TRUE;
            ptPbSmokeFireContact->osd_show = g_tnvrFixProIntelCfg.atSmokefire[i].tLinkAlarm.byOsdShow;
            ptPbSmokeFireContact->has_focus = TRUE;
            ptPbSmokeFireContact->focus = g_tnvrFixProIntelCfg.atSmokefire[i].tLinkAlarm.byFocus;
            PRINTDBG("mm@  %d to smoke fire\n", __LINE__);
            ///<烟火识别其他参数
            ptPbSmokeFireCfg->other_param = (TPbFixSmokeFireOtherParam *)NVRALLOC( sizeof(TPbFixSmokeFireOtherParam));
            if (NULL == ptPbSmokeFireCfg->other_param)
            {
                PRINTERR("malloc TPbFixSmokeFireOtherParam failed\n");
                eRet = NVR_ERR__MALLOC_FAILED;
                break;
            }
            NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbSmokeFireCfg->other_param);
            ptPbSmokeFireOther = ptPbSmokeFireCfg->other_param;
            NvrFixTPbFixSmokeFireOtherParamInit(ptPbSmokeFireOther);      ///<初始化protobuf结构体
            ptPbSmokeFireOther->has_pos_x = TRUE;
            ptPbSmokeFireOther->pos_x = g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wX;
            ptPbSmokeFireOther->has_pos_y = TRUE;
            ptPbSmokeFireOther->pos_y = g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wY;
            ptPbSmokeFireOther->has_width = TRUE;
            ptPbSmokeFireOther->width = g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wWidth;
            ptPbSmokeFireOther->has_hight = TRUE;
            ptPbSmokeFireOther->hight = g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wHight;
            ptPbSmokeFireOther->has_sensity = TRUE;
            ptPbSmokeFireOther->sensity = g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wSensity;
            ptPbSmokeFireOther->has_ducnfirm = TRUE;
            ptPbSmokeFireOther->ducnfirm = g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.eDuCnfirm;
            ptPbSmokeFireOther->has_twmode = TRUE;
            ptPbSmokeFireOther->twmode = g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.eTargetWaitMode;
            ptPbSmokeFireOther->has_twtime = TRUE;
            ptPbSmokeFireOther->twtime = g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wTargetWaitTime;
            PRINTDBG("mm@  %d to smoke fire\n", __LINE__);
            ///<烟火识别参数
            ptPbSmokeFireCfg->n_plan_param = NVR_PUI_PRESET_MAX_NUM + 1;
            ptPbSmokeFireCfg->plan_param = (TPbFixSmokeFirePlanParam **)NVRALLOC(ptPbSmokeFireCfg->n_plan_param * sizeof(TPbFixSmokeFirePlanParam *));
            if (NULL == ptPbSmokeFireCfg->plan_param)
            {
                PRINTERR("malloc TPbFixSmokeFirePlanParam * failed\n");
                eRet = NVR_ERR__MALLOC_FAILED;
                break;
            }
            NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbSmokeFireCfg->plan_param);
            for(j = 0; j < ptPbSmokeFireCfg->n_plan_param; j++)
            {
                ptPbSmokeFireCfg->plan_param[j] = (TPbFixSmokeFirePlanParam *)NVRALLOC(sizeof(TPbFixSmokeFirePlanParam));
                if (NULL == ptPbSmokeFireCfg->plan_param[j])
                {
                    PRINTERR("malloc %d TPbFixSmokeFirePlanParam failed\n", j);
                    eRet = NVR_ERR__MALLOC_FAILED;
                    break;
                }
                NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbSmokeFireCfg->plan_param[j]);
                ptPbSmokeFirePlan = ptPbSmokeFireCfg->plan_param[j];
                NvrFixTPbFixSmokeFirePlanParamInit(ptPbSmokeFirePlan);      ///<初始化protobuf结构体

                ptPbSmokeFirePlan->has_enable = TRUE;
                ptPbSmokeFirePlan->enable = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].bEnable;

                ptPbSmokeFirePlan->has_presetid = TRUE;
                ptPbSmokeFirePlan->presetid = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].nPresetId;

                ptPbSmokeFirePlan->has_check_region_num = TRUE;
                ptPbSmokeFirePlan->check_region_num = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].wCheckRegion;
                ptPbSmokeFirePlan->n_check_region_area = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].wCheckRegion;
                if (ptPbSmokeFirePlan->n_check_region_area)
                {
                    ptPbSmokeFirePlan->check_region_area = (TPbFixPloygon **)NVRALLOC(ptPbSmokeFirePlan->n_check_region_area * sizeof(TPbFixPloygon *));
                    if (NULL == ptPbSmokeFirePlan->check_region_area)
                    {
                        PRINTERR("malloc TPbFixPloygon * failed\n");
                        eRet = NVR_ERR__MALLOC_FAILED;
                        break;
                    }
                    NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbSmokeFirePlan->check_region_area);
                    for(k = 0; k < ptPbSmokeFirePlan->n_check_region_area; k++)
                    {
                        ptPbSmokeFirePlan->check_region_area[k] = (TPbFixPloygon *)NVRALLOC(sizeof(TPbFixPloygon));
                        if (NULL == ptPbSmokeFirePlan->check_region_area[k])
                        {
                            PRINTERR("malloc %d TPbFixPloygon failed\n", k);
                            eRet = NVR_ERR__MALLOC_FAILED;
                            break;
                        }
                        NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbSmokeFirePlan->check_region_area[k]);
                        ptPbPloygon = ptPbSmokeFirePlan->check_region_area[k];
                        NvrFixTPbFixPloygonInit(ptPbPloygon);      ///<初始化protobuf结构体

                        ptPbPloygon->has_point_num = TRUE;
                        ptPbPloygon->point_num =  g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tCheckRegion[k].dwPointNum;
                        ptPbPloygon->n_point = ptPbPloygon->point_num;
                        ptPbPloygon->point = (TPbFixPoint **)NVRALLOC(ptPbPloygon->n_point * sizeof(TPbFixPoint *));
                        for(m = 0; m < ptPbPloygon->n_point; m++)
                        {
                            ptPbPloygon->point[m] = (TPbFixPoint *)NVRALLOC(sizeof(TPbFixPoint));
                            if (NULL == ptPbPloygon->point[m])
                            {
                                PRINTERR("malloc %d TPbFixPoint failed\n", k);
                                eRet = NVR_ERR__MALLOC_FAILED;
                                break;
                            }
                            NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbPloygon->point[m]);
                            ptPboint = ptPbPloygon->point[m];
                            NvrFixTPbFixPointInit(ptPboint);      ///<初始化protobuf结构体

                            ptPboint->has_pos_x = TRUE;
                            ptPboint->pos_x = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tCheckRegion[k].atPolygonPoint[m].wPosX;
                            ptPboint->has_pos_y = TRUE;
                            ptPboint->pos_y = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tCheckRegion[k].atPolygonPoint[m].wPosY;
                        }
                    }
                }
                ptPbSmokeFirePlan->has_shield_region_num = TRUE;
                ptPbSmokeFirePlan->shield_region_num = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].wShieldRegion;
                ptPbSmokeFirePlan->n_shield_region_area = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].wShieldRegion;
                if (ptPbSmokeFirePlan->n_shield_region_area)
                {
                    ptPbSmokeFirePlan->shield_region_area = (TPbFixPloygon **)NVRALLOC(ptPbSmokeFirePlan->n_shield_region_area * sizeof(TPbFixPloygon *));
                    if (NULL == ptPbSmokeFirePlan->shield_region_area)
                    {
                        PRINTERR("shield_region_area malloc TPbFixPloygon * failed\n");
                        eRet = NVR_ERR__MALLOC_FAILED;
                        break;
                    }
                    NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbSmokeFirePlan->shield_region_area);
                    for(k = 0; k < ptPbSmokeFirePlan->n_shield_region_area; k++)
                    {
                        ptPbSmokeFirePlan->shield_region_area[k] = (TPbFixPloygon *)NVRALLOC(sizeof(TPbFixPloygon));
                        if (NULL == ptPbSmokeFirePlan->check_region_area[k])
                        {
                            PRINTERR("shield_region_area malloc %d TPbFixPloygon failed\n", k);
                            eRet = NVR_ERR__MALLOC_FAILED;
                            break;
                        }
                        NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbSmokeFirePlan->shield_region_area[k]);
                        ptPbPloygon = ptPbSmokeFirePlan->shield_region_area[k];
                        NvrFixTPbFixPloygonInit(ptPbPloygon);      ///<初始化protobuf结构体

                        ptPbPloygon->has_point_num = TRUE;
                        ptPbPloygon->point_num =  g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tShieldRegion[k].dwPointNum;
                        ptPbPloygon->n_point = ptPbPloygon->point_num;
                        ptPbPloygon->point = (TPbFixPoint **)NVRALLOC(ptPbPloygon->n_point * sizeof(TPbFixPoint *));
                        for(m = 0; m < ptPbPloygon->n_point; m++)
                        {
                            ptPbPloygon->point[m] = (TPbFixPoint *)NVRALLOC(sizeof(TPbFixPoint));
                            if (NULL == ptPbPloygon->point[m])
                            {
                                PRINTERR("shield_region_area malloc %d TPbFixPoint failed\n", k);
                                eRet = NVR_ERR__MALLOC_FAILED;
                                break;
                            }
                            NvrSrvRecordPointer(&ptNodeHead, (void *)ptPbPloygon->point[m]);
                            ptPboint = ptPbPloygon->point[m];
                            NvrFixTPbFixPointInit(ptPboint);      ///<初始化protobuf结构体

                            ptPboint->has_pos_x = TRUE;
                            ptPboint->pos_x = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tShieldRegion[k].atPolygonPoint[m].wPosX;
                            ptPboint->has_pos_y = TRUE;
                            ptPboint->pos_y = g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tShieldRegion[k].atPolygonPoint[m].wPosY;
                        }
                    }
                }
            }
        }
    } while (0);

    do
    {
        dwBufLen = tpb_fix_profe_intel_cfg__get_packed_size(&tPbFixCfg);

        PRINTDBG("get pack size :%u \n", dwBufLen);

        if (dwBufLen > 0)
        {
            ///<为ptPbcSimple->data申请内存，外部释放
            ptPbcSimple->data = NVRALLOC(dwBufLen);
            if(NULL == ptPbcSimple->data)
            {
               PRINTERR("malloc pack buffer failed.\n");
               eRet =  NVR_ERR__ERROR;
               break;
            }

            ///<序列化到buffer中
            ptPbcSimple->len = tpb_fix_profe_intel_cfg__pack(&tPbFixCfg, ptPbcSimple->data);

            if(dwBufLen != ptPbcSimple->len)
            {
                PRINTERR("pack buffer failed, pack len:%d \n", ptPbcSimple->len);
                eRet =  NVR_ERR__ERROR;
                break;
            }
        }

    }while (0);

    ///free掉序列化过程分配的所有指针
    NvrSrvFreePointer(&ptNodeHead);
    ///free掉记录指针列表的头指针
    if(NULL != ptNodeHead)
    {
        NVRFREE(ptNodeHead);
        ptNodeHead = NULL;
    }

    return eRet;
}

NVRSTATUS NvrFixCfgProtoToStruct(const TPbFixCfg *ptPbNvrFixCfg)
{
	FIX_ASSERT(ptPbNvrFixCfg);
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 i = 0,j = 0,k = 0,m = 0;

	//先赋予默认值，再赋予实际值
	NvrFixDefaultCfgInit();

	PRINTDBG("start...\n");

	//isp参数
	for(i = 0; i < ptPbNvrFixCfg->n_fix_isp; i++)
	{
		if(NULL != ptPbNvrFixCfg->fix_isp[i])
		{
			///<zf参数
			if(ptPbNvrFixCfg->fix_isp[i]->has_zf_day_zoom)
			{
				g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_DAY].dwZoomPosition = ptPbNvrFixCfg->fix_isp[i]->zf_day_zoom;
			}
			if(ptPbNvrFixCfg->fix_isp[i]->has_zf_day_focus)
			{
				g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_DAY].dwFocus = ptPbNvrFixCfg->fix_isp[i]->zf_day_focus;
			}	
			if(ptPbNvrFixCfg->fix_isp[i]->has_zf_night_zoom)
			{
				g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_NIGHT].dwZoomPosition = ptPbNvrFixCfg->fix_isp[i]->zf_night_zoom;
			}
			if(ptPbNvrFixCfg->fix_isp[i]->has_zf_night_focus)
			{
				g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_NIGHT].dwFocus = ptPbNvrFixCfg->fix_isp[i]->zf_night_focus;
			}	

			//手动聚焦参数
			if(ptPbNvrFixCfg->fix_isp[i]->has_manual_focus)
			{
				g_tNvrFixCfg.tIspCfg[i].dwManualFocus = ptPbNvrFixCfg->fix_isp[i]->manual_focus;
			}

			PRINTDBG("[%u]dwManualFocus :%u,zf Day z%u-f%u,Night z%u-f%u\n",i,\
										g_tNvrFixCfg.tIspCfg[i].dwManualFocus,\
										g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_DAY].dwZoomPosition,\
										g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_DAY].dwFocus,\
										g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_NIGHT].dwZoomPosition,\
										g_tNvrFixCfg.tIspCfg[i].tZFCfg[NVR_ISP_IRCUT_MODE_NIGHT].dwFocus);
		}
	}

	PRINTDBG("NvrPtzCfgPtzModuleProtoToStruct ptz num:%d\n",ptPbNvrFixCfg->n_fix_ptz);

	//ptz参数,只有一个
	if(ptPbNvrFixCfg->n_fix_ptz != 0)
	{
		if(NULL != ptPbNvrFixCfg->fix_ptz && NULL != ptPbNvrFixCfg->fix_ptz[0])
		{
			///TNvrPtzCtrlPrm配置
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->ptz_ctrl_param)
			 {
			 	
				g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm.byProtocalType = ptPbNvrFixCfg->fix_ptz[0]->ptz_ctrl_param->protocal_type;
				g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm.byAddress = ptPbNvrFixCfg->fix_ptz[0]->ptz_ctrl_param->address;
				g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm.byExtraAddress = ptPbNvrFixCfg->fix_ptz[0]->ptz_ctrl_param->extra_address;
			 }
			 
			 ///<TNvrPtzBasicState配置
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->ptz_state)
			 {
			 	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwAutoFlip = NVR_PTZ_MODE_ON;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDepthrateSpd = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->depth_rate_spd;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwScanSpeedValue = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->scan_speed_value;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwPreSetSpdValue= ptPbNvrFixCfg->fix_ptz[0]->ptz_state->preset_spd_value;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eZoomSpeedValue= ptPbNvrFixCfg->fix_ptz[0]->ptz_state->ezoom_speed_value;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.ePTOsdMode = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->ept_osd_mode;
				strncpy(g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.achPtzSoftVer,ptPbNvrFixCfg->fix_ptz[0]->ptz_state->ptz_soft_ver,sizeof(g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.achPtzSoftVer)-1);
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwVidEncFreeze = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->vid_enc_freeze;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPriorityType = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->priority_type;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPriDelayTime = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->pri_delay_time;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwInfaredState = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->infared_state;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eInfaredMode = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->einfared_mode;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwInfaredSens = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->infared_sens;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwInfaredValue = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->infared_value;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwNearInfaredValue = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->near_infared_value;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eLaserSwitch = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->e_laser_switch;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eLaserMode = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->e_laser_mode;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserDist = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->laser_dist;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserIntensity = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->laser_intensity;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserCentradMode = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->laser_centrad_mode;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwWiperState = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->wiper_state;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDefrostState = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->defrost_state;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwPosLimitDisplay = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->pos_limit_display;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eVerticaRange = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->evertica_range;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserCentradSpeed = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->laser_centrad_speed;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDeMistState = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->demist_state;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDeMistTime = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->demist_time;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwSLState = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->sl_state;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eSLMode = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->esl_mode;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwSLValue = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->sl_value;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPowerOnMode = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->power_on_mode;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLedMainSwitch = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->led_main_switch;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eFanDemisterMode = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->efan_demister_mode;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPowerOffMode = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->power_off_mode;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byCarMode = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->car_mode;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byExtWifiSwitch = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->ext_wifi_switch;
				g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPtzId = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->ptz_id;
				if(ptPbNvrFixCfg->fix_ptz[0]->ptz_state->has_manu_limit_pos)
				{
					g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwManuLimitPos = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->manu_limit_pos;
				}
				if(ptPbNvrFixCfg->fix_ptz[0]->ptz_state->has_scan_limit_pos)
				{
					g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwScanLimitPos = ptPbNvrFixCfg->fix_ptz[0]->ptz_state->scan_limit_pos;
				}
				
			 }
			 
			 ///<TNvrPowerOffRsmCfg配置
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->poweroff_rsm_cfg)
			 {
				 g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.bEnable = ptPbNvrFixCfg->fix_ptz[0]->poweroff_rsm_cfg->enable;
				  g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.eResumeMode = ptPbNvrFixCfg->fix_ptz[0]->poweroff_rsm_cfg->resume_mode;
				  g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.adwParam[NVR_DEV_POWEROFF_LOAD_PRESET] =  ptPbNvrFixCfg->fix_ptz[0]->poweroff_rsm_cfg->param[0];
				  g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.adwParam[NVR_DEV_POWEROFF_MEMORY] = ptPbNvrFixCfg->fix_ptz[0]->poweroff_rsm_cfg->param[1];

			 }
			 
			 ///<TNvrPresetInfo配置
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->preset_info)
			 {
			 
				 for(i=0;i<ptPbNvrFixCfg->fix_ptz[0]->n_preset_info;i++)
				 {
					 g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].bIsSet = ptPbNvrFixCfg->fix_ptz[0]->preset_info[i]->is_set;
				 	 g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].nFoucsPos = ptPbNvrFixCfg->fix_ptz[0]->preset_info[i]->foucs_pos;
				 	 g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].nZoomPos = ptPbNvrFixCfg->fix_ptz[0]->preset_info[i]->zoom_pos;
				 	 g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].dwSpeed = ptPbNvrFixCfg->fix_ptz[0]->preset_info[i]->speed;
					 g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].dwAliasLen = ptPbNvrFixCfg->fix_ptz[0]->preset_info[i]->alias_len;

					 strncpy((s8*)g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].abyAlias,ptPbNvrFixCfg->fix_ptz[0]->preset_info[i]->alias,g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].dwAliasLen);

					 g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].wHPos = ptPbNvrFixCfg->fix_ptz[0]->preset_info[i]->h_pos;
					 g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].wVPos = ptPbNvrFixCfg->fix_ptz[0]->preset_info[i]->w_vpos;
					 g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].bSpecial = ptPbNvrFixCfg->fix_ptz[0]->preset_info[i]->special;
				
				 }
			 }
			 
			  ///<TNvrPathCrsInfo配置
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->path_crs_info)
			 {
				 for(i=0;i<ptPbNvrFixCfg->fix_ptz[0]->n_path_crs_info;i++)
				 {
					 g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].wPresetCnt= ptPbNvrFixCfg->fix_ptz[0]->path_crs_info[i]->preset_cnt;

					 for(j= 0;j< ptPbNvrFixCfg->fix_ptz[0]->path_crs_info[i]->n_pathcrs_preset_info;j++)
					 {
					 	if(NULL != ptPbNvrFixCfg->fix_ptz[0]->path_crs_info[i]->pathcrs_preset_info[j])
					 	{
							g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].bIsSet = ptPbNvrFixCfg->fix_ptz[0]->path_crs_info[i]->pathcrs_preset_info[j]->is_set;
							g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].wPrenum = ptPbNvrFixCfg->fix_ptz[0]->path_crs_info[i]->pathcrs_preset_info[j]->preset_id;
							g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].wStaytime = ptPbNvrFixCfg->fix_ptz[0]->path_crs_info[i]->pathcrs_preset_info[j]->stay_time;
							g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].dwAliasLen = ptPbNvrFixCfg->fix_ptz[0]->path_crs_info[i]->pathcrs_preset_info[j]->alias_len;
							memcpy((s8*)g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].abyAlias,ptPbNvrFixCfg->fix_ptz[0]->path_crs_info[i]->pathcrs_preset_info[j]->alias,g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].dwAliasLen);
						}
					 }
				
				 }
			 }
			 
			  ///<TNvrWatchOnParam配置
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->watch_on_param)
			 {
				 g_tNvrFixCfg.tPztCfg[0].tWatchOn.bEnable = ptPbNvrFixCfg->fix_ptz[0]->watch_on_param->enable;
				 g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwWaitTimeSec= ptPbNvrFixCfg->fix_ptz[0]->watch_on_param->wait_time_ec;
				 g_tNvrFixCfg.tPztCfg[0].tWatchOn.byTaskType= ptPbNvrFixCfg->fix_ptz[0]->watch_on_param->task_type;
				 g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwPresetID= ptPbNvrFixCfg->fix_ptz[0]->watch_on_param->preset_id;
				 g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwSyncScanID= ptPbNvrFixCfg->fix_ptz[0]->watch_on_param->sync_scan_id;
				 g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwPathCruiseID = ptPbNvrFixCfg->fix_ptz[0]->watch_on_param->path_cruise_id;

			 }

			  ///<TNvrTmingTaskParam配置
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->timing_task_param)
			 {
				 g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.bEnable= ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->timing_task_head->enable;
				 g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.dwResumeTime= ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->timing_task_head->resume_time;
				 g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.dwDataAddr= ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->timing_task_head->data_addr;
				 g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.dwTimePeriodNum= ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->timing_task_head->time_period_num;
				 g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.dwTmingInfoSize= ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->timing_task_head->tming_info_size;

				 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->evday_param)
				 {
					 for(i = 0;i<NVR_WEEK_DAY;i++)
					 {
					 	if(NULL != ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->evday_param[i])
					 	{
							for(j=0;j<NVR_DEV_MAX_TMINGTASK_PERIOD;j++)
						 	{
						 		if(NULL != ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->evday_param[i]->tming_info[j])
						 		{
									g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].bIsEnable = ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->evday_param[i]->tming_info[j]->is_enable;
									g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].nStartTime= ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->evday_param[i]->tming_info[j]->start_time;
									g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].nEndTime= ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->evday_param[i]->tming_info[j]->end_time;
									g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].eTaskType= ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->evday_param[i]->tming_info[j]->task_type;
									g_tNvrFixCfg.tPztCfg[0].tTimeTask.atEvdayParam[i].atTmingInfo[j].byTaskParam= ptPbNvrFixCfg->fix_ptz[0]->timing_task_param->evday_param[i]->tming_info[j]->task_param;
								}

							}
						}

					 }
				 }
			 }
			 
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->limit_state_param)
			 {
			 	if(TRUE == ptPbNvrFixCfg->fix_ptz[0]->limit_state_param->has_manu_limit_pos)
			 	{
					g_tNvrFixCfg.tPztCfg[0].tLimitState.dwManuLimitPos = ptPbNvrFixCfg->fix_ptz[0]->limit_state_param->manu_limit_pos;
				}
				if(TRUE == ptPbNvrFixCfg->fix_ptz[0]->limit_state_param->has_scan_limit_pos)
			 	{
					g_tNvrFixCfg.tPztCfg[0].tLimitState.dwScanLimitPos= ptPbNvrFixCfg->fix_ptz[0]->limit_state_param->scan_limit_pos;
				}
			 }
			 else
			 {
				///<参数不存在使用默认参数
			 }

			///<10,方位图参数配置
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->cool_param)
			 {
				if(TRUE == ptPbNvrFixCfg->fix_ptz[0]->cool_param->has_x_pos)
				{
					 g_tNvrFixCfg.tPztCfg[0].tCooParam.nX =ptPbNvrFixCfg->fix_ptz[0]->cool_param->x_pos;
				}
				if(TRUE == ptPbNvrFixCfg->fix_ptz[0]->cool_param->has_y_pos)
				{
					 g_tNvrFixCfg.tPztCfg[0].tCooParam.nY=ptPbNvrFixCfg->fix_ptz[0]->cool_param->y_pos;
				}
			 }
			 
			 ///<11,除雾配置
			 if(NULL != ptPbNvrFixCfg->fix_ptz[0]->demist_parm)
			 {
				if(TRUE == ptPbNvrFixCfg->fix_ptz[0]->demist_parm->has_demist_mode)
				{
		 			 g_tNvrFixCfg.tPztCfg[0].tDeMistCfg.eDeMistMode =ptPbNvrFixCfg->fix_ptz[0]->demist_parm->demist_mode;
		 		}
				if(TRUE == ptPbNvrFixCfg->fix_ptz[0]->demist_parm->has_demist_time)
				{
					g_tNvrFixCfg.tPztCfg[0].tDeMistCfg.dwDeMistTime = ptPbNvrFixCfg->fix_ptz[0]->demist_parm->demist_time;
				}
			 }
			 else ///<新加配置需配置下默认参数
			 {
				g_tNvrFixCfg.tPztCfg[0].tDeMistCfg.eDeMistMode = NVR_ISP_DE_MIST_CLOSE;
				g_tNvrFixCfg.tPztCfg[0].tDeMistCfg.dwDeMistTime = 60;
			 }
			 
			 PRINTDBG("NvrPtzCfgPtzModuleProtoToStruct end\n");
		}
	}

	///<媒控配置
	for(i = 0; i < ptPbNvrFixCfg->n_fix_mc; i++)
	{
		if(NULL != ptPbNvrFixCfg->fix_mc[i])
		{
			if(ptPbNvrFixCfg->fix_mc[i]->has_smallmap_pos_type)
			{
				g_tNvrFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosType = (ENvrPuiDualChnOverlayPosType)ptPbNvrFixCfg->fix_mc[0]->smallmap_pos_type;
			}
			if(ptPbNvrFixCfg->fix_mc[i]->has_smallmap_pos_size)
			{	
				g_tNvrFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosSize = (ENvrPuiDualChnOverlayPosType)ptPbNvrFixCfg->fix_mc[0]->smallmap_pos_size;
			}	
		}
	}

	///<智能配置
	for(i = 0; i < ptPbNvrFixCfg->n_fix_intel; i++)
	{
		if(NULL != ptPbNvrFixCfg->fix_intel[i])
		{
			if(NULL != ptPbNvrFixCfg->fix_intel[i]->thermeas_temp)
			{
				if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->has_enable)
				{
					g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.bEnable = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->enable;
				}
				if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->has_cursel)
				{
					g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->cursel;
				}
				
				if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->link_alarm)
				{
					if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->link_alarm->has_post_center)
					{
						g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm.byPostCenter = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->link_alarm->post_center;
					}
					if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->link_alarm->has_osd_show)
					{
						g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm.byOsdShow = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->link_alarm->osd_show;
					}
					if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->link_alarm->has_focus)
					{
						g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm.byFocus = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->link_alarm->focus;
					}
				}

				for(j = 0; j < ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->n_temp_info; j++)
				{
					if(NULL != ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j])
					{
						if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->has_temp_format)
						{
							g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].eTherTempFormat = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_format;
						}
						if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->has_presetid)
						{
							g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].wPresetId = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->presetid;
						}
						if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->has_emissivity)
						{
							g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].fEmissivity = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->emissivity;
						}
						if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->has_sensitivity)
						{
							g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].dwSensitivity = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->sensitivity;
						}
						if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->has_thertemptype)
						{
							g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].eTherTempType = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->thertemptype;
						}

						for(k = 0; k < ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->n_temp_area; k++)
						{		
							if(NULL != ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k])
							{
								if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->has_enable)
								{
									g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].bEnable = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->enable;
								}
								if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->has_alarm_rule)
								{
									g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].eTherTempAlarmRule = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->alarm_rule;
								}
								if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->has_alarm_temp)
								{
									g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].nAlarmTemp = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->alarm_temp;
								}
								if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->has_alarm_tol)
								{
									g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].byAlarmTol = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->alarm_tol;
								}

								for(m = 0; m < ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->n_tregion; m++)
								{
									if(NULL != ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->tregion[m])
									{
										if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->tregion[m]->has_pos_x)
										{
											g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].tRegion[m].wStartX = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->tregion[m]->pos_x;
										}
										if(ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->tregion[m]->has_pos_y)
										{
											g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[j].tArea[k].tRegion[m].wStartY = ptPbNvrFixCfg->fix_intel[i]->thermeas_temp->temp_info[j]->temp_area[k]->tregion[m]->pos_y;
										}
									}
								}
							}							
						}
					}
				}
			}
		}
	}
	return eRet;
}

NVRSTATUS NvrFixAlgCfgProtoToStruct(const TPbFixProfeIntelCfg *ptPbNvrFixCfg)
{
    FIX_ASSERT(ptPbNvrFixCfg);
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 i = 0,j = 0,k = 0,m = 0;

    PRINTDBG("start...\n");

    ///<智能配置
    for(i = 0; i < ptPbNvrFixCfg->n_smoke_fire; i++)
    {
        ///< 烟火识别
        if(NULL != ptPbNvrFixCfg->smoke_fire[i])
        {
            PRINTDBG("has smoke fire param \n");
            if(ptPbNvrFixCfg->smoke_fire[i]->has_enable)
            {
                g_tnvrFixProIntelCfg.atSmokefire[i].bEnable = ptPbNvrFixCfg->smoke_fire[i]->enable;
                PRINTDBG("bEnable:%d \n", g_tnvrFixProIntelCfg.atSmokefire[i].bEnable);
            }

            if(ptPbNvrFixCfg->smoke_fire[i]->link_alarm)
            {
                PRINTDBG("has smoke fire link_alarm \n");
                if(ptPbNvrFixCfg->smoke_fire[i]->link_alarm->has_post_center)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tLinkAlarm.byPostCenter = ptPbNvrFixCfg->smoke_fire[i]->link_alarm->post_center;
                }
                if(ptPbNvrFixCfg->smoke_fire[i]->link_alarm->has_osd_show)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tLinkAlarm.byOsdShow = ptPbNvrFixCfg->smoke_fire[i]->link_alarm->osd_show;
                }
                if(ptPbNvrFixCfg->smoke_fire[i]->link_alarm->has_focus)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tLinkAlarm.byFocus = ptPbNvrFixCfg->smoke_fire[i]->link_alarm->focus;
                }
            }

            if(ptPbNvrFixCfg->smoke_fire[i]->other_param)
            {
                PRINTDBG("has smoke fire other_param \n");
                if(ptPbNvrFixCfg->smoke_fire[i]->other_param->has_pos_x)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wX = ptPbNvrFixCfg->smoke_fire[i]->other_param->pos_x;
                }
                if(ptPbNvrFixCfg->smoke_fire[i]->other_param->has_pos_y)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wY = ptPbNvrFixCfg->smoke_fire[i]->other_param->pos_y;
                }
                if(ptPbNvrFixCfg->smoke_fire[i]->other_param->has_width)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wWidth = ptPbNvrFixCfg->smoke_fire[i]->other_param->width;
                }
                if(ptPbNvrFixCfg->smoke_fire[i]->other_param->has_hight)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wHight = ptPbNvrFixCfg->smoke_fire[i]->other_param->hight;
                }
                if(ptPbNvrFixCfg->smoke_fire[i]->other_param->has_sensity)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wSensity = ptPbNvrFixCfg->smoke_fire[i]->other_param->sensity;
                }
                if(ptPbNvrFixCfg->smoke_fire[i]->other_param->has_ducnfirm)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.eDuCnfirm = ptPbNvrFixCfg->smoke_fire[i]->other_param->ducnfirm;
                }
                if(ptPbNvrFixCfg->smoke_fire[i]->other_param->has_twmode)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.eTargetWaitMode = ptPbNvrFixCfg->smoke_fire[i]->other_param->twmode;
                }
                if(ptPbNvrFixCfg->smoke_fire[i]->other_param->has_twtime)
                {
                    g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wTargetWaitTime = ptPbNvrFixCfg->smoke_fire[i]->other_param->twtime;
                }
            }

            PRINTDBG("n_plan_param:%d \n", ptPbNvrFixCfg->smoke_fire[i]->n_plan_param);
            for(j = 0; j < ptPbNvrFixCfg->smoke_fire[i]->n_plan_param; j++)
            {
                if(NULL != ptPbNvrFixCfg->smoke_fire[i]->plan_param[j] && j <= NVR_PUI_PRESET_MAX_NUM)
                {
                    if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->has_enable)
                    {
                        g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].bEnable = ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->enable;
                        if (g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].bEnable)
                        {
                            PRINTDBG("get bEnable:%d \n", g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].bEnable);
                        }
                    }

                    if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->has_presetid)
                    {
                        g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].nPresetId = ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->presetid;
                    }

                    if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->has_check_region_num)
                    {
                        g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].wCheckRegion = ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_num;
                        if (g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].wCheckRegion)
                        {
                            PRINTDBG("get wCheckRegion:%d \n", g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].wCheckRegion);
                        }
                    }

                    for(k = 0; k < ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->n_check_region_area; k++)
                    {
                        if(NULL != ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_area[k] && k < ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_num)
                        {
                            if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_area[k]->has_point_num)
                            {
                                g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tCheckRegion[k].dwPointNum = ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_area[k]->point_num;
                                if (g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tCheckRegion[k].dwPointNum)
                                {
                                    PRINTDBG("get wCheckRegion dwPointNum:%d \n", g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tCheckRegion[k].dwPointNum);
                                }
                            }

                            for(m = 0; m < ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_area[k]->point_num; m++)
                            {
                                if(NULL != ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_area[k]->point[m])
                                {
                                    if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_area[k]->point[m]->has_pos_x)
                                    {
                                        g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tCheckRegion[k].atPolygonPoint[m].wPosX =
                                                ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_area[k]->point[m]->pos_x;
                                    }
                                    if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_area[k]->point[m]->has_pos_y)
                                    {
                                        g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tCheckRegion[k].atPolygonPoint[m].wPosY =
                                                ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->check_region_area[k]->point[m]->pos_y;
                                    }
                                }
                            }
                        }
                    }
                    if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->has_shield_region_num)
                    {
                        g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].wShieldRegion = ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_num;
                    }
                    for(k = 0; k < ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->n_shield_region_area; k++)
                    {
                        if(NULL != ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_area[k] && k < ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_num)
                        {
                            if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_area[k]->has_point_num)
                            {
                                g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tShieldRegion[k].dwPointNum = ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_area[k]->point_num;
                            }

                            for(m = 0; m < ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_area[k]->point_num; m++)
                            {
                                if(NULL != ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_area[k]->point[m])
                                {
                                    if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_area[k]->point[m]->has_pos_x)
                                    {
                                        g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tShieldRegion[k].atPolygonPoint[m].wPosX =
                                                ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_area[k]->point[m]->pos_x;
                                    }
                                    if(ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_area[k]->point[m]->has_pos_y)
                                    {
                                        g_tnvrFixProIntelCfg.atSmokefire[i].atPreset[j].tShieldRegion[k].atPolygonPoint[m].wPosY =
                                                ptPbNvrFixCfg->smoke_fire[i]->plan_param[j]->shield_region_area[k]->point[m]->pos_y;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

	return eRet;
}

NVRSTATUS NvrFixCfgSave(void)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;			///<分配器
	TNvrPbAllocData tPbAllocData;				///<分配器上下文

	mzero(tPbAlocator);
	mzero(tPbAllocData)

	///<内存分配器初始化
	tPbcSimple.allocator = &tPbAlocator;
	NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

	do
	{
		///<序列化动作待添加
		eRet = NvrFixCfgStructToProto(&tPbcSimple);
		if(NVR_ERR__OK != eRet)
		{
			PRINTERR("NveFixCfgSave FixCfgStructToProto failed ret:%d\n", eRet);
			break;
		}

		///<配置写入配置文件中
		eRet = NVRCfgSetParam(NVR_CFG_SERVER, FIX_CFG, tPbcSimple.data, tPbcSimple.len);
		if(NVR_ERR__OK != eRet)
		{
			PRINTERR("NVRCfgSetParam failed ret:%d\n", eRet);
			break;
		}

	}while(0);

	///<释放tPbcSimple中malloc出来的空间
	if(NULL != tPbcSimple.data)
	{
		NVRFREE(tPbcSimple.data);
		tPbcSimple.data = NULL;
	}
	///<判断被分配的内存是否全部被释放，没全部释放则记录日志
	if(0 != tPbAllocData.dwAllocCount)
	{
		PRINTERR("NveFixCfgSave all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
	}

	return eRet;
}

NVRSTATUS NvrFixAlgCfgSave(void)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
    ProtobufCAllocator  tPbAlocator;            ///<分配器
    TNvrPbAllocData tPbAllocData;               ///<分配器上下文

    mzero(tPbAlocator);
    mzero(tPbAllocData)

    ///<内存分配器初始化
    tPbcSimple.allocator = &tPbAlocator;
    NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

    do
    {
        ///<序列化动作待添加
        eRet = NvrFixAlgCfgStructToProto(&tPbcSimple);
        if(NVR_ERR__OK != eRet)
        {
            PRINTERR("FixCfgStructToProto failed ret:%d\n", eRet);
            break;
        }

        ///<配置写入配置文件中
        eRet = NVRCfgSetParam(NVR_CFG_SERVER, FIX_ALG_CFG, tPbcSimple.data, tPbcSimple.len);
        if(NVR_ERR__OK != eRet)
        {
            PRINTERR("NVRCfgSetParam failed ret:%d\n", eRet);
            break;
        }

    }while(0);

    ///<释放tPbcSimple中malloc出来的空间
    if(NULL != tPbcSimple.data)
    {
        NVRFREE(tPbcSimple.data);
        tPbcSimple.data = NULL;
    }
    ///<判断被分配的内存是否全部被释放，没全部释放则记录日志
    if(0 != tPbAllocData.dwAllocCount)
    {
        PRINTERR("NveFixCfgSave all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
    }

    return eRet;
}

NVRSTATUS NvrFixDefaultCfgInit(void)
{
	u8 i =0,j = 0,k = 0;
	
	memset(&g_tNvrFixCfg,0,sizeof(g_tNvrFixCfg));
   	NvrFixCfgPtzDefaultInit();

	g_tNvrFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosSize = NVR_PUI_THER_ORIGIN_SIZE;
	g_tNvrFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosType = NVR_PUI_THER_UP_LEFT_POS;

	g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.bEnable = FALSE;
	g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel = 0;
	g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm.byPostCenter = FALSE;
	g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm.byOsdShow = FALSE;
	g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm.byFocus = FALSE;	
	for(i = 0;i < NVR_CAP_THER_MAX_SMART_PLAN_NUM;i++)
	{
		g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].eTherTempFormat = NVR_CAP_THER_TEMP_CELSIUS;
		g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].wPresetId = 0;
		g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].fEmissivity = 0.96;
		g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].dwSensitivity = 50;
		g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].eTherTempType = NVR_CAP_THER_TEMP_TYPE_POINT;
		for(j = 0;j < NVR_CAP_THER_MAX_TYPE_AREA_NUM;j++)
		{
			g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].tArea[j].bEnable = FALSE;
			g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].tArea[j].eTherTempAlarmRule = NVR_CAP_THER_ALARM_RULE_TEMP_GREATER;
			g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].tArea[j].nAlarmTemp = 0;
			g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].tArea[j].byAlarmTol = 0;

			for(k = 0;k < NVR_CAP_THER_MAX_REGION_NUM;k++)
			{
				g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].tArea[j].tRegion[k].wStartX = 0;
				g_tNvrFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[i].tArea[j].tRegion[k].wStartY = 0;
			}
		}
	}
			
	PRINTDBG("default Fix cfg init success\n");
	return NVR_ERR__OK;
}

NVRSTATUS NvrFixAlgDefaultCfgInit(void)
{
    s32 i = 0;
    memset(&g_tnvrFixProIntelCfg,0,sizeof(g_tnvrFixProIntelCfg));

    for (i = 0; i < NVR_MAX_LCAM_CHN_NUM; i++)
    {
        g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wSensity = 50;
        g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.eDuCnfirm = NVR_CAP_SMOKE_FIRE_MODE_CLOSE;
        g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.eTargetWaitMode = NVR_CAP_SMOKE_FIRE_TAGET_WAIT_AUTO;
        g_tnvrFixProIntelCfg.atSmokefire[i].tOtherInfo.wTargetWaitTime = 10;
    }
    PRINTDBG("default Fix alg cfg init success\n");
    return NVR_ERR__OK;
}

NVRSTATUS NvrFixCfgInit()
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 dwCfgBuflen = 0;
	u8	*pbyCfgBuf = NULL;					///<获取配置时保存配置数据的buf
	
	TPbFixCfg *ptPbNvrFixCfg = NULL;
	ENvrCfgRecoveryType eRecoveryType = NVR_CFG_RESET_NO;	///<配置恢复类型

	ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;			///<分配器
	TNvrPbAllocData tPbAllocData;				///<分配器上下文
	mzero(tPbAlocator);
	mzero(tPbAllocData);


    if(!OsApi_SemBCreate(&g_hNvrFixCfgRWSem))
    {
        PRINTERR("OsApi_SemBCreate Fix cfg sem failed \n");
        return NVR_ERR__ERROR;
    }

    mzero(g_tNvrFixCfg);

	///<内存分配器初始化
	tPbcSimple.allocator = &tPbAlocator;
	NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

	NvrSysGetRecoveryFlag(&eRecoveryType, NVR_EVENT_CFG_RESET);

	///<初始化告警基本配置参数，获取失败则创建默认配置
	eRet = NVRCfgGetParamLen(NVR_CFG_SERVER, FIX_CFG, &dwCfgBuflen);
	if(NVR_ERR__OK == eRet && NVR_CFG_RESET_NO == eRecoveryType)
	{
		PRINTERR("get unpack size :%u \n", dwCfgBuflen);

		pbyCfgBuf = (u8 *)NVRALLOC(dwCfgBuflen);
		if(NULL == pbyCfgBuf)
		{
			PRINTERR("malloc cfgbuf failed\n");
			return NVR_ERR__MALLOC_FAILED;
		}

		eRet = NVRCfgGetParam(NVR_CFG_SERVER, FIX_CFG, pbyCfgBuf);
		if(NVR_ERR__OK == eRet)
		{
			
			PRINTERR("NVRCfgGetParam get cfg succ\n");

			ptPbNvrFixCfg = tpb_fix_cfg__unpack(tPbcSimple.allocator, dwCfgBuflen, pbyCfgBuf);			///<反序列化动作

			if (NULL != ptPbNvrFixCfg)
			{
				NvrFixCfgProtoToStruct(ptPbNvrFixCfg);

				tpb_fix_cfg__free_unpacked(ptPbNvrFixCfg, tPbcSimple.allocator); ///<释放ptPbNvrFixCfg
			}
			else
			{
				PRINTERR("Fix cfg unpack failed, use default cfg!!!\n");
				NvrFixDefaultCfgInit();
				
				eRet = NvrFixCfgSave();
				if(NVR_ERR__OK != eRet)
				{
					PRINTERR("save cfg failed ret:%d\n", eRet);
					return eRet;
				}		
				///<清除花样扫描记录
			   NvrSystem("rm -rf /usr/config/syncscan.dat");
			   ///<清除断电记忆位置
			   NvrSystem("rm -rf /usr/config/poweroffrsm.dat");
			}
		}
		else
		{
			PRINTERR("NVRCfgGetParam get %s failed\n", FIX_CFG);
		}

		///<释放pbyCfgBuf空间
		if(pbyCfgBuf != NULL)
		{
		   NVRFREE(pbyCfgBuf);
		   pbyCfgBuf = NULL;
		}

	}
	else
	{
		PRINTERR("create default cfg\n");
		
		///<获取配置失败，创建默认配置
		eRet = NvrFixDefaultCfgInit();
		if(NVR_ERR__OK != eRet)
		{
			PRINTERR("default cfg init failed ret:%d\n", eRet);
			return eRet;
		}		

		///<默认配置写入配置文件中
		eRet = NvrFixCfgSave();
		if(NVR_ERR__OK != eRet)
		{
			PRINTERR("save cfg failed ret:%d\n", eRet);
			return eRet;
		}		

	}

	PRINTERR("Fix init cfg success\n");

	eRet = NvrFixAlgCfgInit();

	return eRet;
}

NVRSTATUS NvrFixAlgCfgInit()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwCfgBuflen = 0;
    u8  *pbyCfgBuf = NULL;                  ///<获取配置时保存配置数据的buf

    TPbFixProfeIntelCfg *ptPbNvrFixAlgCfg = NULL;
    ENvrCfgRecoveryType eRecoveryType = NVR_CFG_RESET_NO;   ///<配置恢复类型

    ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
    ProtobufCAllocator  tPbAlocator;            ///<分配器
    TNvrPbAllocData tPbAllocData;               ///<分配器上下文
    mzero(tPbAlocator);
    mzero(tPbAllocData);

    if(!OsApi_SemBCreate(&g_hNvrFixAlgCfgRWSem))
    {
        PRINTERR("g_hNvrFixAlgCfgRWSem Fix cfg sem failed \n");
        return NVR_ERR__ERROR;
    }

    ///<内存分配器初始化
    tPbcSimple.allocator = &tPbAlocator;
    NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

    NvrSysGetRecoveryFlag(&eRecoveryType, NVR_EVENT_CFG_RESET);

    ///< 专业智能配置开始
    mzero(g_tnvrFixProIntelCfg);

    ///<内存分配器初始化
    tPbcSimple.allocator = &tPbAlocator;
    NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

    PRINTERR("NVRCfgGetParam do fix alg cfg init\n");
    do
    {
        BOOL bCreateDefault = 0;
        if (NVR_CFG_RESET_NO == eRecoveryType)
        {
            eRet = NVRCfgGetParamLen(NVR_CFG_SERVER, FIX_ALG_CFG, &dwCfgBuflen);
            if(NVR_ERR__OK == eRet)
            {
                PRINTERR("get fix alg unpack size :%u \n", dwCfgBuflen);
                pbyCfgBuf = (u8 *)NVRALLOC(dwCfgBuflen);
                if(NULL == pbyCfgBuf)
                {
                    PRINTERR("malloc cfgbuf failed\n");
                    break;
                }

                eRet = NVRCfgGetParam(NVR_CFG_SERVER, FIX_ALG_CFG, pbyCfgBuf);
                if(NVR_ERR__OK == eRet)
                {
                    PRINTERR("NVRCfgGetParam get fix alg cfg succ\n");
                    ptPbNvrFixAlgCfg = tpb_fix_profe_intel_cfg__unpack(tPbcSimple.allocator, dwCfgBuflen, pbyCfgBuf);          ///<反序列化动作
                    if (NULL != ptPbNvrFixAlgCfg)
                    {
                        NvrFixAlgCfgProtoToStruct(ptPbNvrFixAlgCfg);
                        tpb_fix_profe_intel_cfg__free_unpacked(ptPbNvrFixAlgCfg, tPbcSimple.allocator); ///<释放ptPbNvrFixCfg
                    }
                    else
                    {
                        bCreateDefault = TRUE;
                    }
                }
                else
                {
                    bCreateDefault = TRUE;
                }

                ///<释放pbyCfgBuf空间
                if(pbyCfgBuf != NULL)
                {
                   NVRFREE(pbyCfgBuf);
                   pbyCfgBuf = NULL;
                }
            }
        }
        else
        {
            bCreateDefault = TRUE;
        }

        if (bCreateDefault)
        {
            PRINTERR("create default fix alg cfg\n");

            ///<获取配置失败，创建默认配置
            eRet = NvrFixAlgDefaultCfgInit();
            if(NVR_ERR__OK != eRet)
            {
                PRINTERR("default cfg init failed ret:%d\n", eRet);
                return eRet;
            }

            ///<默认配置写入配置文件中
            eRet = NvrFixAlgCfgSave();
            if(NVR_ERR__OK != eRet)
            {
                PRINTERR("save cfg failed ret:%d\n", eRet);
                return eRet;
            }
        }
    } while (0);

    PRINTERR("Fix init alg cfg success\n");

    return eRet;
}


NVRSTATUS NvrFixCfgSetParam(TNvrFixCfg *ptNvrFixCfg)
{
    FIX_ASSERT(ptNvrFixCfg);

    OsApi_SemTake(g_hNvrFixCfgRWSem);

    g_tNvrFixCfg  = *ptNvrFixCfg;

    NvrFixCfgSave();        ///<保存到flash

    OsApi_SemGive(g_hNvrFixCfgRWSem);

    return NVR_ERR__OK;
}

NVRSTATUS NvrFixCfgGetParam(TNvrFixCfg *ptNvrFixCfg)
{
    FIX_ASSERT(ptNvrFixCfg);

    OsApi_SemTake(g_hNvrFixCfgRWSem);

    *ptNvrFixCfg = g_tNvrFixCfg;

    OsApi_SemGive(g_hNvrFixCfgRWSem);

    return NVR_ERR__OK;
}

NVRSTATUS NvrFixCfgGetParamById(u16 wChnId, ENvrFixCfgId eParamId, void *pParam, s32 nSize)
{
    FIX_ASSERT(pParam);

    FIXASSERTSURE(eParamId < NVR_FIX_CFG_MAX)

    NVRSTATUS eRet = NVR_ERR__OK;

    if (eParamId < NVR_FIX_CFG_PROINTEL)
    {
        OsApi_SemTake(g_hNvrFixCfgRWSem);
    }
    else
    {
        OsApi_SemTake(g_hNvrFixAlgCfgRWSem);
    }

    switch (eParamId)
    {
        case NVR_FIX_CFG_ISP:
        {
            FIXASSERTSURE(sizeof(TNvrFixIspCfg) == nSize);

            memcpy(pParam, &g_tNvrFixCfg.tIspCfg[wChnId], sizeof(TNvrFixIspCfg));
            break;
        }

        case NVR_FIX_CFG_MC:
        {
            FIXASSERTSURE(sizeof(TNvrFixMcCfg) == nSize);

            memcpy(pParam, &g_tNvrFixCfg.tMcCfg, sizeof(TNvrFixMcCfg));
            break;
        }

        case NVR_FIX_CFG_PTZ:
        {
            FIXASSERTSURE(sizeof(TNvrCfgPtzCfg) == nSize);

            memcpy(pParam, &g_tNvrFixCfg.tPztCfg, sizeof(TNvrCfgPtzCfg));
            break;
        }

        case NVR_FIX_CFG_THERMEASTEMP:
        {
            FIXASSERTSURE(sizeof(TNvrIntelTherMeasTempParam) == nSize);

            memcpy(pParam, &g_tNvrFixCfg.tIntelCfg.tTherMeasTemp, sizeof(TNvrIntelTherMeasTempParam));
            break;
        }

        case NVR_FIX_CFG_SMOKEFIRE:
        {
            FIXASSERTSURE(sizeof(TNvrIntelSmokeFireCfg) == nSize);

            memcpy(pParam, &g_tnvrFixProIntelCfg.atSmokefire[wChnId], sizeof(TNvrIntelSmokeFireCfg));
            break;
        }

        default:
        {
            PRINTERR("Don't find funcation eParamId: %d!\n",__FUNCTION__, eParamId);
            eRet = NVR_ERR__ERROR;
        }

    }

    if (eParamId < NVR_FIX_CFG_PROINTEL)
    {
        OsApi_SemGive(g_hNvrFixCfgRWSem);
    }
    else
    {
        OsApi_SemGive(g_hNvrFixAlgCfgRWSem);
    }

    return eRet;
}

NVRSTATUS NvrFixCfgSetParamById(u16 wChnId, ENvrFixCfgId eParamId, void *pParam, s32 nSize)
{
    FIX_ASSERT(pParam);
    FIXASSERTSURE(eParamId < NVR_FIX_CFG_MAX)

    NVRSTATUS eRet = NVR_ERR__OK;
    BOOL bNeedSave = FALSE;

    if (eParamId < NVR_FIX_CFG_PROINTEL)
    {
        OsApi_SemTake(g_hNvrFixCfgRWSem);
    }
    else
    {
        OsApi_SemTake(g_hNvrFixAlgCfgRWSem);
    }

    switch (eParamId)
    {
        case NVR_FIX_CFG_ISP:
        {
            FIXASSERTSURE(sizeof(TNvrFixIspCfg) == nSize);

            memcpy(&g_tNvrFixCfg.tIspCfg, pParam, sizeof(TNvrFixIspCfg));
            bNeedSave = TRUE;
            break;
        }

        case NVR_FIX_CFG_MC:
        {
            FIXASSERTSURE(sizeof(TNvrFixMcCfg) == nSize);

            memcpy(&g_tNvrFixCfg.tMcCfg, pParam, sizeof(TNvrFixMcCfg));
            bNeedSave = TRUE;
            break;
        }

        case NVR_FIX_CFG_PTZ:
        {
            FIXASSERTSURE(sizeof(TNvrCfgPtzCfg) == nSize);

            memcpy(&g_tNvrFixCfg.tPztCfg, pParam, sizeof(TNvrCfgPtzCfg));
            bNeedSave = TRUE;
            break;
        }

        case NVR_FIX_CFG_THERMEASTEMP:
        {
            FIXASSERTSURE(sizeof(TNvrIntelTherMeasTempParam) == nSize);

            memcpy(&g_tNvrFixCfg.tIntelCfg.tTherMeasTemp, pParam, sizeof(TNvrIntelTherMeasTempParam));
            bNeedSave = TRUE;
            break;
        }

        case NVR_FIX_CFG_SMOKEFIRE:
        {
            FIXASSERTSURE(sizeof(TNvrIntelSmokeFireCfg) == nSize);

            memcpy(&g_tnvrFixProIntelCfg.atSmokefire[wChnId], pParam, sizeof(TNvrIntelSmokeFireCfg));
            bNeedSave = TRUE;
            break;
        }

        default:
        {
            PRINTERR("Don't find funcation eParamId: %d!\n",__FUNCTION__, eParamId);
            eRet = NVR_ERR__ERROR;
        }
    }

    if (bNeedSave)
    {
        if (eParamId < NVR_FIX_CFG_PROINTEL)
        {
            NvrFixCfgSave();        ///<保存到flash
        }

        if (eParamId > NVR_FIX_CFG_PROINTEL)
        {
            NvrFixAlgCfgSave();     ///<保存到flash
        }
    }

    PRINTDBG("set cfg over \n");
    if (eParamId < NVR_FIX_CFG_PROINTEL)
    {
        OsApi_SemGive(g_hNvrFixCfgRWSem);
    }
    else
    {
        OsApi_SemGive(g_hNvrFixAlgCfgRWSem);
    }

    return eRet;
}

 NVRSTATUS NvrFixCfgGetParamByFlag(void *pRetValue,u32 dwSize, ENvrGetOrSetParamFlag eFunFlag)
 {
	 NVRSTATUS eRet = NVR_ERR__OK; 
	 ASSERTSURE(pRetValue)
	 ASSERTSURE(eFunFlag < NVR_PTZ_MAX_FLAG)
 
	 OsApi_SemTake( g_hNvrFixCfgRWSem );	
	 
	 switch(eFunFlag)
	 {

		 case NVR_PTZ_MODULE_CTRL_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm))
				 {
					memcpy((TNvrPtzCtrlPrm*)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm), dwSize);
				 }
		 
			 }break;

		 case NVR_PTZ_MODULE_STATE_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tIpcPtzState))
				 {
					 memcpy((TNvrPtzBasicState*)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].tIpcPtzState), dwSize);
				 }
		 
			 }break;
		 case NVR_PTZ_MODULE_POWE_RSM_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg))
				 {
					 memcpy((TNvrPowerOffRsmCfg*)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg), dwSize);
				 }
		 
			 }break;
		 case NVR_PTZ_MODULE_PRESET_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].atPresetInfo))
				 {
					 memcpy((TNvrPresetInfo*)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].atPresetInfo), NVR_PUI_PRESET_MAX_NUM*sizeof(TNvrPresetInfo));
				 }
		 
			 }break;

		 case NVR_PTZ_MODULE_PATH_CRS_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo))
				 {
					 memcpy((TNvrPathCrsInfo*)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo), NVR_MAX_PATHCRUISE_NUM*sizeof(TNvrPathCrsInfo));
				 }
		 
			 }break;
		case NVR_PTZ_MODULE_WATCH_ON_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tWatchOn))
				 {
					 memcpy((TNvrWatchOnParam*)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].tWatchOn), dwSize);
				 }
		 
			 }break;
		case NVR_PTZ_MODULE_TIMING_TASK_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tTimeTask))
				 {
					 memcpy((TNvrTmingTaskParam*)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].tTimeTask), dwSize);
				 }
		 
			 }break;

		case NVR_PTZ_MODULE_COOR_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tCooParam))
				 {
					 memcpy((TNvrCooParam *)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].tCooParam), dwSize);
				 }
		 
			 }break;
		case NVR_PTZ_MODULE_AUTOWIPER_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tAutoWiper))
				 {
					 memcpy((TNvrCapDefLcamMcAutoWiper *)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].tAutoWiper), dwSize);
				 }
		 
			 }break;
		 case NVR_PTZ_MODULE_DEMIST_PARAM:
		 	{
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tDeMistCfg))
				 {
					 memcpy((TNvrIspDeMistCfg *)pRetValue, &(g_tNvrFixCfg.tPztCfg[0].tDeMistCfg), dwSize);
				 }
		 
			 }break;
		 default:
			 PRINTERR("[CONFIG][%s]Don't find funcation flag: %d!\n",__FUNCTION__, eFunFlag);
			 eRet = NVR_ERR__ERROR;
			 break;
	 }
	 OsApi_SemGive( g_hNvrFixCfgRWSem ); //释放锁
	 return eRet;
 }
 
 NVRSTATUS NvrFixCfgSetParamByFlag(const void *pSetValue ,u32 dwSize, ENvrGetOrSetParamFlag eFunFlag)
 {
	 NVRSTATUS eRet = NVR_ERR__OK;
	 ASSERTSURE(pSetValue)
	 ASSERTSURE(eFunFlag < NVR_PTZ_MAX_FLAG)
 
	 OsApi_SemTake( g_hNvrFixCfgRWSem );
	 switch(eFunFlag)
	 {	
		case NVR_PTZ_MODULE_CTRL_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm))
				 {
					 memcpy(&(g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm),(TNvrPtzCtrlPrm*)pSetValue, dwSize);
				 }
		 		 
				 NvrFixCfgSave();
			 }break;

		case NVR_PTZ_MODULE_STATE_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tIpcPtzState))
				 {
					 memcpy(&(g_tNvrFixCfg.tPztCfg[0].tIpcPtzState),(TNvrPtzBasicState*)pSetValue,dwSize);
				 }
				 NvrFixCfgSave();
		 
			 }break;
		case NVR_PTZ_MODULE_POWE_RSM_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg))
				 {
					 memcpy(&(g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg),(TNvrPowerOffRsmCfg*)pSetValue,  dwSize);
					
					 NvrFixCfgSave();
				 }
		 
			 }break;
		 case NVR_PTZ_MODULE_PRESET_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].atPresetInfo))
				 {
					 memcpy(&(g_tNvrFixCfg.tPztCfg[0].atPresetInfo),(TNvrPresetInfo*)pSetValue,  NVR_PUI_PRESET_MAX_NUM*sizeof(TNvrPresetInfo));
					
					NvrFixCfgSave();
				 }
		 
			 }break;

		 case NVR_PTZ_MODULE_PATH_CRS_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo))
				 {
					 memcpy(&(g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo),(TNvrPathCrsInfo*)pSetValue,  NVR_MAX_PATHCRUISE_NUM*sizeof(TNvrPathCrsInfo));
					NvrFixCfgSave();
				 }
		 
			 }break;
		case NVR_PTZ_MODULE_WATCH_ON_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tWatchOn))
				 {
					 memcpy( &(g_tNvrFixCfg.tPztCfg[0].tWatchOn),(TNvrWatchOnParam*)pSetValue, dwSize);
					 NvrFixCfgSave();
				 }
		 
			 }break;
		case NVR_PTZ_MODULE_TIMING_TASK_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tTimeTask))
				 {
					 memcpy(&(g_tNvrFixCfg.tPztCfg[0].tTimeTask),(TNvrTmingTaskParam*)pSetValue,  dwSize);
					 NvrFixCfgSave();
				 }
		 
			 }break;

		case NVR_PTZ_MODULE_COOR_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tCooParam))
				 {
					 memcpy(&(g_tNvrFixCfg.tPztCfg[0].tCooParam),(TNvrCooParam *)pSetValue,  dwSize);
					 NvrFixCfgSave();
				 }
			 }break;
		case NVR_PTZ_MODULE_DEMIST_PARAM:
			 {
				 if(dwSize == sizeof(g_tNvrFixCfg.tPztCfg[0].tDeMistCfg))
				 {
					 memcpy(&(g_tNvrFixCfg.tPztCfg[0].tDeMistCfg),(TNvrIspDeMistCfg *)pSetValue,  dwSize);
					 NvrFixCfgSave();
				 }
			 }break;
		default:
			PRINTERR("[CONFIG][%s]Don't find funcation flag!\n",__FUNCTION__);
			eRet = NVR_ERR__ERROR;
			break;
		
	}
    OsApi_SemGive( g_hNvrFixCfgRWSem );
	if(NVR_PTZ_MODULE_TIMING_TASK_PARAM == eFunFlag)
	{
		 ///<对布防时间进行转换
		NvrFixDevTcConvertPtzTime();
	}
	return eRet;
	
 }
 

 NVRSTATUS NvrFixCfgGetPathCrsInfo(u8 byPathNum, TNvrPathCrsInfo *ptPathCrsInfo)
 {
 	if(byPathNum >= NVR_MAX_PATHCRUISE_NUM)
 	{
		PRINTERR("NvrPtzCfgGetPathCrsInfo byPathNum: %d is over range\n", byPathNum);
		return NVR_ERR__ERROR;
 	}

	memcpy(ptPathCrsInfo, &(g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[byPathNum]), sizeof(TNvrPathCrsInfo));

	return NVR_ERR__OK;
 }

NVRSTATUS NvrFixCfgSetPathCrsInfo(u8 byPathNum, const TNvrPathCrsInfo *ptPathCrsInfo)
{
    if(byPathNum >= NVR_MAX_PATHCRUISE_NUM)
    {
        PRINTERR("NvrPtzCfgSetPathCrsInfo byPathNum: %d is over range\n", byPathNum);
        return NVR_ERR__ERROR;
    }

    ASSERTSURE(ptPathCrsInfo)
    memcpy(&(g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[byPathNum]), ptPathCrsInfo, sizeof(TNvrPathCrsInfo)); 
    return NVR_ERR__OK;
}

///<根据语言类型转换特殊预置位的名称，中文情况下不需要处理直接返回中文字符串（配置文件中保存的是中文名称），非中文返回英文字符串
NVRSTATUS NvrFixCfgSpecPreAliasConvBylan(u16 wPresetId, TNvrPresetInfo *ptPresetInfo, ENvrLanguageType eLanguageType)
{			
	TNvrCapFixInernalCapInfo tInterCapParam;
	
	mzero(tInterCapParam);
	NvrCapGetFixInterCapParam(&tInterCapParam);

	//支持特殊预置位则做预置位名称转换
	if(tInterCapParam.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.bySpecialPreset)
	{
		if ((NVR_PTZ_SPECIAL_PRESET_AUTOFLIP == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_RESET == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_CSCAN1 == wPresetId)
			|| (NVR_PTZ_SPECIAL_PRESET_CSCAN2 == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_CSCAN3 == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_CSCAN4 == wPresetId)
			|| (NVR_PTZ_SPECIAL_PRESET_DAYMODE == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_NIGHTMODE == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_PSCAN1 == wPresetId)
			|| (NVR_PTZ_SPECIAL_PRESET_PSCAN2 == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_PSCAN3 == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_PSCAN4 == wPresetId)
			///<TODO，特殊预置位限位设置待处理，主菜单功能也暂未实现
			//|| (NVR_PTZ_SPECIAL_PRESET_OTCRUISE == wIndex) || (NVR_PTZ_SPECIAL_PRESET_AUTODN == wIndex) || (NVR_PTZ_SPECIAL_PRESET_SETML == wIndex)
			//|| (NVR_PTZ_SPECIAL_PRESET_CONFIRMML == wIndex) || (NVR_PTZ_SPECIAL_PRESET_RREBOOT == wIndex) || (NVR_PTZ_SPECIAL_PRESET_MANU == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_OTCRUISE == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_AUTODN == wPresetId)
			|| (NVR_PTZ_SPECIAL_PRESET_RREBOOT == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_MANU == wPresetId)
			|| (NVR_PTZ_SPECIAL_PRESET_STOPSCAN == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_RANDOMSCAN == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_FRAMESCAN == wPresetId)
			|| (NVR_PTZ_SPECIAL_PRESET_AUTOSCAN == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_VERTICALSCAN == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_OVERALLSCAN == wPresetId)
			|| (NVR_PTZ_SPECIAL_PRESET_CSCAN5 == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_CSCAN6 == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_CSCAN7 == wPresetId)
			|| (NVR_PTZ_SPECIAL_PRESET_CSCAN8 == wPresetId) || (NVR_PTZ_SPECIAL_PRESET_WIPER == wPresetId && tInterCapParam.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byWipeSupport == NVR_CAP_SUPPORT))
		{
			switch(eLanguageType)
			{
				case NVR_LANGUAGE_CHN:
				{
					///<中文直接用配置文件中的名称即可，配置文件中保存的是中文名称
					break;
				}
				///<非中文情况下，暂时都返回英文
				case NVR_LANGUAGE_ENG:
				case NVR_LANGUAGE_TRADITIONAL_CHN:
				case NVR_LANGUAGE_KOREAN:
				case NVR_LANGUAGE_TURKEY:
				case NVR_LANGUAGE_THAILAND:
				case NVR_LANGUAGE_HEBREW:
				case NVR_LANGUAGE_CZECH:
				case NVR_LANGUAGE_SPAIN:
				case NVR_LANGUAGE_RUSSIAN:
				case NVR_LANGUAGE_ARABIC:
				case NVR_LANGUAGE_PORTUGUESE:	
				default:
				{	
					memset(ptPresetInfo->abyAlias, 0, sizeof(ptPresetInfo->abyAlias));
					if (NVR_PTZ_SPECIAL_PRESET_AUTOFLIP == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Auto Flip");
					else if (NVR_PTZ_SPECIAL_PRESET_RESET == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Return to Zero Position");
					else if (NVR_PTZ_SPECIAL_PRESET_CSCAN1 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Patrol Scanning 1");
					else if (NVR_PTZ_SPECIAL_PRESET_CSCAN2 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Patrol Scanning 2");
					else if (NVR_PTZ_SPECIAL_PRESET_CSCAN3 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Patrol Scanning 3");
					else if (NVR_PTZ_SPECIAL_PRESET_CSCAN4 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Patrol Scanning 4");
					else if (NVR_PTZ_SPECIAL_PRESET_DAYMODE == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Day Mode");
					else if (NVR_PTZ_SPECIAL_PRESET_NIGHTMODE == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Night Mode");
					else if (NVR_PTZ_SPECIAL_PRESET_PSCAN1 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Mixed Scanning 1");
					else if (NVR_PTZ_SPECIAL_PRESET_PSCAN2 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Mixed Scanning 2");
					else if (NVR_PTZ_SPECIAL_PRESET_PSCAN3 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Mixed Scanning 3");
					else if (NVR_PTZ_SPECIAL_PRESET_PSCAN4 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Mixed Scanning 4");
					else if (NVR_PTZ_SPECIAL_PRESET_OTCRUISE == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Mixed Scanning 1");
					else if (NVR_PTZ_SPECIAL_PRESET_AUTODN == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Auto Night Cut");
#if 0		///<TODO，特殊预置位限位设置待处理	
					else if (NVR_PTZ_SPECIAL_PRESET_SETML == wIndex)
						snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "设置手动限位");
					else if (NVR_PTZ_SPECIAL_PRESET_CONFIRMML == wIndex)
						snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "确认手动限位");
#endif			
					else if (NVR_PTZ_SPECIAL_PRESET_RREBOOT == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Remote Reboot");
					else if (NVR_PTZ_SPECIAL_PRESET_MANU == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Main Menu");
					else if (NVR_PTZ_SPECIAL_PRESET_STOPSCAN == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Stop Scanning");
					else if (NVR_PTZ_SPECIAL_PRESET_RANDOMSCAN == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Random Scanning");
					else if (NVR_PTZ_SPECIAL_PRESET_FRAMESCAN == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Frame Scanning");
					else if (NVR_PTZ_SPECIAL_PRESET_AUTOSCAN == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Auto Scanning");
					else if (NVR_PTZ_SPECIAL_PRESET_VERTICALSCAN == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Tilt Scanning");
					else if (NVR_PTZ_SPECIAL_PRESET_OVERALLSCAN == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Panoramic Scanning");
					else if (NVR_PTZ_SPECIAL_PRESET_CSCAN5 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Patrol Scanning 5");
					else if (NVR_PTZ_SPECIAL_PRESET_CSCAN6 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Patrol Scanning 6");
					else if (NVR_PTZ_SPECIAL_PRESET_CSCAN7 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Patrol Scanning 7");
					else if (NVR_PTZ_SPECIAL_PRESET_CSCAN8 == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Patrol Scanning 8");
					else if (NVR_PTZ_SPECIAL_PRESET_WIPER == wPresetId)
						snprintf((s8*)ptPresetInfo->abyAlias, sizeof(ptPresetInfo->abyAlias), "Wiper");
					break;
				}	
			}
			PRINTDBG("NvrPtzCfgPreAliasConvBylan, eLanguageType: %d, ptPresetInfo->abyAlias: %s\n", eLanguageType, ptPresetInfo->abyAlias);
		}
	}
	else
	{
		PRINTDBG("NvrPtzCfgPreAliasConvBylan, do not support special preset.\n");
		return NVR_ERR__ERROR;
	}

	return NVR_ERR__OK;	
}

NVRSTATUS NvrFixCfgGetPresetInfo(u16 wPresetId, TNvrPresetInfo *ptPresetInfo)
{
	 NVRSTATUS eRet = NVR_ERR__OK;
	 TNvrCapFixInernalCapInfo tInterCapParam;
	 mzero(tInterCapParam);
	 
     eRet = NvrCapGetFixInterCapParam(&tInterCapParam);
     if(NVR_ERR__OK != eRet)
     {
	    PRINTERR("NvrFixCfgGetPresetInfo get Intercap failed, ret:%d\n",eRet);
	    return eRet;
     }

    ASSERTSURE(ptPresetInfo)
    ASSERTSURE(wPresetId < tInterCapParam.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPresetNum.dwMaxValue);
	memcpy(ptPresetInfo,(g_tNvrFixCfg.tPztCfg[0].atPresetInfo+wPresetId),sizeof(TNvrPresetInfo));
    return NVR_ERR__OK;
}
NVRSTATUS NvrFixCfgSetPresetInfo(u16 wPresetId, const TNvrPresetInfo *ptPresetInfo)
{
	TNvrPresetInfo atPresetInfo[NVR_PUI_PRESET_MAX_NUM] = {0};
				
	NvrFixCfgGetParamByFlag(atPresetInfo, NVR_PUI_PRESET_MAX_NUM*sizeof(TNvrPresetInfo), NVR_PTZ_MODULE_PRESET_PARAM);
	memcpy(&atPresetInfo[wPresetId], ptPresetInfo, sizeof(TNvrPresetInfo));	
	NvrFixCfgSetParamByFlag(atPresetInfo, NVR_PUI_PRESET_MAX_NUM*sizeof(TNvrPresetInfo), NVR_PTZ_MODULE_PRESET_PARAM);

    return NVR_ERR__OK;
}



void NvrFixCfgGeneralPtzPresetDefCfg(u16 wIndex, TNvrPresetInfo *ptPresetCfg, BOOL32 bAllDef)
{
	TNvrCapFixInernalCapInfo tInterCapParam;
	u8 abyTmpBuf[NVR_PUI_PRESET_NAME_BUF_LEN] = {0};
	u32 dwTmpLen = NVR_PUI_PRESET_NAME_BUF_LEN;

	 mzero(tInterCapParam);
	 NvrCapGetFixInterCapParam(&tInterCapParam);

	if (bAllDef)
	{
		ptPresetCfg->nZoomPos = 0;
		ptPresetCfg->nFoucsPos = 0;
		ptPresetCfg->wHPos = 0;
		ptPresetCfg->wVPos = 0;
		ptPresetCfg->dwSpeed = 0;
		ptPresetCfg->dwAliasLen = sizeof(ptPresetCfg->abyAlias);//用于UTF-8转UNICODE临时缓存IN(缓存长度)/OUT(转换后别名长度)
		ptPresetCfg->bIsSet = FALSE;
		ptPresetCfg->bSpecial = FALSE;
		snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "preset-%u", wIndex+1);
	}

	//支持特殊预置位则做预置位名称转换
	if(tInterCapParam.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.bySpecialPreset)
	{
		if ((NVR_PTZ_SPECIAL_PRESET_AUTOFLIP == wIndex) || (NVR_PTZ_SPECIAL_PRESET_RESET == wIndex) || (NVR_PTZ_SPECIAL_PRESET_CSCAN1 == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_CSCAN2 == wIndex) || (NVR_PTZ_SPECIAL_PRESET_CSCAN3 == wIndex) || (NVR_PTZ_SPECIAL_PRESET_CSCAN4 == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_DAYMODE == wIndex) || (NVR_PTZ_SPECIAL_PRESET_NIGHTMODE == wIndex) || (NVR_PTZ_SPECIAL_PRESET_PSCAN1 == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_PSCAN2 == wIndex) || (NVR_PTZ_SPECIAL_PRESET_PSCAN3 == wIndex) || (NVR_PTZ_SPECIAL_PRESET_PSCAN4 == wIndex)
			///<TODO，特殊预置位限位设置待处理，主菜单功能也暂未实现
			//|| (NVR_PTZ_SPECIAL_PRESET_OTCRUISE == wIndex) || (NVR_PTZ_SPECIAL_PRESET_AUTODN == wIndex) || (NVR_PTZ_SPECIAL_PRESET_SETML == wIndex)
			//|| (NVR_PTZ_SPECIAL_PRESET_CONFIRMML == wIndex) || (NVR_PTZ_SPECIAL_PRESET_RREBOOT == wIndex) || (NVR_PTZ_SPECIAL_PRESET_MANU == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_OTCRUISE == wIndex) || (NVR_PTZ_SPECIAL_PRESET_AUTODN == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_RREBOOT == wIndex) || (NVR_PTZ_SPECIAL_PRESET_MANU == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_STOPSCAN == wIndex) || (NVR_PTZ_SPECIAL_PRESET_RANDOMSCAN == wIndex) || (NVR_PTZ_SPECIAL_PRESET_FRAMESCAN == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_AUTOSCAN == wIndex) || (NVR_PTZ_SPECIAL_PRESET_VERTICALSCAN == wIndex) || (NVR_PTZ_SPECIAL_PRESET_OVERALLSCAN == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_CSCAN5 == wIndex) || (NVR_PTZ_SPECIAL_PRESET_CSCAN6 == wIndex) || (NVR_PTZ_SPECIAL_PRESET_CSCAN7 == wIndex)
			|| (NVR_PTZ_SPECIAL_PRESET_CSCAN8 == wIndex) || (NVR_PTZ_SPECIAL_PRESET_WIPER == wIndex && tInterCapParam.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byWipeSupport == NVR_CAP_SUPPORT))
		{
			ptPresetCfg->bIsSet = TRUE;
			ptPresetCfg->bSpecial = TRUE;

#ifdef __I18N__
            if (NVR_PTZ_SPECIAL_PRESET_AUTOFLIP == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "AutoFlip");
            else if (NVR_PTZ_SPECIAL_PRESET_RESET == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "BackZero");
            else if (NVR_PTZ_SPECIAL_PRESET_CSCAN1 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "CruiseScan1");
            else if (NVR_PTZ_SPECIAL_PRESET_CSCAN2 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "CruiseScan2");
            else if (NVR_PTZ_SPECIAL_PRESET_CSCAN3 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "CruiseScan3");
            else if (NVR_PTZ_SPECIAL_PRESET_CSCAN4 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "CruiseScan4");
            else if (NVR_PTZ_SPECIAL_PRESET_DAYMODE == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "DayMode");
            else if (NVR_PTZ_SPECIAL_PRESET_NIGHTMODE == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "NightMode");
            else if (NVR_PTZ_SPECIAL_PRESET_PSCAN1 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "PatternScan1");
            else if (NVR_PTZ_SPECIAL_PRESET_PSCAN2 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "PatternScan2");
            else if (NVR_PTZ_SPECIAL_PRESET_PSCAN3 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "PatternScan3");
            else if (NVR_PTZ_SPECIAL_PRESET_PSCAN4 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "PatternScan4");
            else if (NVR_PTZ_SPECIAL_PRESET_OTCRUISE == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "PresetCruis");
            else if (NVR_PTZ_SPECIAL_PRESET_AUTODN == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "DNAutoMode");
#if 0       ///<TODO，特殊预置位限位设置待处理
            else if (NVR_PTZ_SPECIAL_PRESET_SETML == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "SetManualLimit");
            else if (NVR_PTZ_SPECIAL_PRESET_CONFIRMML == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "ConfirmManualLimit");
#endif
            else if (NVR_PTZ_SPECIAL_PRESET_RREBOOT == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "RemoteRestart");
            else if (NVR_PTZ_SPECIAL_PRESET_MANU == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "MainMenu");
            else if (NVR_PTZ_SPECIAL_PRESET_STOPSCAN == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "StopScan");
            else if (NVR_PTZ_SPECIAL_PRESET_RANDOMSCAN == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "RandomScan");
            else if (NVR_PTZ_SPECIAL_PRESET_FRAMESCAN == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "FrameScan");
            else if (NVR_PTZ_SPECIAL_PRESET_AUTOSCAN == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "HorizontalScan");
            else if (NVR_PTZ_SPECIAL_PRESET_VERTICALSCAN == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "VerticalScan");
            else if (NVR_PTZ_SPECIAL_PRESET_OVERALLSCAN == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "PanoramicScan");
            else if (NVR_PTZ_SPECIAL_PRESET_CSCAN5 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "CruiseScan5");
            else if (NVR_PTZ_SPECIAL_PRESET_CSCAN6 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "CruiseScan6");
            else if (NVR_PTZ_SPECIAL_PRESET_CSCAN7 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "CruiseScan7");
            else if (NVR_PTZ_SPECIAL_PRESET_CSCAN8 == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "CruiseScan8");
            else if (NVR_PTZ_SPECIAL_PRESET_WIPER == wIndex)
                snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "Wiper");

#else
			if (NVR_PTZ_SPECIAL_PRESET_AUTOFLIP == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "自动翻转");
			else if (NVR_PTZ_SPECIAL_PRESET_RESET == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "回到零点");
			else if (NVR_PTZ_SPECIAL_PRESET_CSCAN1 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "巡航扫描1");
			else if (NVR_PTZ_SPECIAL_PRESET_CSCAN2 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "巡航扫描2");
			else if (NVR_PTZ_SPECIAL_PRESET_CSCAN3 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "巡航扫描3");
			else if (NVR_PTZ_SPECIAL_PRESET_CSCAN4 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "巡航扫描4");
			else if (NVR_PTZ_SPECIAL_PRESET_DAYMODE == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "白天模式");
			else if (NVR_PTZ_SPECIAL_PRESET_NIGHTMODE == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "黑夜模式");
			else if (NVR_PTZ_SPECIAL_PRESET_PSCAN1 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "花样扫描1");
			else if (NVR_PTZ_SPECIAL_PRESET_PSCAN2 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "花样扫描2");
			else if (NVR_PTZ_SPECIAL_PRESET_PSCAN3 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "花样扫描3");
			else if (NVR_PTZ_SPECIAL_PRESET_PSCAN4 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "花样扫描4");
			else if (NVR_PTZ_SPECIAL_PRESET_OTCRUISE == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "预置位巡航");
			else if (NVR_PTZ_SPECIAL_PRESET_AUTODN == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "日夜自动模式");
#if 0		///<TODO，特殊预置位限位设置待处理	
			else if (NVR_PTZ_SPECIAL_PRESET_SETML == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "设置手动限位");
			else if (NVR_PTZ_SPECIAL_PRESET_CONFIRMML == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "确认手动限位");
#endif			
			else if (NVR_PTZ_SPECIAL_PRESET_RREBOOT == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "远程重启");
			else if (NVR_PTZ_SPECIAL_PRESET_MANU == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "主菜单");
			else if (NVR_PTZ_SPECIAL_PRESET_STOPSCAN == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "停止扫描");
			else if (NVR_PTZ_SPECIAL_PRESET_RANDOMSCAN == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "随机扫描");
			else if (NVR_PTZ_SPECIAL_PRESET_FRAMESCAN == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "帧扫描");
			else if (NVR_PTZ_SPECIAL_PRESET_AUTOSCAN == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "水平扫描");
			else if (NVR_PTZ_SPECIAL_PRESET_VERTICALSCAN == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "垂直扫描");
			else if (NVR_PTZ_SPECIAL_PRESET_OVERALLSCAN == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "全景扫描");
			else if (NVR_PTZ_SPECIAL_PRESET_CSCAN5 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "巡航扫描5");
			else if (NVR_PTZ_SPECIAL_PRESET_CSCAN6 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "巡航扫描6");
			else if (NVR_PTZ_SPECIAL_PRESET_CSCAN7 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "巡航扫描7");
			else if (NVR_PTZ_SPECIAL_PRESET_CSCAN8 == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "巡航扫描8");
			else if (NVR_PTZ_SPECIAL_PRESET_WIPER == wIndex)
				snprintf((s8*)ptPresetCfg->abyAlias, sizeof(ptPresetCfg->abyAlias), "雨刷");
#endif

			CharConvConvertGbktoUnicode((s8*)ptPresetCfg->abyAlias, abyTmpBuf, &dwTmpLen);
			CharConvConvertUnicodetoUtf8(abyTmpBuf, dwTmpLen, (s8*)ptPresetCfg->abyAlias, NVR_PUI_PRESET_NAME_BUF_LEN);
		}
	}
	return;
}
 
 NVRSTATUS NvrFixCfgPtzDefaultInit()
 {
 	 int i = 0, j = 0;
	 TNvrCapFixInernalCapInfo tInterCapParam;

	 mzero(tInterCapParam);
	 NvrCapGetFixInterCapParam(&tInterCapParam);
	 ///<tPtzCtrlPrm
 	 g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm.byAddress = 1;
	 g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm.byProtocalType = NVR_PTZPROTO_TYPE_PELCO_D_LFEB;
	 g_tNvrFixCfg.tPztCfg[0].tPtzCtrlPrm.byExtraAddress = 1;

	 ///<tIpcPtzState	
	 /*因cgi对0是关，1是开，因此球机保持与其对应,统一采用NVR_PTZ_MODE_ON与
	 NVR_PTZ_MODE_CLOSE ,dev ptzdeal时需做对应转换*/
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwAutoFlip = NVR_PTZ_MODE_ON;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDepthrateSpd = NVR_PTZ_MODE_ON;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwScanSpeedValue = 10;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwPreSetSpdValue= 40;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eZoomSpeedValue= NVR_PTZ_ZOOM_SPEED_HIGH;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.ePTOsdMode = NVR_DEV_PT_MODE_NUMBER;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwVidEncFreeze = NVR_PTZ_MODE_CLOSE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPriorityType = NVR_DEV_PRIORITY_NETWORK;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPriDelayTime = 5;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwInfaredState = NVR_PTZ_MODE_CLOSE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eInfaredMode = NVR_INFRARED_AUTO_OPEN;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwInfaredSens = 1;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwInfaredValue = 100;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwNearInfaredValue = 100;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eLaserSwitch = NVR_DEV_LASER_AUTO;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eLaserMode = NVR_DEV_LASER_DEFAULT;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserDist = 0;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserIntensity = 70;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserCentradMode = 50;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwWiperState = NVR_PTZ_MODE_CLOSE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDefrostState = NVR_PTZ_MODE_CLOSE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwPosLimitDisplay = NVR_DEV_POSLIMIT_DISPLAY_V5;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eVerticaRange = NVR_DEV_VERTICA_RANGE_0_90;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLaserCentradSpeed = 50;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDeMistState = NVR_PTZ_MODE_CLOSE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwDeMistTime = 60;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwSLState = NVR_PTZ_MODE_CLOSE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eSLMode = NVR_DEV_SET_SL_MODE_BRIGHTNESS;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwSLValue = 20;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPowerOnMode = NVR_POWERON_MODE_BUTTON;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwLedMainSwitch = NVR_PTZ_MODE_ON;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.eFanDemisterMode = NVR_FAN_DEMISTER_CLOSE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPowerOffMode = NVR_POWEROFF_MODE_BUTTON;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byCarMode = TRUE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byExtWifiSwitch = NVR_PTZ_MODE_CLOSE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.byPtzId = 1;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwManuLimitPos = NVR_PTZ_MODE_CLOSE;
	g_tNvrFixCfg.tPztCfg[0].tIpcPtzState.dwScanLimitPos = NVR_PTZ_MODE_CLOSE;
	///<路径巡航参数
	for (i = 0; i < NVR_MAX_PATHCRUISE_NUM; i++)
	{
		g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].wPresetCnt = 0;			
		for (j = 0; j < tInterCapParam.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPresetNum.dwMaxValue; j++)
		{
			g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[i].tPresetInfo[j].wStaytime = 15;
		}
	}
	///<第一条路径默认1~8号预置位
	g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[0].wPresetCnt = 8;
	for (i = 0; i < 8; i++)
	{
		g_tNvrFixCfg.tPztCfg[0].atPathCrsInfo[0].tPresetInfo[i].wPrenum = i;
	}
	///<tPowerOffRsmCfg   
	g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.bEnable = TRUE;
	g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.eResumeMode = NVR_DEV_POWEROFF_MEMORY;
	g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.adwParam[NVR_DEV_POWEROFF_MEMORY] = 60;
	g_tNvrFixCfg.tPztCfg[0].tPowerOffRsmCfg.adwParam[NVR_DEV_POWEROFF_LOAD_PRESET] = 0;

	///<atPresetInfo   
	for(i = 0; i < tInterCapParam.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPresetNum.dwMaxValue; i++)
	{
		NvrFixCfgGeneralPtzPresetDefCfg(i, &g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i], TRUE);
		
		//snprintf((s8*)g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].abyAlias, NVR_PUI_PRESET_NAME_BUF_LEN, "preset%u", i+1);
		//g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].dwAliasLen = strlen((s8*)g_tNvrFixCfg.tPztCfg[0].atPresetInfo[i].abyAlias);

	}
	///<tWatchOn   
	g_tNvrFixCfg.tPztCfg[0].tWatchOn.bEnable = FALSE;
	g_tNvrFixCfg.tPztCfg[0].tWatchOn.byTaskType = NVR_PTZCTRL_PRESET_LOAD_TASK;
	g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwPresetID = 0;
	g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwPathCruiseID = 0;
	g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwSyncScanID = 0;
	g_tNvrFixCfg.tPztCfg[0].tWatchOn.dwWaitTimeSec = 60;

	///<time task
	g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.bEnable = FALSE;
	g_tNvrFixCfg.tPztCfg[0].tTimeTask.tTmingTaskHead.dwResumeTime = 5;

	///<demist
	g_tNvrFixCfg.tPztCfg[0].tDeMistCfg.eDeMistMode = NVR_ISP_DE_MIST_ENABLE;
	g_tNvrFixCfg.tPztCfg[0].tDeMistCfg.dwDeMistTime = 60;
	return NVR_ERR__OK;
 }




