#!/bin/sh
cd prj_linux
make -f makefile_cgiapp_ubuntu_64_ws_release clean
make -f makefile_cgiapp_ubuntu_64_ws_release 2>&1 |tee ../../../10-common/version/compileinfo/cgiapp_ubuntu_64_ws_release.txt

make -f makefile_cgiapp_ubuntu_64_ws_sanitizer_release clean
make -f makefile_cgiapp_ubuntu_64_ws_sanitizer_release 2>&1 |tee ../../../10-common/version/compileinfo/cgiapp_ubuntu_64_ws_sanitizer_release.txt

cd ../