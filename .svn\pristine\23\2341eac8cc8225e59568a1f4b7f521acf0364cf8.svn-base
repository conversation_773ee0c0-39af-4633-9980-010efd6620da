

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrpcap
ARC_TARGET	      := nvrpcap

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3516DV300_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrpcap\
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include\
		$(CURDIR)/../../common\
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/libpcap\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal\
		$(CURDIR)/../../../10-common/include/service\
		$(CURDIR)/../../../10-common/include/cbb/protobuf
CFLAGS += -D_HIS3516DV300_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/his3516dv300
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


