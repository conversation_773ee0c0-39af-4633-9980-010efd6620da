

TOP := ../

COMM_DIR := ./

SRC_DIR := $(TOP)/source


## Name and type of the target for this Makefile

SO_TARGET      := ipccfgfit

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER= _ARM_HIS3519AV100_
PWLIB_SUPPORT = 0

CFLAGS += -D_LINUX -DBIT_ANDROID -Wall -g -D_IPCQUEUE_

OBJS := $(SRC_DIR)/ipccfgfit_app	\
		$(SRC_DIR)/ipccfgfit_ipdt	\
		$(SRC_DIR)/ipccfgfit_sys	\
		$(SRC_DIR)/ipccfgfit	\



## Libraries to include in shared object file
        

## Add driver-specific include directory to the search path
##ARC_LIBS += 
INC_PATH += ../../../10-common/include/app					\
			../../../10-common/include/system				\
			../../../10-common/include/cbb					\
			../../../10-common/include/cbb/appclt			\
			../../../10-common/include/cbb/cjson			\
			../../../10-common/include/cbb/debuglog			\
			../../../10-common/include/cbb/protobuf			\
			../../../10-common/include/cbb/osp				\
			../../../10-common/include/cbb/kdvsys			\
			../../../10-common/include/cbb/charconversion	\
			../../../10-common/include/cbb/crc_check		\
			../../../10-common/include/service				\
            ../include

INSTALL_LIB_PATH = ../../../10-common/lib/release/his3519av100/ipccfgfit

LIB_PATH := ../../../10-common/lib/release/his3519av100

LIBS := appbase cjson debuglog kdvsys

include $(COMM_DIR)/makelib.mk

