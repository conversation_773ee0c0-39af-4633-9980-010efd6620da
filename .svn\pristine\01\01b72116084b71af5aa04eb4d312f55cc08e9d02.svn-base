#!/bin/bash
#set -x
path="../../../10-common/version/compileinfo/nvrlib_rk3568.txt"
date>>$path

module_name=$(basename $PWD)
proj_dir=prj_linux

echo ==============================================
echo =      ${module_name}_linux for rk3568           =
echo ==============================================

echo "============compile lib$module_name rk3568============">>$path

make -C $proj_dir -e DEBUG=1 -f makefile_rk3568 clean all 2>&1 1>/dev/null |tee -a $path


