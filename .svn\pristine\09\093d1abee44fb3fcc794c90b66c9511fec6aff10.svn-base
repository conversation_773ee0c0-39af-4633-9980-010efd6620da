

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
DO_UPX = 0
LINUX_COMPILER = _AX603A_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
##CFLAGS += -D__MRTC__
CFLAGS += -D_AX603A_
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a/sdklib
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a/kdssl-ext
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a/nvrgnss
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a/capfix
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a/webrtc
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a/wifim
LIBS +=	nvrcfg nvrlog nvrcap nvrftp nvrcustcap nvrusrmgr nvrnetwork nvrsys nvrpcap nvrproto protobufc sqlite3wrapper malloc debuglog netcbb mbnet ddnsc upnpc drv pthread nvrgeo\
	sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu nvrprobe httpclient mxml ghttp go rtspclient kdmposa osp netpacket kdmtsps mediaswitch stdc++ nvrqueue ispctrl mediactrl nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp dl ais algctrl airp gnss wifi kdvsys SDEF pcap pubsecstack curl nvrcoi btctrl nvrunifiedlog\
	kdmfileinterface\
	md5lib_axera_linux smartcodec_axera_linux ax_nt ax_tuning ax_sys ax_interpreter ax_interpreter_external ai_kit_release sns_os04a10 sns_imx485 ax_3a ax_mipi \
    sns_dummy  sns_sc200ai sns_imx586 ax_isp ax_jpegenc ax_sensor_interface ax_mailbox \
    act_ak7374 ax_actuator_interface ax_virt_vo ax_venc ax_vdec ax_ivps dpu drm ax_dsp ax_npu_cv_kit ax_img_algo ax_run_joint ax_dsp_cv sns_os08a20 \
    aaclcdec_axera_linux aaclcenc_axera_linux aaclddec_axera_linux aacldenc_axera_linux \
    aec_mulresample_axera_linux audcodec_axera_linux audproc_axera_linux \
    g711_axera_linux g722_axera_linux g726_axera_linux g7221c_axera_linux stdg722_axera_linux\
    extexp_axera_linux videomanage_axera_linux resample_axera_linux \
    adpcm_axera_linux aaclcdec_axera_linux aaclcenc_axera_linux \
    spe_axera_linux asd_axera_linux mixer_axera_linux g728_axera_linux g729_axera_linux \
    g719_axera_linux mp3dec_axera_linux mp3enc_axera_linux mp2_axera_linux \
    aaclddec_axera_linux aacldenc_axera_linux opus_axera_linux amr_nb_axera_linux \
    asound\
	m\
	kdmssl\
	kdmcrypto\
	cjson\
	udm\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrdynamicplugin\
	nvrstitch\
	lwshelper\
	websockets\
	skf_szgx_3310uk\
	kdssl-ext\
	nvrmd5\
    #heapcheck\
    #tcmalloc
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif




## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/hal/drvlib\
		$(CURDIR)/../../../10-common/include/hal/drvlib/system

#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif

CFLAGS += -g
INSTALL_NO_UPX_APP_PATH := ../../../10-common/version/release/ax603a/bin_noupx
INSTALL_APP_PATH := ../../../10-common/version/release/ax603a/bin_fixipc
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk
