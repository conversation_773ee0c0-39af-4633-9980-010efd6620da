#!/bin/bash
#set -x
path="../../../10-common/version/compileinfo/nvrlib_netra8107.txt"
date>>$path

module_name=$(basename $PWD)
proj_dir=prj_linux

echo ==============================================
echo =      ${module_name}_linux for netra8107           =
echo ==============================================

echo "============compile lib$module_name netra8107============">>$path

make -C $proj_dir -e DEBUG=1 -f makefile_netra8107 clean all 2>&1 1>/dev/null |tee -a $path


