

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _SSC339G_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
CFLAGS += -D__MRTC__
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/kdssl-ext
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/nvrgnss
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/capnvr

LIBS +=	nvrcfg nvrlog nvrcap nvrcustcap nvrusrmgr nvrnetwork nvrsys nvrproto protobufc sqlite3wrapper malloc kdvsys debuglog netcbb mbnet  ddnsc ddnscext upnpc drv pthread nvrgeo nvrcoi \
	sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu  nvrpcap  nvrftp httpclient mxml ghttp go rtspclient umpposa mca osp netpacket umptsps umpencrypt stdc++ nvrqueue mediactrl_nvr nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp umpfileinterface nvrguard nvrsmtp smtp airp dl ais algctrl pubsecstack curl nvrunifiedlog\
	equalizer_ex_sigmastar_linux mixer_sigmastar_linux \
	spe_sigmastar_linux videomanage_sigmastar_linux \
	audproc_plus_sigmastar_linux speechexcit_sigmastar_linux \
	resample_sigmastar_linux extexp_sigmastar_linux \
	multiaec_v202_sigmastar_linux agc_speechsense_sigmastar_linux \
	voicechanger_sigmastar_linux audcodec_sigmastar_linux \
	dlydct_sigmastar_linux g7221c_sigmastar_linux \
	adpcm_sigmastar_linux g711_sigmastar_linux \
	g722_sigmastar_linux aaclcenc_sigmastar_linux \
	aaclcdec_sigmastar_linux g726_sigmastar_linux \
	aaclddec_sigmastar_linux aacldenc_sigmastar_linux \
	amr_nb_sigmastar_linux g719_sigmastar_linux \
	g728_sigmastar_linux g729_sigmastar_linux \
	mp3dec_sigmastar_linux mp3enc_sigmastar_linux \
	opus_sigmastar_linux mp2_sigmastar_linux\
	m \
	pcap\
	ssl\
	crypto\
	cjson\
	udm\
	uuid\
	blkid\
	nvrproduct\
	umpnatagent\
	rpdownload\
    nvrstitch\
	lwshelper\
	websockets\
	nvrmd5\
	kdssl-ext\
	SDEF\
	mediaswitch\
	#nvrdynamicplugin\
	#stdg722_sigmastar_linux\

ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif

# 非车载类设备,使用普通mediaswitch
#LDFLAGS += $(TOP)/../../10-common/lib/release/ssc339g/libmediaswitch.so

## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/hal/drvlib\
		$(CURDIR)/../../../10-common/include/hal/drvlib/system

#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif

INSTALL_APP_PATH := ../../../10-common/version/release/ssc339g/bin_nvr/
INSTALL_NO_UPX_APP_PATH := ../../../10-common/version/release/ssc339g/bin_nvr_noupx

include $(COMM_DIR)/common.mk


