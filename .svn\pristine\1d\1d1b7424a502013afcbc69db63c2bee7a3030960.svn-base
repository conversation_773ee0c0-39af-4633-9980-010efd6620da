/*
 * Note: this file originally auto-generated by mib2c using
 *        $
 */
#ifndef IPCMIB_H
#define IPCMIB_H

/* function declarations */
void init_ipcMIB(void);
Netsnmp_Node_Handler handle_ipcVideoEncoderNumber;
Netsnmp_Node_Handler handle_ipcAudioEncoderNumber;
Netsnmp_Node_Handler handle_ipcPlatformNumber;
Netsnmp_Node_Handler handle_ipcGbPlatformNum;
Netsnmp_Node_Handler handle_ipcConnections;
Netsnmp_Node_Handler handle_ipcSDStatus;
Netsnmp_Node_Handler handle_ipcSysCpuUsage;
Netsnmp_Node_Handler handle_ipcSysResetTime;
Netsnmp_Node_Handler handle_ipcSysRebootTime;
Netsnmp_Node_Handler handle_ipcSysClocksyncTime;

#endif /* IPCMIB_H */
