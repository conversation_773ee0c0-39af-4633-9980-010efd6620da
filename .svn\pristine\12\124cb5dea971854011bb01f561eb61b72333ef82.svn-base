/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrsys.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrsys.pb-c.h"
void   tpb_nvr_sys_param__init
                     (TPbNvrSysParam         *message)
{
  static TPbNvrSysParam init_value = TPB_NVR_SYS_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_param__get_packed_size
                     (const TPbNvrSysParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_param__pack
                     (const TPbNvrSysParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_param__pack_to_buffer
                     (const TPbNvrSysParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysParam *
       tpb_nvr_sys_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_param__free_unpacked
                     (TPbNvrSysParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_vid_res__init
                     (TPbNvrSysVidRes         *message)
{
  static TPbNvrSysVidRes init_value = TPB_NVR_SYS_VID_RES__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_vid_res__get_packed_size
                     (const TPbNvrSysVidRes *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_vid_res__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_vid_res__pack
                     (const TPbNvrSysVidRes *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_vid_res__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_vid_res__pack_to_buffer
                     (const TPbNvrSysVidRes *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_vid_res__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysVidRes *
       tpb_nvr_sys_vid_res__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysVidRes *)
     protobuf_c_message_unpack (&tpb_nvr_sys_vid_res__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_vid_res__free_unpacked
                     (TPbNvrSysVidRes *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_vid_res__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_zero_chn_enc_param__init
                     (TPbNvrSysZeroChnEncParam         *message)
{
  static TPbNvrSysZeroChnEncParam init_value = TPB_NVR_SYS_ZERO_CHN_ENC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_zero_chn_enc_param__get_packed_size
                     (const TPbNvrSysZeroChnEncParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_zero_chn_enc_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_zero_chn_enc_param__pack
                     (const TPbNvrSysZeroChnEncParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_zero_chn_enc_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_zero_chn_enc_param__pack_to_buffer
                     (const TPbNvrSysZeroChnEncParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_zero_chn_enc_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysZeroChnEncParam *
       tpb_nvr_sys_zero_chn_enc_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysZeroChnEncParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_zero_chn_enc_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_zero_chn_enc_param__free_unpacked
                     (TPbNvrSysZeroChnEncParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_zero_chn_enc_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_ntp_param__init
                     (TPbNvrNtpParam         *message)
{
  static TPbNvrNtpParam init_value = TPB_NVR_NTP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_ntp_param__get_packed_size
                     (const TPbNvrNtpParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_ntp_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ntp_param__pack
                     (const TPbNvrNtpParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ntp_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ntp_param__pack_to_buffer
                     (const TPbNvrNtpParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ntp_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNtpParam *
       tpb_nvr_ntp_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNtpParam *)
     protobuf_c_message_unpack (&tpb_nvr_ntp_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ntp_param__free_unpacked
                     (TPbNvrNtpParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ntp_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_time_auto_adapt_param__init
                     (TPbNvrTimeAutoAdaptParam         *message)
{
  static TPbNvrTimeAutoAdaptParam init_value = TPB_NVR_TIME_AUTO_ADAPT_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_time_auto_adapt_param__get_packed_size
                     (const TPbNvrTimeAutoAdaptParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_time_auto_adapt_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_time_auto_adapt_param__pack
                     (const TPbNvrTimeAutoAdaptParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_time_auto_adapt_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_time_auto_adapt_param__pack_to_buffer
                     (const TPbNvrTimeAutoAdaptParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_time_auto_adapt_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrTimeAutoAdaptParam *
       tpb_nvr_time_auto_adapt_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrTimeAutoAdaptParam *)
     protobuf_c_message_unpack (&tpb_nvr_time_auto_adapt_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_time_auto_adapt_param__free_unpacked
                     (TPbNvrTimeAutoAdaptParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_time_auto_adapt_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_ptp_param__init
                     (TPbNvrPtpParam         *message)
{
  static TPbNvrPtpParam init_value = TPB_NVR_PTP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_ptp_param__get_packed_size
                     (const TPbNvrPtpParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_ptp_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ptp_param__pack
                     (const TPbNvrPtpParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ptp_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ptp_param__pack_to_buffer
                     (const TPbNvrPtpParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ptp_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPtpParam *
       tpb_nvr_ptp_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPtpParam *)
     protobuf_c_message_unpack (&tpb_nvr_ptp_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ptp_param__free_unpacked
                     (TPbNvrPtpParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ptp_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_auto_sync_time_param__init
                     (TPbNvrAutoSyncTimeParam         *message)
{
  static TPbNvrAutoSyncTimeParam init_value = TPB_NVR_AUTO_SYNC_TIME_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_auto_sync_time_param__get_packed_size
                     (const TPbNvrAutoSyncTimeParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_auto_sync_time_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_auto_sync_time_param__pack
                     (const TPbNvrAutoSyncTimeParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_auto_sync_time_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_auto_sync_time_param__pack_to_buffer
                     (const TPbNvrAutoSyncTimeParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_auto_sync_time_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAutoSyncTimeParam *
       tpb_nvr_auto_sync_time_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAutoSyncTimeParam *)
     protobuf_c_message_unpack (&tpb_nvr_auto_sync_time_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_auto_sync_time_param__free_unpacked
                     (TPbNvrAutoSyncTimeParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_auto_sync_time_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_summer_time__init
                     (TPbNvrSummerTime         *message)
{
  static TPbNvrSummerTime init_value = TPB_NVR_SUMMER_TIME__INIT;
  *message = init_value;
}
size_t tpb_nvr_summer_time__get_packed_size
                     (const TPbNvrSummerTime *message)
{
  assert(message->base.descriptor == &tpb_nvr_summer_time__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_summer_time__pack
                     (const TPbNvrSummerTime *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_summer_time__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_summer_time__pack_to_buffer
                     (const TPbNvrSummerTime *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_summer_time__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSummerTime *
       tpb_nvr_summer_time__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSummerTime *)
     protobuf_c_message_unpack (&tpb_nvr_summer_time__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_summer_time__free_unpacked
                     (TPbNvrSummerTime *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_summer_time__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_summer_time_param__init
                     (TPbNvrSummerTimeParam         *message)
{
  static TPbNvrSummerTimeParam init_value = TPB_NVR_SUMMER_TIME_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_summer_time_param__get_packed_size
                     (const TPbNvrSummerTimeParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_summer_time_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_summer_time_param__pack
                     (const TPbNvrSummerTimeParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_summer_time_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_summer_time_param__pack_to_buffer
                     (const TPbNvrSummerTimeParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_summer_time_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSummerTimeParam *
       tpb_nvr_summer_time_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSummerTimeParam *)
     protobuf_c_message_unpack (&tpb_nvr_summer_time_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_summer_time_param__free_unpacked
                     (TPbNvrSummerTimeParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_summer_time_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_time_param__init
                     (TPbNvrTimeParam         *message)
{
  static TPbNvrTimeParam init_value = TPB_NVR_TIME_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_time_param__get_packed_size
                     (const TPbNvrTimeParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_time_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_time_param__pack
                     (const TPbNvrTimeParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_time_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_time_param__pack_to_buffer
                     (const TPbNvrTimeParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_time_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrTimeParam *
       tpb_nvr_time_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrTimeParam *)
     protobuf_c_message_unpack (&tpb_nvr_time_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_time_param__free_unpacked
                     (TPbNvrTimeParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_time_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_udp_re_tran_param__init
                     (TPbNvrSysUdpReTranParam         *message)
{
  static TPbNvrSysUdpReTranParam init_value = TPB_NVR_SYS_UDP_RE_TRAN_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_udp_re_tran_param__get_packed_size
                     (const TPbNvrSysUdpReTranParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_udp_re_tran_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_udp_re_tran_param__pack
                     (const TPbNvrSysUdpReTranParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_udp_re_tran_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_udp_re_tran_param__pack_to_buffer
                     (const TPbNvrSysUdpReTranParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_udp_re_tran_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysUdpReTranParam *
       tpb_nvr_sys_udp_re_tran_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysUdpReTranParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_udp_re_tran_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_udp_re_tran_param__free_unpacked
                     (TPbNvrSysUdpReTranParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_udp_re_tran_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_ktcp_param__init
                     (TPbNvrSysKtcpParam         *message)
{
  static TPbNvrSysKtcpParam init_value = TPB_NVR_SYS_KTCP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_ktcp_param__get_packed_size
                     (const TPbNvrSysKtcpParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_ktcp_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_ktcp_param__pack
                     (const TPbNvrSysKtcpParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_ktcp_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_ktcp_param__pack_to_buffer
                     (const TPbNvrSysKtcpParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_ktcp_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysKtcpParam *
       tpb_nvr_sys_ktcp_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysKtcpParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_ktcp_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_ktcp_param__free_unpacked
                     (TPbNvrSysKtcpParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_ktcp_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_mrtc_param__init
                     (TPbNvrSysMrtcParam         *message)
{
  static TPbNvrSysMrtcParam init_value = TPB_NVR_SYS_MRTC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_mrtc_param__get_packed_size
                     (const TPbNvrSysMrtcParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_mrtc_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_mrtc_param__pack
                     (const TPbNvrSysMrtcParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_mrtc_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_mrtc_param__pack_to_buffer
                     (const TPbNvrSysMrtcParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_mrtc_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysMrtcParam *
       tpb_nvr_sys_mrtc_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysMrtcParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_mrtc_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_mrtc_param__free_unpacked
                     (TPbNvrSysMrtcParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_mrtc_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_ping_param__init
                     (TPbNvrSysPingParam         *message)
{
  static TPbNvrSysPingParam init_value = TPB_NVR_SYS_PING_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_ping_param__get_packed_size
                     (const TPbNvrSysPingParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_ping_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_ping_param__pack
                     (const TPbNvrSysPingParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_ping_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_ping_param__pack_to_buffer
                     (const TPbNvrSysPingParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_ping_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysPingParam *
       tpb_nvr_sys_ping_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysPingParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_ping_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_ping_param__free_unpacked
                     (TPbNvrSysPingParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_ping_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_advance_sys_param__init
                     (TPbNvrSysAdvanceSysParam         *message)
{
  static TPbNvrSysAdvanceSysParam init_value = TPB_NVR_SYS_ADVANCE_SYS_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_advance_sys_param__get_packed_size
                     (const TPbNvrSysAdvanceSysParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_advance_sys_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_advance_sys_param__pack
                     (const TPbNvrSysAdvanceSysParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_advance_sys_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_advance_sys_param__pack_to_buffer
                     (const TPbNvrSysAdvanceSysParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_advance_sys_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysAdvanceSysParam *
       tpb_nvr_sys_advance_sys_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysAdvanceSysParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_advance_sys_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_advance_sys_param__free_unpacked
                     (TPbNvrSysAdvanceSysParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_advance_sys_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_custom_plug_download__init
                     (TPbNvrSysCustomPlugDownload         *message)
{
  static TPbNvrSysCustomPlugDownload init_value = TPB_NVR_SYS_CUSTOM_PLUG_DOWNLOAD__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_custom_plug_download__get_packed_size
                     (const TPbNvrSysCustomPlugDownload *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_custom_plug_download__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_custom_plug_download__pack
                     (const TPbNvrSysCustomPlugDownload *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_custom_plug_download__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_custom_plug_download__pack_to_buffer
                     (const TPbNvrSysCustomPlugDownload *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_custom_plug_download__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysCustomPlugDownload *
       tpb_nvr_sys_custom_plug_download__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysCustomPlugDownload *)
     protobuf_c_message_unpack (&tpb_nvr_sys_custom_plug_download__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_custom_plug_download__free_unpacked
                     (TPbNvrSysCustomPlugDownload *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_custom_plug_download__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_advance_param__init
                     (TPbNvrSysAdvanceParam         *message)
{
  static TPbNvrSysAdvanceParam init_value = TPB_NVR_SYS_ADVANCE_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_advance_param__get_packed_size
                     (const TPbNvrSysAdvanceParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_advance_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_advance_param__pack
                     (const TPbNvrSysAdvanceParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_advance_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_advance_param__pack_to_buffer
                     (const TPbNvrSysAdvanceParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_advance_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysAdvanceParam *
       tpb_nvr_sys_advance_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysAdvanceParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_advance_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_advance_param__free_unpacked
                     (TPbNvrSysAdvanceParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_advance_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_call_dev__init
                     (TPbNvrSysCallDev         *message)
{
  static TPbNvrSysCallDev init_value = TPB_NVR_SYS_CALL_DEV__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_call_dev__get_packed_size
                     (const TPbNvrSysCallDev *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_call_dev__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_call_dev__pack
                     (const TPbNvrSysCallDev *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_call_dev__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_call_dev__pack_to_buffer
                     (const TPbNvrSysCallDev *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_call_dev__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysCallDev *
       tpb_nvr_sys_call_dev__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysCallDev *)
     protobuf_c_message_unpack (&tpb_nvr_sys_call_dev__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_call_dev__free_unpacked
                     (TPbNvrSysCallDev *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_call_dev__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_aud_call_binding_param__init
                     (TPbNvrSysAudCallBindingParam         *message)
{
  static TPbNvrSysAudCallBindingParam init_value = TPB_NVR_SYS_AUD_CALL_BINDING_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_aud_call_binding_param__get_packed_size
                     (const TPbNvrSysAudCallBindingParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_aud_call_binding_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_aud_call_binding_param__pack
                     (const TPbNvrSysAudCallBindingParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_aud_call_binding_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_aud_call_binding_param__pack_to_buffer
                     (const TPbNvrSysAudCallBindingParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_aud_call_binding_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysAudCallBindingParam *
       tpb_nvr_sys_aud_call_binding_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysAudCallBindingParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_aud_call_binding_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_aud_call_binding_param__free_unpacked
                     (TPbNvrSysAudCallBindingParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_aud_call_binding_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_event_detail__init
                     (TPbNvrSysEventDetail         *message)
{
  static TPbNvrSysEventDetail init_value = TPB_NVR_SYS_EVENT_DETAIL__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_event_detail__get_packed_size
                     (const TPbNvrSysEventDetail *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_event_detail__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_event_detail__pack
                     (const TPbNvrSysEventDetail *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_event_detail__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_event_detail__pack_to_buffer
                     (const TPbNvrSysEventDetail *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_event_detail__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysEventDetail *
       tpb_nvr_sys_event_detail__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysEventDetail *)
     protobuf_c_message_unpack (&tpb_nvr_sys_event_detail__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_event_detail__free_unpacked
                     (TPbNvrSysEventDetail *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_event_detail__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_manual_event_param__init
                     (TPbNvrSysManualEventParam         *message)
{
  static TPbNvrSysManualEventParam init_value = TPB_NVR_SYS_MANUAL_EVENT_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_manual_event_param__get_packed_size
                     (const TPbNvrSysManualEventParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_manual_event_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_manual_event_param__pack
                     (const TPbNvrSysManualEventParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_manual_event_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_manual_event_param__pack_to_buffer
                     (const TPbNvrSysManualEventParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_manual_event_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysManualEventParam *
       tpb_nvr_sys_manual_event_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysManualEventParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_manual_event_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_manual_event_param__free_unpacked
                     (TPbNvrSysManualEventParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_manual_event_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_limit_speed_param__init
                     (TPbNvrSysLimitSpeedParam         *message)
{
  static TPbNvrSysLimitSpeedParam init_value = TPB_NVR_SYS_LIMIT_SPEED_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_limit_speed_param__get_packed_size
                     (const TPbNvrSysLimitSpeedParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_limit_speed_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_limit_speed_param__pack
                     (const TPbNvrSysLimitSpeedParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_limit_speed_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_limit_speed_param__pack_to_buffer
                     (const TPbNvrSysLimitSpeedParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_limit_speed_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysLimitSpeedParam *
       tpb_nvr_sys_limit_speed_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysLimitSpeedParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_limit_speed_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_limit_speed_param__free_unpacked
                     (TPbNvrSysLimitSpeedParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_limit_speed_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_geography_pos_param__init
                     (TPbNvrSysGeographyPosParam         *message)
{
  static TPbNvrSysGeographyPosParam init_value = TPB_NVR_SYS_GEOGRAPHY_POS_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_geography_pos_param__get_packed_size
                     (const TPbNvrSysGeographyPosParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_geography_pos_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_geography_pos_param__pack
                     (const TPbNvrSysGeographyPosParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_geography_pos_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_geography_pos_param__pack_to_buffer
                     (const TPbNvrSysGeographyPosParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_geography_pos_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysGeographyPosParam *
       tpb_nvr_sys_geography_pos_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysGeographyPosParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_geography_pos_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_geography_pos_param__free_unpacked
                     (TPbNvrSysGeographyPosParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_geography_pos_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_upgrade_server_param__init
                     (TPbNvrSysUpgradeServerParam         *message)
{
  static TPbNvrSysUpgradeServerParam init_value = TPB_NVR_SYS_UPGRADE_SERVER_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_upgrade_server_param__get_packed_size
                     (const TPbNvrSysUpgradeServerParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_upgrade_server_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_upgrade_server_param__pack
                     (const TPbNvrSysUpgradeServerParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_upgrade_server_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_upgrade_server_param__pack_to_buffer
                     (const TPbNvrSysUpgradeServerParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_upgrade_server_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysUpgradeServerParam *
       tpb_nvr_sys_upgrade_server_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysUpgradeServerParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_upgrade_server_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_upgrade_server_param__free_unpacked
                     (TPbNvrSysUpgradeServerParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_upgrade_server_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_vehicle_param__init
                     (TPbNvrSysVehicleParam         *message)
{
  static TPbNvrSysVehicleParam init_value = TPB_NVR_SYS_VEHICLE_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_vehicle_param__get_packed_size
                     (const TPbNvrSysVehicleParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_vehicle_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_vehicle_param__pack
                     (const TPbNvrSysVehicleParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_vehicle_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_vehicle_param__pack_to_buffer
                     (const TPbNvrSysVehicleParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_vehicle_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysVehicleParam *
       tpb_nvr_sys_vehicle_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysVehicleParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_vehicle_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_vehicle_param__free_unpacked
                     (TPbNvrSysVehicleParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_vehicle_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_message_submit_param__init
                     (TPbNvrSysMessageSubmitParam         *message)
{
  static TPbNvrSysMessageSubmitParam init_value = TPB_NVR_SYS_MESSAGE_SUBMIT_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_message_submit_param__get_packed_size
                     (const TPbNvrSysMessageSubmitParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_message_submit_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_message_submit_param__pack
                     (const TPbNvrSysMessageSubmitParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_message_submit_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_message_submit_param__pack_to_buffer
                     (const TPbNvrSysMessageSubmitParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_message_submit_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysMessageSubmitParam *
       tpb_nvr_sys_message_submit_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysMessageSubmitParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_message_submit_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_message_submit_param__free_unpacked
                     (TPbNvrSysMessageSubmitParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_message_submit_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_ball_ctr_matin_param__init
                     (TPbNvrBallCtrMatinParam         *message)
{
  static TPbNvrBallCtrMatinParam init_value = TPB_NVR_BALL_CTR_MATIN_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_ball_ctr_matin_param__get_packed_size
                     (const TPbNvrBallCtrMatinParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_ball_ctr_matin_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ball_ctr_matin_param__pack
                     (const TPbNvrBallCtrMatinParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ball_ctr_matin_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ball_ctr_matin_param__pack_to_buffer
                     (const TPbNvrBallCtrMatinParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ball_ctr_matin_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrBallCtrMatinParam *
       tpb_nvr_ball_ctr_matin_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrBallCtrMatinParam *)
     protobuf_c_message_unpack (&tpb_nvr_ball_ctr_matin_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ball_ctr_matin_param__free_unpacked
                     (TPbNvrBallCtrMatinParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ball_ctr_matin_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_enc_chn_switch_info__init
                     (TPbNvrSysEncChnSwitchInfo         *message)
{
  static TPbNvrSysEncChnSwitchInfo init_value = TPB_NVR_SYS_ENC_CHN_SWITCH_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_enc_chn_switch_info__get_packed_size
                     (const TPbNvrSysEncChnSwitchInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_enc_chn_switch_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_enc_chn_switch_info__pack
                     (const TPbNvrSysEncChnSwitchInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_enc_chn_switch_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_enc_chn_switch_info__pack_to_buffer
                     (const TPbNvrSysEncChnSwitchInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_enc_chn_switch_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysEncChnSwitchInfo *
       tpb_nvr_sys_enc_chn_switch_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysEncChnSwitchInfo *)
     protobuf_c_message_unpack (&tpb_nvr_sys_enc_chn_switch_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_enc_chn_switch_info__free_unpacked
                     (TPbNvrSysEncChnSwitchInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_enc_chn_switch_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_lan_enc_id_bind_param__init
                     (TPbNvrSysLanEncIdBindParam         *message)
{
  static TPbNvrSysLanEncIdBindParam init_value = TPB_NVR_SYS_LAN_ENC_ID_BIND_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_lan_enc_id_bind_param__get_packed_size
                     (const TPbNvrSysLanEncIdBindParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_lan_enc_id_bind_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_lan_enc_id_bind_param__pack
                     (const TPbNvrSysLanEncIdBindParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_lan_enc_id_bind_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_lan_enc_id_bind_param__pack_to_buffer
                     (const TPbNvrSysLanEncIdBindParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_lan_enc_id_bind_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysLanEncIdBindParam *
       tpb_nvr_sys_lan_enc_id_bind_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysLanEncIdBindParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_lan_enc_id_bind_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_lan_enc_id_bind_param__free_unpacked
                     (TPbNvrSysLanEncIdBindParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_lan_enc_id_bind_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_cfg__init
                     (TPbNvrSysCfg         *message)
{
  static TPbNvrSysCfg init_value = TPB_NVR_SYS_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_cfg__get_packed_size
                     (const TPbNvrSysCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_cfg__pack
                     (const TPbNvrSysCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_cfg__pack_to_buffer
                     (const TPbNvrSysCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysCfg *
       tpb_nvr_sys_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysCfg *)
     protobuf_c_message_unpack (&tpb_nvr_sys_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_cfg__free_unpacked
                     (TPbNvrSysCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_auto_day_param__init
                     (TPbNvrSysAutoDayParam         *message)
{
  static TPbNvrSysAutoDayParam init_value = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_auto_day_param__get_packed_size
                     (const TPbNvrSysAutoDayParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_day_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_auto_day_param__pack
                     (const TPbNvrSysAutoDayParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_day_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_auto_day_param__pack_to_buffer
                     (const TPbNvrSysAutoDayParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_day_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysAutoDayParam *
       tpb_nvr_sys_auto_day_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysAutoDayParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_auto_day_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_auto_day_param__free_unpacked
                     (TPbNvrSysAutoDayParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_day_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_auto_week_param__init
                     (TPbNvrSysAutoWeekParam         *message)
{
  static TPbNvrSysAutoWeekParam init_value = TPB_NVR_SYS_AUTO_WEEK_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_auto_week_param__get_packed_size
                     (const TPbNvrSysAutoWeekParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_week_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_auto_week_param__pack
                     (const TPbNvrSysAutoWeekParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_week_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_auto_week_param__pack_to_buffer
                     (const TPbNvrSysAutoWeekParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_week_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysAutoWeekParam *
       tpb_nvr_sys_auto_week_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysAutoWeekParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_auto_week_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_auto_week_param__free_unpacked
                     (TPbNvrSysAutoWeekParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_week_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_auto_month_param__init
                     (TPbNvrSysAutoMonthParam         *message)
{
  static TPbNvrSysAutoMonthParam init_value = TPB_NVR_SYS_AUTO_MONTH_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_auto_month_param__get_packed_size
                     (const TPbNvrSysAutoMonthParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_month_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_auto_month_param__pack
                     (const TPbNvrSysAutoMonthParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_month_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_auto_month_param__pack_to_buffer
                     (const TPbNvrSysAutoMonthParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_month_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysAutoMonthParam *
       tpb_nvr_sys_auto_month_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysAutoMonthParam *)
     protobuf_c_message_unpack (&tpb_nvr_sys_auto_month_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_auto_month_param__free_unpacked
                     (TPbNvrSysAutoMonthParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_auto_month_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_auto_reboot_param__init
                     (TPbNvrAutoRebootParam         *message)
{
  static TPbNvrAutoRebootParam init_value = TPB_NVR_AUTO_REBOOT_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_auto_reboot_param__get_packed_size
                     (const TPbNvrAutoRebootParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_auto_reboot_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_auto_reboot_param__pack
                     (const TPbNvrAutoRebootParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_auto_reboot_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_auto_reboot_param__pack_to_buffer
                     (const TPbNvrAutoRebootParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_auto_reboot_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAutoRebootParam *
       tpb_nvr_auto_reboot_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAutoRebootParam *)
     protobuf_c_message_unpack (&tpb_nvr_auto_reboot_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_auto_reboot_param__free_unpacked
                     (TPbNvrAutoRebootParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_auto_reboot_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_low_power_sleep_info__init
                     (TPbNvrSysLowPowerSleepInfo         *message)
{
  static TPbNvrSysLowPowerSleepInfo init_value = TPB_NVR_SYS_LOW_POWER_SLEEP_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_low_power_sleep_info__get_packed_size
                     (const TPbNvrSysLowPowerSleepInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_low_power_sleep_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_low_power_sleep_info__pack
                     (const TPbNvrSysLowPowerSleepInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_low_power_sleep_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_low_power_sleep_info__pack_to_buffer
                     (const TPbNvrSysLowPowerSleepInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_low_power_sleep_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysLowPowerSleepInfo *
       tpb_nvr_sys_low_power_sleep_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysLowPowerSleepInfo *)
     protobuf_c_message_unpack (&tpb_nvr_sys_low_power_sleep_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_low_power_sleep_info__free_unpacked
                     (TPbNvrSysLowPowerSleepInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_low_power_sleep_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_time_seg__init
                     (TPbNvrSysTimeSeg         *message)
{
  static TPbNvrSysTimeSeg init_value = TPB_NVR_SYS_TIME_SEG__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_time_seg__get_packed_size
                     (const TPbNvrSysTimeSeg *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_time_seg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_time_seg__pack
                     (const TPbNvrSysTimeSeg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_time_seg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_time_seg__pack_to_buffer
                     (const TPbNvrSysTimeSeg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_time_seg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysTimeSeg *
       tpb_nvr_sys_time_seg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysTimeSeg *)
     protobuf_c_message_unpack (&tpb_nvr_sys_time_seg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_time_seg__free_unpacked
                     (TPbNvrSysTimeSeg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_time_seg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_sleep_time_of_day__init
                     (TPbNvrSysSleepTimeOfDay         *message)
{
  static TPbNvrSysSleepTimeOfDay init_value = TPB_NVR_SYS_SLEEP_TIME_OF_DAY__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_sleep_time_of_day__get_packed_size
                     (const TPbNvrSysSleepTimeOfDay *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_time_of_day__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_sleep_time_of_day__pack
                     (const TPbNvrSysSleepTimeOfDay *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_time_of_day__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_sleep_time_of_day__pack_to_buffer
                     (const TPbNvrSysSleepTimeOfDay *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_time_of_day__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysSleepTimeOfDay *
       tpb_nvr_sys_sleep_time_of_day__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysSleepTimeOfDay *)
     protobuf_c_message_unpack (&tpb_nvr_sys_sleep_time_of_day__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_sleep_time_of_day__free_unpacked
                     (TPbNvrSysSleepTimeOfDay *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_time_of_day__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_timed_sleep_info__init
                     (TPbNvrSysTimedSleepInfo         *message)
{
  static TPbNvrSysTimedSleepInfo init_value = TPB_NVR_SYS_TIMED_SLEEP_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_timed_sleep_info__get_packed_size
                     (const TPbNvrSysTimedSleepInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_timed_sleep_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_timed_sleep_info__pack
                     (const TPbNvrSysTimedSleepInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_timed_sleep_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_timed_sleep_info__pack_to_buffer
                     (const TPbNvrSysTimedSleepInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_timed_sleep_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysTimedSleepInfo *
       tpb_nvr_sys_timed_sleep_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysTimedSleepInfo *)
     protobuf_c_message_unpack (&tpb_nvr_sys_timed_sleep_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_timed_sleep_info__free_unpacked
                     (TPbNvrSysTimedSleepInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_timed_sleep_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_sleep_of_mode_info__init
                     (TPbNvrSysSleepOfModeInfo         *message)
{
  static TPbNvrSysSleepOfModeInfo init_value = TPB_NVR_SYS_SLEEP_OF_MODE_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_sleep_of_mode_info__get_packed_size
                     (const TPbNvrSysSleepOfModeInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_of_mode_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_sleep_of_mode_info__pack
                     (const TPbNvrSysSleepOfModeInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_of_mode_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_sleep_of_mode_info__pack_to_buffer
                     (const TPbNvrSysSleepOfModeInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_of_mode_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysSleepOfModeInfo *
       tpb_nvr_sys_sleep_of_mode_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysSleepOfModeInfo *)
     protobuf_c_message_unpack (&tpb_nvr_sys_sleep_of_mode_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_sleep_of_mode_info__free_unpacked
                     (TPbNvrSysSleepOfModeInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_of_mode_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_sleep_info__init
                     (TPbNvrSysSleepInfo         *message)
{
  static TPbNvrSysSleepInfo init_value = TPB_NVR_SYS_SLEEP_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_sleep_info__get_packed_size
                     (const TPbNvrSysSleepInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_sleep_info__pack
                     (const TPbNvrSysSleepInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_sleep_info__pack_to_buffer
                     (const TPbNvrSysSleepInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysSleepInfo *
       tpb_nvr_sys_sleep_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysSleepInfo *)
     protobuf_c_message_unpack (&tpb_nvr_sys_sleep_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_sleep_info__free_unpacked
                     (TPbNvrSysSleepInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_sleep_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sys_power_waste_mode_info__init
                     (TPbNvrSysPowerWasteModeInfo         *message)
{
  static TPbNvrSysPowerWasteModeInfo init_value = TPB_NVR_SYS_POWER_WASTE_MODE_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_sys_power_waste_mode_info__get_packed_size
                     (const TPbNvrSysPowerWasteModeInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_sys_power_waste_mode_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sys_power_waste_mode_info__pack
                     (const TPbNvrSysPowerWasteModeInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sys_power_waste_mode_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sys_power_waste_mode_info__pack_to_buffer
                     (const TPbNvrSysPowerWasteModeInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sys_power_waste_mode_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSysPowerWasteModeInfo *
       tpb_nvr_sys_power_waste_mode_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSysPowerWasteModeInfo *)
     protobuf_c_message_unpack (&tpb_nvr_sys_power_waste_mode_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sys_power_waste_mode_info__free_unpacked
                     (TPbNvrSysPowerWasteModeInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sys_power_waste_mode_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_sys_param__field_descriptors[11] =
{
  {
    "dev_name",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSysParam, has_dev_name),
    offsetof(TPbNvrSysParam, dev_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_name_len",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_dev_name_len),
    offsetof(TPbNvrSysParam, dev_name_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_num",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_dev_num),
    offsetof(TPbNvrSysParam, dev_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "language",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_language),
    offsetof(TPbNvrSysParam, language),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "auto_logout",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_auto_logout),
    offsetof(TPbNvrSysParam, auto_logout),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "audio_listen",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_audio_listen),
    offsetof(TPbNvrSysParam, audio_listen),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "menu_tran",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_menu_tran),
    offsetof(TPbNvrSysParam, menu_tran),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "boot_guide_enable",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_boot_guide_enable),
    offsetof(TPbNvrSysParam, boot_guide_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "opt_pwd_enable",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_opt_pwd_enable),
    offsetof(TPbNvrSysParam, opt_pwd_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "delay_shutdown_time",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_delay_shutdown_time),
    offsetof(TPbNvrSysParam, delay_shutdown_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "security_level",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysParam, has_security_level),
    offsetof(TPbNvrSysParam, security_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_param__field_indices_by_name[] = {
  5,   /* field[5] = audio_listen */
  4,   /* field[4] = auto_logout */
  7,   /* field[7] = boot_guide_enable */
  9,   /* field[9] = delay_shutdown_time */
  0,   /* field[0] = dev_name */
  1,   /* field[1] = dev_name_len */
  2,   /* field[2] = dev_num */
  3,   /* field[3] = language */
  6,   /* field[6] = menu_tran */
  8,   /* field[8] = opt_pwd_enable */
  10,   /* field[10] = security_level */
};
static const ProtobufCIntRange tpb_nvr_sys_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 11 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysParam",
  "TPbNvrSysParam",
  "TPbNvrSysParam",
  "",
  sizeof(TPbNvrSysParam),
  11,
  tpb_nvr_sys_param__field_descriptors,
  tpb_nvr_sys_param__field_indices_by_name,
  1,  tpb_nvr_sys_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_vid_res__field_descriptors[2] =
{
  {
    "width",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysVidRes, has_width),
    offsetof(TPbNvrSysVidRes, width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "height",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysVidRes, has_height),
    offsetof(TPbNvrSysVidRes, height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_vid_res__field_indices_by_name[] = {
  1,   /* field[1] = height */
  0,   /* field[0] = width */
};
static const ProtobufCIntRange tpb_nvr_sys_vid_res__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_vid_res__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysVidRes",
  "TPbNvrSysVidRes",
  "TPbNvrSysVidRes",
  "",
  sizeof(TPbNvrSysVidRes),
  2,
  tpb_nvr_sys_vid_res__field_descriptors,
  tpb_nvr_sys_vid_res__field_indices_by_name,
  1,  tpb_nvr_sys_vid_res__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_vid_res__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_zero_chn_enc_param__field_descriptors[6] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysZeroChnEncParam, has_enable),
    offsetof(TPbNvrSysZeroChnEncParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enc_res",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysZeroChnEncParam, enc_res),
    &tpb_nvr_sys_vid_res__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "frame_rate",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysZeroChnEncParam, has_frame_rate),
    offsetof(TPbNvrSysZeroChnEncParam, frame_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bit_rate",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysZeroChnEncParam, has_bit_rate),
    offsetof(TPbNvrSysZeroChnEncParam, bit_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "video_src",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysZeroChnEncParam, has_video_src),
    offsetof(TPbNvrSysZeroChnEncParam, video_src),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enc_type",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysZeroChnEncParam, has_enc_type),
    offsetof(TPbNvrSysZeroChnEncParam, enc_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_zero_chn_enc_param__field_indices_by_name[] = {
  3,   /* field[3] = bit_rate */
  0,   /* field[0] = enable */
  1,   /* field[1] = enc_res */
  5,   /* field[5] = enc_type */
  2,   /* field[2] = frame_rate */
  4,   /* field[4] = video_src */
};
static const ProtobufCIntRange tpb_nvr_sys_zero_chn_enc_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_zero_chn_enc_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysZeroChnEncParam",
  "TPbNvrSysZeroChnEncParam",
  "TPbNvrSysZeroChnEncParam",
  "",
  sizeof(TPbNvrSysZeroChnEncParam),
  6,
  tpb_nvr_sys_zero_chn_enc_param__field_descriptors,
  tpb_nvr_sys_zero_chn_enc_param__field_indices_by_name,
  1,  tpb_nvr_sys_zero_chn_enc_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_zero_chn_enc_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_ntp_param__field_descriptors[4] =
{
  {
    "server_port",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNtpParam, has_server_port),
    offsetof(TPbNvrNtpParam, server_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "interval",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNtpParam, has_interval),
    offsetof(TPbNvrNtpParam, interval),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "server_ip",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNtpParam, server_ip),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "backupserver_ip",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNtpParam, backupserver_ip),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ntp_param__field_indices_by_name[] = {
  3,   /* field[3] = backupserver_ip */
  1,   /* field[1] = interval */
  2,   /* field[2] = server_ip */
  0,   /* field[0] = server_port */
};
static const ProtobufCIntRange tpb_nvr_ntp_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_ntp_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNtpParam",
  "TPbNvrNtpParam",
  "TPbNvrNtpParam",
  "",
  sizeof(TPbNvrNtpParam),
  4,
  tpb_nvr_ntp_param__field_descriptors,
  tpb_nvr_ntp_param__field_indices_by_name,
  1,  tpb_nvr_ntp_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ntp_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_time_auto_adapt_param__field_descriptors[4] =
{
  {
    "src_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTimeAutoAdaptParam, has_src_num),
    offsetof(TPbNvrTimeAutoAdaptParam, src_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sync_type_pri",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTimeAutoAdaptParam, n_sync_type_pri),
    offsetof(TPbNvrTimeAutoAdaptParam, sync_type_pri),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sync_type_enable",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTimeAutoAdaptParam, n_sync_type_enable),
    offsetof(TPbNvrTimeAutoAdaptParam, sync_type_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "adapt_lock_time",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTimeAutoAdaptParam, has_adapt_lock_time),
    offsetof(TPbNvrTimeAutoAdaptParam, adapt_lock_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_time_auto_adapt_param__field_indices_by_name[] = {
  3,   /* field[3] = adapt_lock_time */
  0,   /* field[0] = src_num */
  2,   /* field[2] = sync_type_enable */
  1,   /* field[1] = sync_type_pri */
};
static const ProtobufCIntRange tpb_nvr_time_auto_adapt_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_time_auto_adapt_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrTimeAutoAdaptParam",
  "TPbNvrTimeAutoAdaptParam",
  "TPbNvrTimeAutoAdaptParam",
  "",
  sizeof(TPbNvrTimeAutoAdaptParam),
  4,
  tpb_nvr_time_auto_adapt_param__field_descriptors,
  tpb_nvr_time_auto_adapt_param__field_indices_by_name,
  1,  tpb_nvr_time_auto_adapt_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_time_auto_adapt_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_ptp_param__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtpParam, has_enable),
    offsetof(TPbNvrPtpParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "eth_name",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrPtpParam, has_eth_name),
    offsetof(TPbNvrPtpParam, eth_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptpmode",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrPtpParam, has_ptpmode),
    offsetof(TPbNvrPtpParam, ptpmode),
    &em_pb_nvr_sys_ptp_clock_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptptype",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrPtpParam, has_ptptype),
    offsetof(TPbNvrPtpParam, ptptype),
    &em_pb_nvr_sys_ptp_clock_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ptp_param__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  1,   /* field[1] = eth_name */
  2,   /* field[2] = ptpmode */
  3,   /* field[3] = ptptype */
};
static const ProtobufCIntRange tpb_nvr_ptp_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_ptp_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPtpParam",
  "TPbNvrPtpParam",
  "TPbNvrPtpParam",
  "",
  sizeof(TPbNvrPtpParam),
  4,
  tpb_nvr_ptp_param__field_descriptors,
  tpb_nvr_ptp_param__field_indices_by_name,
  1,  tpb_nvr_ptp_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ptp_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_auto_sync_time_param__field_descriptors[7] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAutoSyncTimeParam, has_enable),
    offsetof(TPbNvrAutoSyncTimeParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrAutoSyncTimeParam, has_type),
    offsetof(TPbNvrAutoSyncTimeParam, type),
    &em_pb_nvr_sys_synctime_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ntp_sync",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrAutoSyncTimeParam, ntp_sync),
    &tpb_nvr_ntp_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ntp_enable",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAutoSyncTimeParam, has_ntp_enable),
    offsetof(TPbNvrAutoSyncTimeParam, ntp_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sync_type",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAutoSyncTimeParam, has_sync_type),
    offsetof(TPbNvrAutoSyncTimeParam, sync_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "auto_adpt",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrAutoSyncTimeParam, auto_adpt),
    &tpb_nvr_time_auto_adapt_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptp_sync_list",
    7,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrAutoSyncTimeParam, n_ptp_sync_list),
    offsetof(TPbNvrAutoSyncTimeParam, ptp_sync_list),
    &tpb_nvr_ptp_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_auto_sync_time_param__field_indices_by_name[] = {
  5,   /* field[5] = auto_adpt */
  0,   /* field[0] = enable */
  3,   /* field[3] = ntp_enable */
  2,   /* field[2] = ntp_sync */
  6,   /* field[6] = ptp_sync_list */
  4,   /* field[4] = sync_type */
  1,   /* field[1] = type */
};
static const ProtobufCIntRange tpb_nvr_auto_sync_time_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 7 }
};
const ProtobufCMessageDescriptor tpb_nvr_auto_sync_time_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAutoSyncTimeParam",
  "TPbNvrAutoSyncTimeParam",
  "TPbNvrAutoSyncTimeParam",
  "",
  sizeof(TPbNvrAutoSyncTimeParam),
  7,
  tpb_nvr_auto_sync_time_param__field_descriptors,
  tpb_nvr_auto_sync_time_param__field_indices_by_name,
  1,  tpb_nvr_auto_sync_time_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_auto_sync_time_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_summer_time__field_descriptors[4] =
{
  {
    "month",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSummerTime, has_month),
    offsetof(TPbNvrSummerTime, month),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "week_of_month",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSummerTime, has_week_of_month),
    offsetof(TPbNvrSummerTime, week_of_month),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSummerTime, has_day),
    offsetof(TPbNvrSummerTime, day),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "hour",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSummerTime, has_hour),
    offsetof(TPbNvrSummerTime, hour),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_summer_time__field_indices_by_name[] = {
  2,   /* field[2] = day */
  3,   /* field[3] = hour */
  0,   /* field[0] = month */
  1,   /* field[1] = week_of_month */
};
static const ProtobufCIntRange tpb_nvr_summer_time__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_summer_time__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSummerTime",
  "TPbNvrSummerTime",
  "TPbNvrSummerTime",
  "",
  sizeof(TPbNvrSummerTime),
  4,
  tpb_nvr_summer_time__field_descriptors,
  tpb_nvr_summer_time__field_indices_by_name,
  1,  tpb_nvr_summer_time__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_summer_time__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_summer_time_param__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSummerTimeParam, has_enable),
    offsetof(TPbNvrSummerTimeParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "offSet",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSummerTimeParam, has_offset),
    offsetof(TPbNvrSummerTimeParam, offset),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "begin",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSummerTimeParam, begin),
    &tpb_nvr_summer_time__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSummerTimeParam, end),
    &tpb_nvr_summer_time__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_summer_time_param__field_indices_by_name[] = {
  2,   /* field[2] = begin */
  0,   /* field[0] = enable */
  3,   /* field[3] = end */
  1,   /* field[1] = offSet */
};
static const ProtobufCIntRange tpb_nvr_summer_time_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_summer_time_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSummerTimeParam",
  "TPbNvrSummerTimeParam",
  "TPbNvrSummerTimeParam",
  "",
  sizeof(TPbNvrSummerTimeParam),
  4,
  tpb_nvr_summer_time_param__field_descriptors,
  tpb_nvr_summer_time_param__field_indices_by_name,
  1,  tpb_nvr_summer_time_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_summer_time_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_time_param__field_descriptors[3] =
{
  {
    "time_zone",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrTimeParam, has_time_zone),
    offsetof(TPbNvrTimeParam, time_zone),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "summer_param",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrTimeParam, summer_param),
    &tpb_nvr_summer_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "auto_sync_param",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrTimeParam, auto_sync_param),
    &tpb_nvr_auto_sync_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_time_param__field_indices_by_name[] = {
  2,   /* field[2] = auto_sync_param */
  1,   /* field[1] = summer_param */
  0,   /* field[0] = time_zone */
};
static const ProtobufCIntRange tpb_nvr_time_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_time_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrTimeParam",
  "TPbNvrTimeParam",
  "TPbNvrTimeParam",
  "",
  sizeof(TPbNvrTimeParam),
  3,
  tpb_nvr_time_param__field_descriptors,
  tpb_nvr_time_param__field_indices_by_name,
  1,  tpb_nvr_time_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_time_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_udp_re_tran_param__field_descriptors[5] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysUdpReTranParam, has_enable),
    offsetof(TPbNvrSysUdpReTranParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "first_check_point",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysUdpReTranParam, has_first_check_point),
    offsetof(TPbNvrSysUdpReTranParam, first_check_point),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "second_check_point",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysUdpReTranParam, has_second_check_point),
    offsetof(TPbNvrSysUdpReTranParam, second_check_point),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "third_check_point",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysUdpReTranParam, has_third_check_point),
    offsetof(TPbNvrSysUdpReTranParam, third_check_point),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "overdue_discard",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysUdpReTranParam, has_overdue_discard),
    offsetof(TPbNvrSysUdpReTranParam, overdue_discard),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_udp_re_tran_param__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  1,   /* field[1] = first_check_point */
  4,   /* field[4] = overdue_discard */
  2,   /* field[2] = second_check_point */
  3,   /* field[3] = third_check_point */
};
static const ProtobufCIntRange tpb_nvr_sys_udp_re_tran_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_udp_re_tran_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysUdpReTranParam",
  "TPbNvrSysUdpReTranParam",
  "TPbNvrSysUdpReTranParam",
  "",
  sizeof(TPbNvrSysUdpReTranParam),
  5,
  tpb_nvr_sys_udp_re_tran_param__field_descriptors,
  tpb_nvr_sys_udp_re_tran_param__field_indices_by_name,
  1,  tpb_nvr_sys_udp_re_tran_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_udp_re_tran_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_ktcp_param__field_descriptors[6] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysKtcpParam, has_enable),
    offsetof(TPbNvrSysKtcpParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_port",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysKtcpParam, has_start_port),
    offsetof(TPbNvrSysKtcpParam, start_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_port",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysKtcpParam, has_end_port),
    offsetof(TPbNvrSysKtcpParam, end_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bandwidth_min",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysKtcpParam, has_bandwidth_min),
    offsetof(TPbNvrSysKtcpParam, bandwidth_min),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bandwidth_max",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysKtcpParam, has_bandwidth_max),
    offsetof(TPbNvrSysKtcpParam, bandwidth_max),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "conge_type",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysKtcpParam, has_conge_type),
    offsetof(TPbNvrSysKtcpParam, conge_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_ktcp_param__field_indices_by_name[] = {
  4,   /* field[4] = bandwidth_max */
  3,   /* field[3] = bandwidth_min */
  5,   /* field[5] = conge_type */
  0,   /* field[0] = enable */
  2,   /* field[2] = end_port */
  1,   /* field[1] = start_port */
};
static const ProtobufCIntRange tpb_nvr_sys_ktcp_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_ktcp_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysKtcpParam",
  "TPbNvrSysKtcpParam",
  "TPbNvrSysKtcpParam",
  "",
  sizeof(TPbNvrSysKtcpParam),
  6,
  tpb_nvr_sys_ktcp_param__field_descriptors,
  tpb_nvr_sys_ktcp_param__field_indices_by_name,
  1,  tpb_nvr_sys_ktcp_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_ktcp_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_mrtc_param__field_descriptors[2] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysMrtcParam, has_enable),
    offsetof(TPbNvrSysMrtcParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "conge_type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysMrtcParam, has_conge_type),
    offsetof(TPbNvrSysMrtcParam, conge_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_mrtc_param__field_indices_by_name[] = {
  1,   /* field[1] = conge_type */
  0,   /* field[0] = enable */
};
static const ProtobufCIntRange tpb_nvr_sys_mrtc_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_mrtc_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysMrtcParam",
  "TPbNvrSysMrtcParam",
  "TPbNvrSysMrtcParam",
  "",
  sizeof(TPbNvrSysMrtcParam),
  2,
  tpb_nvr_sys_mrtc_param__field_descriptors,
  tpb_nvr_sys_mrtc_param__field_indices_by_name,
  1,  tpb_nvr_sys_mrtc_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_mrtc_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_ping_param__field_descriptors[1] =
{
  {
    "disable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysPingParam, has_disable),
    offsetof(TPbNvrSysPingParam, disable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_ping_param__field_indices_by_name[] = {
  0,   /* field[0] = disable */
};
static const ProtobufCIntRange tpb_nvr_sys_ping_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_ping_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysPingParam",
  "TPbNvrSysPingParam",
  "TPbNvrSysPingParam",
  "",
  sizeof(TPbNvrSysPingParam),
  1,
  tpb_nvr_sys_ping_param__field_descriptors,
  tpb_nvr_sys_ping_param__field_indices_by_name,
  1,  tpb_nvr_sys_ping_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_ping_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_advance_sys_param__field_descriptors[16] =
{
  {
    "disk_prerecord",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_disk_prerecord),
    offsetof(TPbNvrSysAdvanceSysParam, disk_prerecord),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "third_enc_sup",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_third_enc_sup),
    offsetof(TPbNvrSysAdvanceSysParam, third_enc_sup),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "double_aud_sup",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_double_aud_sup),
    offsetof(TPbNvrSysAdvanceSysParam, double_aud_sup),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "audcall_enctype",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrSysAdvanceSysParam, has_audcall_enctype),
    offsetof(TPbNvrSysAdvanceSysParam, audcall_enctype),
    &em_pb_nvr_sys_aud_call_enc_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "blk_size",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_blk_size),
    offsetof(TPbNvrSysAdvanceSysParam, blk_size),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptz_ctrl_time",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_ptz_ctrl_time),
    offsetof(TPbNvrSysAdvanceSysParam, ptz_ctrl_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "close_audio",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_close_audio),
    offsetof(TPbNvrSysAdvanceSysParam, close_audio),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_delay_time",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_alarm_delay_time),
    offsetof(TPbNvrSysAdvanceSysParam, alarm_delay_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dynamic_plugin",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_dynamic_plugin),
    offsetof(TPbNvrSysAdvanceSysParam, dynamic_plugin),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "double_aud_mix",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_double_aud_mix),
    offsetof(TPbNvrSysAdvanceSysParam, double_aud_mix),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "stream_extern_head",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_stream_extern_head),
    offsetof(TPbNvrSysAdvanceSysParam, stream_extern_head),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vsip_app_enable",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_vsip_app_enable),
    offsetof(TPbNvrSysAdvanceSysParam, vsip_app_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vsip_clt_enable",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_vsip_clt_enable),
    offsetof(TPbNvrSysAdvanceSysParam, vsip_clt_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "smooth_snd_enable",
    16,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_smooth_snd_enable),
    offsetof(TPbNvrSysAdvanceSysParam, smooth_snd_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "smooth_snd_rate",
    17,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_smooth_snd_rate),
    offsetof(TPbNvrSysAdvanceSysParam, smooth_snd_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "temp_meas_enable",
    19,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceSysParam, has_temp_meas_enable),
    offsetof(TPbNvrSysAdvanceSysParam, temp_meas_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_advance_sys_param__field_indices_by_name[] = {
  7,   /* field[7] = alarm_delay_time */
  3,   /* field[3] = audcall_enctype */
  4,   /* field[4] = blk_size */
  6,   /* field[6] = close_audio */
  0,   /* field[0] = disk_prerecord */
  9,   /* field[9] = double_aud_mix */
  2,   /* field[2] = double_aud_sup */
  8,   /* field[8] = dynamic_plugin */
  5,   /* field[5] = ptz_ctrl_time */
  13,   /* field[13] = smooth_snd_enable */
  14,   /* field[14] = smooth_snd_rate */
  10,   /* field[10] = stream_extern_head */
  15,   /* field[15] = temp_meas_enable */
  1,   /* field[1] = third_enc_sup */
  11,   /* field[11] = vsip_app_enable */
  12,   /* field[12] = vsip_clt_enable */
};
static const ProtobufCIntRange tpb_nvr_sys_advance_sys_param__number_ranges[3 + 1] =
{
  { 1, 0 },
  { 16, 13 },
  { 19, 15 },
  { 0, 16 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_advance_sys_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysAdvanceSysParam",
  "TPbNvrSysAdvanceSysParam",
  "TPbNvrSysAdvanceSysParam",
  "",
  sizeof(TPbNvrSysAdvanceSysParam),
  16,
  tpb_nvr_sys_advance_sys_param__field_descriptors,
  tpb_nvr_sys_advance_sys_param__field_indices_by_name,
  3,  tpb_nvr_sys_advance_sys_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_advance_sys_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_custom_plug_download__field_descriptors[2] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysCustomPlugDownload, has_enable),
    offsetof(TPbNvrSysCustomPlugDownload, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "custom_plug_url",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSysCustomPlugDownload, has_custom_plug_url),
    offsetof(TPbNvrSysCustomPlugDownload, custom_plug_url),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_custom_plug_download__field_indices_by_name[] = {
  1,   /* field[1] = custom_plug_url */
  0,   /* field[0] = enable */
};
static const ProtobufCIntRange tpb_nvr_sys_custom_plug_download__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_custom_plug_download__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysCustomPlugDownload",
  "TPbNvrSysCustomPlugDownload",
  "TPbNvrSysCustomPlugDownload",
  "",
  sizeof(TPbNvrSysCustomPlugDownload),
  2,
  tpb_nvr_sys_custom_plug_download__field_descriptors,
  tpb_nvr_sys_custom_plug_download__field_indices_by_name,
  1,  tpb_nvr_sys_custom_plug_download__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_custom_plug_download__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_advance_param__field_descriptors[9] =
{
  {
    "udp_re_tran_param",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysAdvanceParam, udp_re_tran_param),
    &tpb_nvr_sys_udp_re_tran_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "advance_sys_param",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysAdvanceParam, advance_sys_param),
    &tpb_nvr_sys_advance_sys_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "plug_download",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysAdvanceParam, plug_download),
    &tpb_nvr_sys_custom_plug_download__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ktcp_param",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysAdvanceParam, ktcp_param),
    &tpb_nvr_sys_ktcp_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ping_param",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysAdvanceParam, ping_param),
    &tpb_nvr_sys_ping_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mtu",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceParam, has_mtu),
    offsetof(TPbNvrSysAdvanceParam, mtu),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mrtc_param",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysAdvanceParam, mrtc_param),
    &tpb_nvr_sys_mrtc_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "not_rtcp_port_share",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceParam, has_not_rtcp_port_share),
    offsetof(TPbNvrSysAdvanceParam, not_rtcp_port_share),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "not_rtcp_port_share_repair",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAdvanceParam, has_not_rtcp_port_share_repair),
    offsetof(TPbNvrSysAdvanceParam, not_rtcp_port_share_repair),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_advance_param__field_indices_by_name[] = {
  1,   /* field[1] = advance_sys_param */
  3,   /* field[3] = ktcp_param */
  6,   /* field[6] = mrtc_param */
  5,   /* field[5] = mtu */
  7,   /* field[7] = not_rtcp_port_share */
  8,   /* field[8] = not_rtcp_port_share_repair */
  4,   /* field[4] = ping_param */
  2,   /* field[2] = plug_download */
  0,   /* field[0] = udp_re_tran_param */
};
static const ProtobufCIntRange tpb_nvr_sys_advance_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_advance_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysAdvanceParam",
  "TPbNvrSysAdvanceParam",
  "TPbNvrSysAdvanceParam",
  "",
  sizeof(TPbNvrSysAdvanceParam),
  9,
  tpb_nvr_sys_advance_param__field_descriptors,
  tpb_nvr_sys_advance_param__field_indices_by_name,
  1,  tpb_nvr_sys_advance_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_advance_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_call_dev__field_descriptors[3] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysCallDev, has_enable),
    offsetof(TPbNvrSysCallDev, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "unicode_len",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysCallDev, has_unicode_len),
    offsetof(TPbNvrSysCallDev, unicode_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_unicode_name",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSysCallDev, has_dev_unicode_name),
    offsetof(TPbNvrSysCallDev, dev_unicode_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_call_dev__field_indices_by_name[] = {
  2,   /* field[2] = dev_unicode_name */
  0,   /* field[0] = enable */
  1,   /* field[1] = unicode_len */
};
static const ProtobufCIntRange tpb_nvr_sys_call_dev__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_call_dev__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysCallDev",
  "TPbNvrSysCallDev",
  "TPbNvrSysCallDev",
  "",
  sizeof(TPbNvrSysCallDev),
  3,
  tpb_nvr_sys_call_dev__field_descriptors,
  tpb_nvr_sys_call_dev__field_indices_by_name,
  1,  tpb_nvr_sys_call_dev__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_call_dev__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_aud_call_binding_param__field_descriptors[6] =
{
  {
    "call_binding_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAudCallBindingParam, has_call_binding_num),
    offsetof(TPbNvrSysAudCallBindingParam, call_binding_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "suppt_call_dev_num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAudCallBindingParam, has_suppt_call_dev_num),
    offsetof(TPbNvrSysAudCallBindingParam, suppt_call_dev_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "call_mix",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAudCallBindingParam, has_call_mix),
    offsetof(TPbNvrSysAudCallBindingParam, call_mix),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_aec",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAudCallBindingParam, has_dev_aec),
    offsetof(TPbNvrSysAudCallBindingParam, dev_aec),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "call_binding_chn_id",
    5,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAudCallBindingParam, n_call_binding_chn_id),
    offsetof(TPbNvrSysAudCallBindingParam, call_binding_chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "call_dev",
    6,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSysAudCallBindingParam, n_call_dev),
    offsetof(TPbNvrSysAudCallBindingParam, call_dev),
    &tpb_nvr_sys_call_dev__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_aud_call_binding_param__field_indices_by_name[] = {
  4,   /* field[4] = call_binding_chn_id */
  0,   /* field[0] = call_binding_num */
  5,   /* field[5] = call_dev */
  2,   /* field[2] = call_mix */
  3,   /* field[3] = dev_aec */
  1,   /* field[1] = suppt_call_dev_num */
};
static const ProtobufCIntRange tpb_nvr_sys_aud_call_binding_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_aud_call_binding_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysAudCallBindingParam",
  "TPbNvrSysAudCallBindingParam",
  "TPbNvrSysAudCallBindingParam",
  "",
  sizeof(TPbNvrSysAudCallBindingParam),
  6,
  tpb_nvr_sys_aud_call_binding_param__field_descriptors,
  tpb_nvr_sys_aud_call_binding_param__field_indices_by_name,
  1,  tpb_nvr_sys_aud_call_binding_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_aud_call_binding_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_event_detail__field_descriptors[5] =
{
  {
    "event_name_len",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysEventDetail, has_event_name_len),
    offsetof(TPbNvrSysEventDetail, event_name_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "event_name",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSysEventDetail, has_event_name),
    offsetof(TPbNvrSysEventDetail, event_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "desc_len",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysEventDetail, has_desc_len),
    offsetof(TPbNvrSysEventDetail, desc_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "desc",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSysEventDetail, has_desc),
    offsetof(TPbNvrSysEventDetail, desc),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "default",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysEventDetail, has_default_),
    offsetof(TPbNvrSysEventDetail, default_),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_event_detail__field_indices_by_name[] = {
  4,   /* field[4] = default */
  3,   /* field[3] = desc */
  2,   /* field[2] = desc_len */
  1,   /* field[1] = event_name */
  0,   /* field[0] = event_name_len */
};
static const ProtobufCIntRange tpb_nvr_sys_event_detail__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_event_detail__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysEventDetail",
  "TPbNvrSysEventDetail",
  "TPbNvrSysEventDetail",
  "",
  sizeof(TPbNvrSysEventDetail),
  5,
  tpb_nvr_sys_event_detail__field_descriptors,
  tpb_nvr_sys_event_detail__field_indices_by_name,
  1,  tpb_nvr_sys_event_detail__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_event_detail__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_manual_event_param__field_descriptors[3] =
{
  {
    "event_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysManualEventParam, has_event_num),
    offsetof(TPbNvrSysManualEventParam, event_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "event_list",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSysManualEventParam, n_event_list),
    offsetof(TPbNvrSysManualEventParam, event_list),
    &tpb_nvr_sys_event_detail__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "trg_event_snap",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysManualEventParam, has_trg_event_snap),
    offsetof(TPbNvrSysManualEventParam, trg_event_snap),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_manual_event_param__field_indices_by_name[] = {
  1,   /* field[1] = event_list */
  0,   /* field[0] = event_num */
  2,   /* field[2] = trg_event_snap */
};
static const ProtobufCIntRange tpb_nvr_sys_manual_event_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_manual_event_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysManualEventParam",
  "TPbNvrSysManualEventParam",
  "TPbNvrSysManualEventParam",
  "",
  sizeof(TPbNvrSysManualEventParam),
  3,
  tpb_nvr_sys_manual_event_param__field_descriptors,
  tpb_nvr_sys_manual_event_param__field_indices_by_name,
  1,  tpb_nvr_sys_manual_event_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_manual_event_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_limit_speed_param__field_descriptors[3] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysLimitSpeedParam, has_enable),
    offsetof(TPbNvrSysLimitSpeedParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "limit_value",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysLimitSpeedParam, has_limit_value),
    offsetof(TPbNvrSysLimitSpeedParam, limit_value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "over_speed_report",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysLimitSpeedParam, has_over_speed_report),
    offsetof(TPbNvrSysLimitSpeedParam, over_speed_report),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_limit_speed_param__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  1,   /* field[1] = limit_value */
  2,   /* field[2] = over_speed_report */
};
static const ProtobufCIntRange tpb_nvr_sys_limit_speed_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_limit_speed_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysLimitSpeedParam",
  "TPbNvrSysLimitSpeedParam",
  "TPbNvrSysLimitSpeedParam",
  "",
  sizeof(TPbNvrSysLimitSpeedParam),
  3,
  tpb_nvr_sys_limit_speed_param__field_descriptors,
  tpb_nvr_sys_limit_speed_param__field_indices_by_name,
  1,  tpb_nvr_sys_limit_speed_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_limit_speed_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_geography_pos_param__field_descriptors[6] =
{
  {
    "open_gps",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysGeographyPosParam, has_open_gps),
    offsetof(TPbNvrSysGeographyPosParam, open_gps),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gps_loc_mode",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysGeographyPosParam, has_gps_loc_mode),
    offsetof(TPbNvrSysGeographyPosParam, gps_loc_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enhance_agps",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysGeographyPosParam, has_enhance_agps),
    offsetof(TPbNvrSysGeographyPosParam, enhance_agps),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nty_ipc",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysGeographyPosParam, has_nty_ipc),
    offsetof(TPbNvrSysGeographyPosParam, nty_ipc),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upload_pubsec",
    5,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysGeographyPosParam, n_upload_pubsec),
    offsetof(TPbNvrSysGeographyPosParam, upload_pubsec),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "limit_speed",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysGeographyPosParam, limit_speed),
    &tpb_nvr_sys_limit_speed_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_geography_pos_param__field_indices_by_name[] = {
  2,   /* field[2] = enhance_agps */
  1,   /* field[1] = gps_loc_mode */
  5,   /* field[5] = limit_speed */
  3,   /* field[3] = nty_ipc */
  0,   /* field[0] = open_gps */
  4,   /* field[4] = upload_pubsec */
};
static const ProtobufCIntRange tpb_nvr_sys_geography_pos_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_geography_pos_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysGeographyPosParam",
  "TPbNvrSysGeographyPosParam",
  "TPbNvrSysGeographyPosParam",
  "",
  sizeof(TPbNvrSysGeographyPosParam),
  6,
  tpb_nvr_sys_geography_pos_param__field_descriptors,
  tpb_nvr_sys_geography_pos_param__field_indices_by_name,
  1,  tpb_nvr_sys_geography_pos_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_geography_pos_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_upgrade_server_param__field_descriptors[3] =
{
  {
    "auto_upgrade",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysUpgradeServerParam, has_auto_upgrade),
    offsetof(TPbNvrSysUpgradeServerParam, auto_upgrade),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "server_ip",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSysUpgradeServerParam, has_server_ip),
    offsetof(TPbNvrSysUpgradeServerParam, server_ip),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "server_port",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysUpgradeServerParam, has_server_port),
    offsetof(TPbNvrSysUpgradeServerParam, server_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_upgrade_server_param__field_indices_by_name[] = {
  0,   /* field[0] = auto_upgrade */
  1,   /* field[1] = server_ip */
  2,   /* field[2] = server_port */
};
static const ProtobufCIntRange tpb_nvr_sys_upgrade_server_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_upgrade_server_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysUpgradeServerParam",
  "TPbNvrSysUpgradeServerParam",
  "TPbNvrSysUpgradeServerParam",
  "",
  sizeof(TPbNvrSysUpgradeServerParam),
  3,
  tpb_nvr_sys_upgrade_server_param__field_descriptors,
  tpb_nvr_sys_upgrade_server_param__field_indices_by_name,
  1,  tpb_nvr_sys_upgrade_server_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_upgrade_server_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_vehicle_param__field_descriptors[8] =
{
  {
    "car_plate_num_len",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysVehicleParam, has_car_plate_num_len),
    offsetof(TPbNvrSysVehicleParam, car_plate_num_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "car_plate_num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSysVehicleParam, has_car_plate_num),
    offsetof(TPbNvrSysVehicleParam, car_plate_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "shutdown_poe_sup",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysVehicleParam, has_shutdown_poe_sup),
    offsetof(TPbNvrSysVehicleParam, shutdown_poe_sup),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sync_lable",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysVehicleParam, has_sync_lable),
    offsetof(TPbNvrSysVehicleParam, sync_lable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "poe_power_enable",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysVehicleParam, has_poe_power_enable),
    offsetof(TPbNvrSysVehicleParam, poe_power_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "flameout_alarm_enable",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysVehicleParam, has_flameout_alarm_enable),
    offsetof(TPbNvrSysVehicleParam, flameout_alarm_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptzout_power_enable",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysVehicleParam, has_ptzout_power_enable),
    offsetof(TPbNvrSysVehicleParam, ptzout_power_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "close_ptzout_power_enable",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysVehicleParam, has_close_ptzout_power_enable),
    offsetof(TPbNvrSysVehicleParam, close_ptzout_power_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_vehicle_param__field_indices_by_name[] = {
  1,   /* field[1] = car_plate_num */
  0,   /* field[0] = car_plate_num_len */
  7,   /* field[7] = close_ptzout_power_enable */
  5,   /* field[5] = flameout_alarm_enable */
  4,   /* field[4] = poe_power_enable */
  6,   /* field[6] = ptzout_power_enable */
  2,   /* field[2] = shutdown_poe_sup */
  3,   /* field[3] = sync_lable */
};
static const ProtobufCIntRange tpb_nvr_sys_vehicle_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 8 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_vehicle_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysVehicleParam",
  "TPbNvrSysVehicleParam",
  "TPbNvrSysVehicleParam",
  "",
  sizeof(TPbNvrSysVehicleParam),
  8,
  tpb_nvr_sys_vehicle_param__field_descriptors,
  tpb_nvr_sys_vehicle_param__field_indices_by_name,
  1,  tpb_nvr_sys_vehicle_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_vehicle_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_message_submit_param__field_descriptors[5] =
{
  {
    "upload_plat_form_gps",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysMessageSubmitParam, has_upload_plat_form_gps),
    offsetof(TPbNvrSysMessageSubmitParam, upload_plat_form_gps),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upload_pubsec",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysMessageSubmitParam, n_upload_pubsec),
    offsetof(TPbNvrSysMessageSubmitParam, upload_pubsec),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nty_ipc_gps",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysMessageSubmitParam, has_nty_ipc_gps),
    offsetof(TPbNvrSysMessageSubmitParam, nty_ipc_gps),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "send_gps_interval",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysMessageSubmitParam, has_send_gps_interval),
    offsetof(TPbNvrSysMessageSubmitParam, send_gps_interval),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nty_ipc_net",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysMessageSubmitParam, has_nty_ipc_net),
    offsetof(TPbNvrSysMessageSubmitParam, nty_ipc_net),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_message_submit_param__field_indices_by_name[] = {
  2,   /* field[2] = nty_ipc_gps */
  4,   /* field[4] = nty_ipc_net */
  3,   /* field[3] = send_gps_interval */
  0,   /* field[0] = upload_plat_form_gps */
  1,   /* field[1] = upload_pubsec */
};
static const ProtobufCIntRange tpb_nvr_sys_message_submit_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_message_submit_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysMessageSubmitParam",
  "TPbNvrSysMessageSubmitParam",
  "TPbNvrSysMessageSubmitParam",
  "",
  sizeof(TPbNvrSysMessageSubmitParam),
  5,
  tpb_nvr_sys_message_submit_param__field_descriptors,
  tpb_nvr_sys_message_submit_param__field_indices_by_name,
  1,  tpb_nvr_sys_message_submit_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_message_submit_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_ball_ctr_matin_param__field_descriptors[3] =
{
  {
    "shut_down_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrBallCtrMatinParam, has_shut_down_enable),
    offsetof(TPbNvrBallCtrMatinParam, shut_down_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "light_enable",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrBallCtrMatinParam, has_light_enable),
    offsetof(TPbNvrBallCtrMatinParam, light_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vehicle_mode_enable",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrBallCtrMatinParam, has_vehicle_mode_enable),
    offsetof(TPbNvrBallCtrMatinParam, vehicle_mode_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ball_ctr_matin_param__field_indices_by_name[] = {
  1,   /* field[1] = light_enable */
  0,   /* field[0] = shut_down_enable */
  2,   /* field[2] = vehicle_mode_enable */
};
static const ProtobufCIntRange tpb_nvr_ball_ctr_matin_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_ball_ctr_matin_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrBallCtrMatinParam",
  "TPbNvrBallCtrMatinParam",
  "TPbNvrBallCtrMatinParam",
  "",
  sizeof(TPbNvrBallCtrMatinParam),
  3,
  tpb_nvr_ball_ctr_matin_param__field_descriptors,
  tpb_nvr_ball_ctr_matin_param__field_indices_by_name,
  1,  tpb_nvr_ball_ctr_matin_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ball_ctr_matin_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_enc_chn_switch_info__field_descriptors[1] =
{
  {
    "enc_id_switch",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysEncChnSwitchInfo, n_enc_id_switch),
    offsetof(TPbNvrSysEncChnSwitchInfo, enc_id_switch),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_enc_chn_switch_info__field_indices_by_name[] = {
  0,   /* field[0] = enc_id_switch */
};
static const ProtobufCIntRange tpb_nvr_sys_enc_chn_switch_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_enc_chn_switch_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysEncChnSwitchInfo",
  "TPbNvrSysEncChnSwitchInfo",
  "TPbNvrSysEncChnSwitchInfo",
  "",
  sizeof(TPbNvrSysEncChnSwitchInfo),
  1,
  tpb_nvr_sys_enc_chn_switch_info__field_descriptors,
  tpb_nvr_sys_enc_chn_switch_info__field_indices_by_name,
  1,  tpb_nvr_sys_enc_chn_switch_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_enc_chn_switch_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_lan_enc_id_bind_param__field_descriptors[1] =
{
  {
    "eth_info_list",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSysLanEncIdBindParam, n_eth_info_list),
    offsetof(TPbNvrSysLanEncIdBindParam, eth_info_list),
    &tpb_nvr_sys_enc_chn_switch_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_lan_enc_id_bind_param__field_indices_by_name[] = {
  0,   /* field[0] = eth_info_list */
};
static const ProtobufCIntRange tpb_nvr_sys_lan_enc_id_bind_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_lan_enc_id_bind_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysLanEncIdBindParam",
  "TPbNvrSysLanEncIdBindParam",
  "TPbNvrSysLanEncIdBindParam",
  "",
  sizeof(TPbNvrSysLanEncIdBindParam),
  1,
  tpb_nvr_sys_lan_enc_id_bind_param__field_descriptors,
  tpb_nvr_sys_lan_enc_id_bind_param__field_indices_by_name,
  1,  tpb_nvr_sys_lan_enc_id_bind_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_lan_enc_id_bind_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_cfg__field_descriptors[12] =
{
  {
    "nvr_sys_param",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, nvr_sys_param),
    &tpb_nvr_sys_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_sys_zero_chn_enc_param",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, nvr_sys_zero_chn_enc_param),
    &tpb_nvr_sys_zero_chn_enc_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_time_param",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, nvr_time_param),
    &tpb_nvr_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_sys_advance_param",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, nvr_sys_advance_param),
    &tpb_nvr_sys_advance_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_call_binding_param",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, aud_call_binding_param),
    &tpb_nvr_sys_aud_call_binding_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "manual_event_param",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, manual_event_param),
    &tpb_nvr_sys_manual_event_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "geography_pos_param",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, geography_pos_param),
    &tpb_nvr_sys_geography_pos_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upgrade_server_param",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, upgrade_server_param),
    &tpb_nvr_sys_upgrade_server_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vehicle_param",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, vehicle_param),
    &tpb_nvr_sys_vehicle_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "message_submit_param",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, message_submit_param),
    &tpb_nvr_sys_message_submit_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ball_ctr_matin_param",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, ball_ctr_matin_param),
    &tpb_nvr_ball_ctr_matin_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "lan_encid_bind_param",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysCfg, lan_encid_bind_param),
    &tpb_nvr_sys_lan_enc_id_bind_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_cfg__field_indices_by_name[] = {
  4,   /* field[4] = aud_call_binding_param */
  10,   /* field[10] = ball_ctr_matin_param */
  6,   /* field[6] = geography_pos_param */
  11,   /* field[11] = lan_encid_bind_param */
  5,   /* field[5] = manual_event_param */
  9,   /* field[9] = message_submit_param */
  3,   /* field[3] = nvr_sys_advance_param */
  0,   /* field[0] = nvr_sys_param */
  1,   /* field[1] = nvr_sys_zero_chn_enc_param */
  2,   /* field[2] = nvr_time_param */
  7,   /* field[7] = upgrade_server_param */
  8,   /* field[8] = vehicle_param */
};
static const ProtobufCIntRange tpb_nvr_sys_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 12 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysCfg",
  "TPbNvrSysCfg",
  "TPbNvrSysCfg",
  "",
  sizeof(TPbNvrSysCfg),
  12,
  tpb_nvr_sys_cfg__field_descriptors,
  tpb_nvr_sys_cfg__field_indices_by_name,
  1,  tpb_nvr_sys_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_auto_day_param__field_descriptors[3] =
{
  {
    "hour",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAutoDayParam, has_hour),
    offsetof(TPbNvrSysAutoDayParam, hour),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "minute",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAutoDayParam, has_minute),
    offsetof(TPbNvrSysAutoDayParam, minute),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "second",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAutoDayParam, has_second),
    offsetof(TPbNvrSysAutoDayParam, second),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_auto_day_param__field_indices_by_name[] = {
  0,   /* field[0] = hour */
  1,   /* field[1] = minute */
  2,   /* field[2] = second */
};
static const ProtobufCIntRange tpb_nvr_sys_auto_day_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_auto_day_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysAutoDayParam",
  "TPbNvrSysAutoDayParam",
  "TPbNvrSysAutoDayParam",
  "",
  sizeof(TPbNvrSysAutoDayParam),
  3,
  tpb_nvr_sys_auto_day_param__field_descriptors,
  tpb_nvr_sys_auto_day_param__field_indices_by_name,
  1,  tpb_nvr_sys_auto_day_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_auto_day_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_auto_week_param__field_descriptors[2] =
{
  {
    "weekday",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAutoWeekParam, has_weekday),
    offsetof(TPbNvrSysAutoWeekParam, weekday),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_param",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysAutoWeekParam, day_param),
    &tpb_nvr_sys_auto_day_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_auto_week_param__field_indices_by_name[] = {
  1,   /* field[1] = day_param */
  0,   /* field[0] = weekday */
};
static const ProtobufCIntRange tpb_nvr_sys_auto_week_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_auto_week_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysAutoWeekParam",
  "TPbNvrSysAutoWeekParam",
  "TPbNvrSysAutoWeekParam",
  "",
  sizeof(TPbNvrSysAutoWeekParam),
  2,
  tpb_nvr_sys_auto_week_param__field_descriptors,
  tpb_nvr_sys_auto_week_param__field_indices_by_name,
  1,  tpb_nvr_sys_auto_week_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_auto_week_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_auto_month_param__field_descriptors[3] =
{
  {
    "month",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAutoMonthParam, has_month),
    offsetof(TPbNvrSysAutoMonthParam, month),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_param",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysAutoMonthParam, day_param),
    &tpb_nvr_sys_auto_day_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day",
    21,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysAutoMonthParam, has_day),
    offsetof(TPbNvrSysAutoMonthParam, day),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_auto_month_param__field_indices_by_name[] = {
  2,   /* field[2] = day */
  1,   /* field[1] = day_param */
  0,   /* field[0] = month */
};
static const ProtobufCIntRange tpb_nvr_sys_auto_month_param__number_ranges[3 + 1] =
{
  { 1, 0 },
  { 3, 1 },
  { 21, 2 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_auto_month_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysAutoMonthParam",
  "TPbNvrSysAutoMonthParam",
  "TPbNvrSysAutoMonthParam",
  "",
  sizeof(TPbNvrSysAutoMonthParam),
  3,
  tpb_nvr_sys_auto_month_param__field_descriptors,
  tpb_nvr_sys_auto_month_param__field_indices_by_name,
  3,  tpb_nvr_sys_auto_month_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_auto_month_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_auto_reboot_param__field_descriptors[4] =
{
  {
    "auto_reboot_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrAutoRebootParam, has_auto_reboot_type),
    offsetof(TPbNvrAutoRebootParam, auto_reboot_type),
    &em_pb_nvr_sys_auto_reboot_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_param",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrAutoRebootParam, day_param),
    &tpb_nvr_sys_auto_day_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "week_param",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrAutoRebootParam, week_param),
    &tpb_nvr_sys_auto_week_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "month_param",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrAutoRebootParam, month_param),
    &tpb_nvr_sys_auto_month_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_auto_reboot_param__field_indices_by_name[] = {
  0,   /* field[0] = auto_reboot_type */
  1,   /* field[1] = day_param */
  3,   /* field[3] = month_param */
  2,   /* field[2] = week_param */
};
static const ProtobufCIntRange tpb_nvr_auto_reboot_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_auto_reboot_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAutoRebootParam",
  "TPbNvrAutoRebootParam",
  "TPbNvrAutoRebootParam",
  "",
  sizeof(TPbNvrAutoRebootParam),
  4,
  tpb_nvr_auto_reboot_param__field_descriptors,
  tpb_nvr_auto_reboot_param__field_indices_by_name,
  1,  tpb_nvr_auto_reboot_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_auto_reboot_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_low_power_sleep_info__field_descriptors[2] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysLowPowerSleepInfo, has_enable),
    offsetof(TPbNvrSysLowPowerSleepInfo, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sleep_power_threshold",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysLowPowerSleepInfo, has_sleep_power_threshold),
    offsetof(TPbNvrSysLowPowerSleepInfo, sleep_power_threshold),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_low_power_sleep_info__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  1,   /* field[1] = sleep_power_threshold */
};
static const ProtobufCIntRange tpb_nvr_sys_low_power_sleep_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_low_power_sleep_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysLowPowerSleepInfo",
  "TPbNvrSysLowPowerSleepInfo",
  "TPbNvrSysLowPowerSleepInfo",
  "",
  sizeof(TPbNvrSysLowPowerSleepInfo),
  2,
  tpb_nvr_sys_low_power_sleep_info__field_descriptors,
  tpb_nvr_sys_low_power_sleep_info__field_indices_by_name,
  1,  tpb_nvr_sys_low_power_sleep_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_low_power_sleep_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_time_seg__field_descriptors[8] =
{
  {
    "start_time",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysTimeSeg, has_start_time),
    offsetof(TPbNvrSysTimeSeg, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysTimeSeg, has_end_time),
    offsetof(TPbNvrSysTimeSeg, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sleep_snap_mode",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrSysTimeSeg, has_sleep_snap_mode),
    offsetof(TPbNvrSysTimeSeg, sleep_snap_mode),
    &em_pb_nvr_sleep_snap_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snap_time_interval",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysTimeSeg, has_snap_time_interval),
    offsetof(TPbNvrSysTimeSeg, snap_time_interval),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_ptz_mode",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrSysTimeSeg, has_link_ptz_mode),
    offsetof(TPbNvrSysTimeSeg, link_ptz_mode),
    &em_pb_nvr_link_ptzmode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_num",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysTimeSeg, has_preset_num),
    offsetof(TPbNvrSysTimeSeg, preset_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "path_num",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysTimeSeg, has_path_num),
    offsetof(TPbNvrSysTimeSeg, path_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upload_post_mode",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrSysTimeSeg, has_upload_post_mode),
    offsetof(TPbNvrSysTimeSeg, upload_post_mode),
    &em_pb_nvr_upload_post_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_time_seg__field_indices_by_name[] = {
  1,   /* field[1] = end_time */
  4,   /* field[4] = link_ptz_mode */
  6,   /* field[6] = path_num */
  5,   /* field[5] = preset_num */
  2,   /* field[2] = sleep_snap_mode */
  3,   /* field[3] = snap_time_interval */
  0,   /* field[0] = start_time */
  7,   /* field[7] = upload_post_mode */
};
static const ProtobufCIntRange tpb_nvr_sys_time_seg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 8 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_time_seg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysTimeSeg",
  "TPbNvrSysTimeSeg",
  "TPbNvrSysTimeSeg",
  "",
  sizeof(TPbNvrSysTimeSeg),
  8,
  tpb_nvr_sys_time_seg__field_descriptors,
  tpb_nvr_sys_time_seg__field_indices_by_name,
  1,  tpb_nvr_sys_time_seg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_time_seg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_sleep_time_of_day__field_descriptors[2] =
{
  {
    "num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysSleepTimeOfDay, has_num),
    offsetof(TPbNvrSysSleepTimeOfDay, num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sleep_time_of_day",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSysSleepTimeOfDay, n_sleep_time_of_day),
    offsetof(TPbNvrSysSleepTimeOfDay, sleep_time_of_day),
    &tpb_nvr_sys_time_seg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_sleep_time_of_day__field_indices_by_name[] = {
  0,   /* field[0] = num */
  1,   /* field[1] = sleep_time_of_day */
};
static const ProtobufCIntRange tpb_nvr_sys_sleep_time_of_day__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_sleep_time_of_day__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysSleepTimeOfDay",
  "TPbNvrSysSleepTimeOfDay",
  "TPbNvrSysSleepTimeOfDay",
  "",
  sizeof(TPbNvrSysSleepTimeOfDay),
  2,
  tpb_nvr_sys_sleep_time_of_day__field_descriptors,
  tpb_nvr_sys_sleep_time_of_day__field_indices_by_name,
  1,  tpb_nvr_sys_sleep_time_of_day__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_sleep_time_of_day__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_timed_sleep_info__field_descriptors[2] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysTimedSleepInfo, has_enable),
    offsetof(TPbNvrSysTimedSleepInfo, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sleep_of_week",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSysTimedSleepInfo, n_sleep_of_week),
    offsetof(TPbNvrSysTimedSleepInfo, sleep_of_week),
    &tpb_nvr_sys_sleep_time_of_day__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_timed_sleep_info__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  1,   /* field[1] = sleep_of_week */
};
static const ProtobufCIntRange tpb_nvr_sys_timed_sleep_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_timed_sleep_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysTimedSleepInfo",
  "TPbNvrSysTimedSleepInfo",
  "TPbNvrSysTimedSleepInfo",
  "",
  sizeof(TPbNvrSysTimedSleepInfo),
  2,
  tpb_nvr_sys_timed_sleep_info__field_descriptors,
  tpb_nvr_sys_timed_sleep_info__field_indices_by_name,
  1,  tpb_nvr_sys_timed_sleep_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_timed_sleep_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_sleep_of_mode_info__field_descriptors[2] =
{
  {
    "low_power_sleep",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysSleepOfModeInfo, low_power_sleep),
    &tpb_nvr_sys_low_power_sleep_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "timed_sleep",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysSleepOfModeInfo, timed_sleep),
    &tpb_nvr_sys_timed_sleep_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_sleep_of_mode_info__field_indices_by_name[] = {
  0,   /* field[0] = low_power_sleep */
  1,   /* field[1] = timed_sleep */
};
static const ProtobufCIntRange tpb_nvr_sys_sleep_of_mode_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_sleep_of_mode_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysSleepOfModeInfo",
  "TPbNvrSysSleepOfModeInfo",
  "TPbNvrSysSleepOfModeInfo",
  "",
  sizeof(TPbNvrSysSleepOfModeInfo),
  2,
  tpb_nvr_sys_sleep_of_mode_info__field_descriptors,
  tpb_nvr_sys_sleep_of_mode_info__field_indices_by_name,
  1,  tpb_nvr_sys_sleep_of_mode_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_sleep_of_mode_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_sleep_info__field_descriptors[2] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysSleepInfo, has_enable),
    offsetof(TPbNvrSysSleepInfo, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "delay_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSysSleepInfo, has_delay_time),
    offsetof(TPbNvrSysSleepInfo, delay_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_sleep_info__field_indices_by_name[] = {
  1,   /* field[1] = delay_time */
  0,   /* field[0] = enable */
};
static const ProtobufCIntRange tpb_nvr_sys_sleep_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_sleep_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysSleepInfo",
  "TPbNvrSysSleepInfo",
  "TPbNvrSysSleepInfo",
  "",
  sizeof(TPbNvrSysSleepInfo),
  2,
  tpb_nvr_sys_sleep_info__field_descriptors,
  tpb_nvr_sys_sleep_info__field_indices_by_name,
  1,  tpb_nvr_sys_sleep_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_sleep_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sys_power_waste_mode_info__field_descriptors[4] =
{
  {
    "power_waste_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrSysPowerWasteModeInfo, has_power_waste_mode),
    offsetof(TPbNvrSysPowerWasteModeInfo, power_waste_mode),
    &em_pb_nvr_sys_power_waste_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "full_waste_sleep",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysPowerWasteModeInfo, full_waste_sleep),
    &tpb_nvr_sys_sleep_of_mode_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "low_waste_sleep",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysPowerWasteModeInfo, low_waste_sleep),
    &tpb_nvr_sys_sleep_of_mode_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sleep_info",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSysPowerWasteModeInfo, sleep_info),
    &tpb_nvr_sys_sleep_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sys_power_waste_mode_info__field_indices_by_name[] = {
  1,   /* field[1] = full_waste_sleep */
  2,   /* field[2] = low_waste_sleep */
  0,   /* field[0] = power_waste_mode */
  3,   /* field[3] = sleep_info */
};
static const ProtobufCIntRange tpb_nvr_sys_power_waste_mode_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_sys_power_waste_mode_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSysPowerWasteModeInfo",
  "TPbNvrSysPowerWasteModeInfo",
  "TPbNvrSysPowerWasteModeInfo",
  "",
  sizeof(TPbNvrSysPowerWasteModeInfo),
  4,
  tpb_nvr_sys_power_waste_mode_info__field_descriptors,
  tpb_nvr_sys_power_waste_mode_info__field_indices_by_name,
  1,  tpb_nvr_sys_power_waste_mode_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sys_power_waste_mode_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue em_pb_nvr_sys_synctime_type__enum_values_by_number[2] =
{
  { "NTP", "EM_PB_NVR_SYS_SYNCTIME_TYPE__NTP", 0 },
  { "PROTOCOL", "EM_PB_NVR_SYS_SYNCTIME_TYPE__PROTOCOL", 1 },
};
static const ProtobufCIntRange em_pb_nvr_sys_synctime_type__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_sys_synctime_type__enum_values_by_name[2] =
{
  { "NTP", 0 },
  { "PROTOCOL", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_sys_synctime_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrSysSynctimeType",
  "EmPbNvrSysSynctimeType",
  "EmPbNvrSysSynctimeType",
  "",
  2,
  em_pb_nvr_sys_synctime_type__enum_values_by_number,
  2,
  em_pb_nvr_sys_synctime_type__enum_values_by_name,
  1,
  em_pb_nvr_sys_synctime_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_sys_ptp_clock_mode__enum_values_by_number[2] =
{
  { "PB_PTP_CLOCk_MODE_MASTER", "EM_PB_NVR_SYS_PTP_CLOCK_MODE__PB_PTP_CLOCk_MODE_MASTER", 0 },
  { "PB_PTP_CLOCK_MODE_SLAVE", "EM_PB_NVR_SYS_PTP_CLOCK_MODE__PB_PTP_CLOCK_MODE_SLAVE", 1 },
};
static const ProtobufCIntRange em_pb_nvr_sys_ptp_clock_mode__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_sys_ptp_clock_mode__enum_values_by_name[2] =
{
  { "PB_PTP_CLOCK_MODE_SLAVE", 1 },
  { "PB_PTP_CLOCk_MODE_MASTER", 0 },
};
const ProtobufCEnumDescriptor em_pb_nvr_sys_ptp_clock_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrSysPtpClockMode",
  "EmPbNvrSysPtpClockMode",
  "EmPbNvrSysPtpClockMode",
  "",
  2,
  em_pb_nvr_sys_ptp_clock_mode__enum_values_by_number,
  2,
  em_pb_nvr_sys_ptp_clock_mode__enum_values_by_name,
  1,
  em_pb_nvr_sys_ptp_clock_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_sys_ptp_clock_type__enum_values_by_number[2] =
{
  { "PB_PTP_CLOCk_TYPE_H", "EM_PB_NVR_SYS_PTP_CLOCK_TYPE__PB_PTP_CLOCk_TYPE_H", 0 },
  { "PB_PTP_CLOCk_TYPE_S", "EM_PB_NVR_SYS_PTP_CLOCK_TYPE__PB_PTP_CLOCk_TYPE_S", 1 },
};
static const ProtobufCIntRange em_pb_nvr_sys_ptp_clock_type__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_sys_ptp_clock_type__enum_values_by_name[2] =
{
  { "PB_PTP_CLOCk_TYPE_H", 0 },
  { "PB_PTP_CLOCk_TYPE_S", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_sys_ptp_clock_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrSysPtpClockType",
  "EmPbNvrSysPtpClockType",
  "EmPbNvrSysPtpClockType",
  "",
  2,
  em_pb_nvr_sys_ptp_clock_type__enum_values_by_number,
  2,
  em_pb_nvr_sys_ptp_clock_type__enum_values_by_name,
  1,
  em_pb_nvr_sys_ptp_clock_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_sys_aud_call_enc_type__enum_values_by_number[2] =
{
  { "PCMA", "EM_PB_NVR_SYS_AUD_CALL_ENC_TYPE__PCMA", 1 },
  { "ADPCM", "EM_PB_NVR_SYS_AUD_CALL_ENC_TYPE__ADPCM", 2 },
};
static const ProtobufCIntRange em_pb_nvr_sys_aud_call_enc_type__value_ranges[] = {
{1, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_sys_aud_call_enc_type__enum_values_by_name[2] =
{
  { "ADPCM", 1 },
  { "PCMA", 0 },
};
const ProtobufCEnumDescriptor em_pb_nvr_sys_aud_call_enc_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrSysAudCallEncType",
  "EmPbNvrSysAudCallEncType",
  "EmPbNvrSysAudCallEncType",
  "",
  2,
  em_pb_nvr_sys_aud_call_enc_type__enum_values_by_number,
  2,
  em_pb_nvr_sys_aud_call_enc_type__enum_values_by_name,
  1,
  em_pb_nvr_sys_aud_call_enc_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_sys_auto_reboot_type__enum_values_by_number[4] =
{
  { "DISABLE", "EM_PB_NVR_SYS_AUTO_REBOOT_TYPE__DISABLE", 0 },
  { "DAY", "EM_PB_NVR_SYS_AUTO_REBOOT_TYPE__DAY", 1 },
  { "WEEK", "EM_PB_NVR_SYS_AUTO_REBOOT_TYPE__WEEK", 2 },
  { "MONTH", "EM_PB_NVR_SYS_AUTO_REBOOT_TYPE__MONTH", 3 },
};
static const ProtobufCIntRange em_pb_nvr_sys_auto_reboot_type__value_ranges[] = {
{0, 0},{0, 4}
};
static const ProtobufCEnumValueIndex em_pb_nvr_sys_auto_reboot_type__enum_values_by_name[4] =
{
  { "DAY", 1 },
  { "DISABLE", 0 },
  { "MONTH", 3 },
  { "WEEK", 2 },
};
const ProtobufCEnumDescriptor em_pb_nvr_sys_auto_reboot_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrSysAutoRebootType",
  "EmPbNvrSysAutoRebootType",
  "EmPbNvrSysAutoRebootType",
  "",
  4,
  em_pb_nvr_sys_auto_reboot_type__enum_values_by_number,
  4,
  em_pb_nvr_sys_auto_reboot_type__enum_values_by_name,
  1,
  em_pb_nvr_sys_auto_reboot_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_sys_power_waste_mode__enum_values_by_number[2] =
{
  { "FULL_POWER_MODE", "EM_PB_NVR_SYS_POWER_WASTE_MODE__FULL_POWER_MODE", 1 },
  { "LOW_POWER_MODE", "EM_PB_NVR_SYS_POWER_WASTE_MODE__LOW_POWER_MODE", 2 },
};
static const ProtobufCIntRange em_pb_nvr_sys_power_waste_mode__value_ranges[] = {
{1, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_sys_power_waste_mode__enum_values_by_name[2] =
{
  { "FULL_POWER_MODE", 0 },
  { "LOW_POWER_MODE", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_sys_power_waste_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrSysPowerWasteMode",
  "EmPbNvrSysPowerWasteMode",
  "EmPbNvrSysPowerWasteMode",
  "",
  2,
  em_pb_nvr_sys_power_waste_mode__enum_values_by_number,
  2,
  em_pb_nvr_sys_power_waste_mode__enum_values_by_name,
  1,
  em_pb_nvr_sys_power_waste_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_sleep_snap_mode__enum_values_by_number[2] =
{
  { "SLEEP_SNAP_MODE_CLOSE", "EM_PB_NVR_SLEEP_SNAP_MODE__SLEEP_SNAP_MODE_CLOSE", 1 },
  { "SLEEP_SNAP_MODE_OPEN", "EM_PB_NVR_SLEEP_SNAP_MODE__SLEEP_SNAP_MODE_OPEN", 2 },
};
static const ProtobufCIntRange em_pb_nvr_sleep_snap_mode__value_ranges[] = {
{1, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_sleep_snap_mode__enum_values_by_name[2] =
{
  { "SLEEP_SNAP_MODE_CLOSE", 0 },
  { "SLEEP_SNAP_MODE_OPEN", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_sleep_snap_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrSleepSnapMode",
  "EmPbNvrSleepSnapMode",
  "EmPbNvrSleepSnapMode",
  "",
  2,
  em_pb_nvr_sleep_snap_mode__enum_values_by_number,
  2,
  em_pb_nvr_sleep_snap_mode__enum_values_by_name,
  1,
  em_pb_nvr_sleep_snap_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_link_ptzmode__enum_values_by_number[3] =
{
  { "LINK_PTZ_MODE_CLOSE", "EM_PB_NVR_LINK_PTZMODE__LINK_PTZ_MODE_CLOSE", 1 },
  { "LINK_PTZ_MODE_PRESET", "EM_PB_NVR_LINK_PTZMODE__LINK_PTZ_MODE_PRESET", 2 },
  { "LINK_PTZ_MODE_CRUISE", "EM_PB_NVR_LINK_PTZMODE__LINK_PTZ_MODE_CRUISE", 3 },
};
static const ProtobufCIntRange em_pb_nvr_link_ptzmode__value_ranges[] = {
{1, 0},{0, 3}
};
static const ProtobufCEnumValueIndex em_pb_nvr_link_ptzmode__enum_values_by_name[3] =
{
  { "LINK_PTZ_MODE_CLOSE", 0 },
  { "LINK_PTZ_MODE_CRUISE", 2 },
  { "LINK_PTZ_MODE_PRESET", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_link_ptzmode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrLinkPTZMode",
  "EmPbNvrLinkPTZMode",
  "EmPbNvrLinkPTZMode",
  "",
  3,
  em_pb_nvr_link_ptzmode__enum_values_by_number,
  3,
  em_pb_nvr_link_ptzmode__enum_values_by_name,
  1,
  em_pb_nvr_link_ptzmode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_upload_post_mode__enum_values_by_number[2] =
{
  { "UPLOAD_POST_MODE_CLOSE", "EM_PB_NVR_UPLOAD_POST_MODE__UPLOAD_POST_MODE_CLOSE", 1 },
  { "UPLOAD_POST_MODE_OPEN", "EM_PB_NVR_UPLOAD_POST_MODE__UPLOAD_POST_MODE_OPEN", 2 },
};
static const ProtobufCIntRange em_pb_nvr_upload_post_mode__value_ranges[] = {
{1, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_upload_post_mode__enum_values_by_name[2] =
{
  { "UPLOAD_POST_MODE_CLOSE", 0 },
  { "UPLOAD_POST_MODE_OPEN", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_upload_post_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrUploadPostMode",
  "EmPbNvrUploadPostMode",
  "EmPbNvrUploadPostMode",
  "",
  2,
  em_pb_nvr_upload_post_mode__enum_values_by_number,
  2,
  em_pb_nvr_upload_post_mode__enum_values_by_name,
  1,
  em_pb_nvr_upload_post_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
