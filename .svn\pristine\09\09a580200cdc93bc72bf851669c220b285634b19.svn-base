#ifndef _BITOPS_H
#define _BITOPS_H
#include <sys/cdefs.h>
//#include <asm/bitsperlong.h>

#ifndef __BITS_PER_LONG
#ifdef __x86_64__                                                                                    
# define __BITS_PER_LONG 64
#else
# define __BITS_PER_LONG 32
#endif
#endif

#ifdef __x86_64__  

#define LOCK_PREFIX_HERE \
		".section .smp_locks,\"a\"\n"	\
		".balign 4\n"			\
		".long 671f - .\n" /* offset */	\
		".previous\n"			\
		"671:"

#define LOCK_PREFIX LOCK_PREFIX_HERE "\n\tlock; "
#if __GNUC__ < 4 || (__GNUC__ == 4 && __GNUC_MINOR__ < 1)
/* Technically wrong, but this avoids compilation errors on some gcc
 *    versions. */
#define BITOP_ADDR(x) "=m" (*(volatile long *) (x))
#else
#define BITOP_ADDR(x) "+m" (*(volatile long *) (x))
#endif

#define ADDR				BITOP_ADDR(addr)

/*
 *  * We do the locked ops that don't return the old value as
 *   * a mask operation on a byte.
 *    */
#define IS_IMMEDIATE(nr)		(__builtin_constant_p(nr))
#define CONST_MASK_ADDR(nr, addr)	BITOP_ADDR((void *)(addr) + ((nr)>>3))
#define CONST_MASK(nr)			(1 << ((nr) & 7))

/**
 *  * set_bit - Atomically set a bit in memory
 *   * @nr: the bit to set
 *    * @addr: the address to start counting from
 *     *
 *      * This function is atomic and may not be reordered.  See __set_bit()
 *       * if you do not require the atomic guarantees.
 *        *
 *         * Note: there are no guarantees that this function will not be reordered
 *          * on non x86 architectures, so if you are writing portable code,
 *           * make sure not to rely on its reordering guarantees.
 *            *
 *             * Note that @nr may be almost arbitrarily large; this function is not
 *              * restricted to acting on a single-word quantity.
 *               */
static __always_inline void
__set_bit(unsigned int nr, volatile unsigned long *addr)
{
	if (IS_IMMEDIATE(nr)) {
		asm volatile(LOCK_PREFIX "orb %1,%0"
			: CONST_MASK_ADDR(nr, addr)
			: "iq" ((u8)CONST_MASK(nr))
			: "memory");
	} else {
		asm volatile(LOCK_PREFIX "bts %1,%0"
			: BITOP_ADDR(addr) : "Ir" (nr) : "memory");
	}
}
/**
 *  * clear_bit - Clears a bit in memory
 *   * @nr: Bit to clear
 *    * @addr: Address to start counting from
 *     *
 *      * clear_bit() is atomic and may not be reordered.  However, it does
 *       * not contain a memory barrier, so if it is used for locking purposes,
 *        * you should call smp_mb__before_clear_bit() and/or smp_mb__after_clear_bit()
 *         * in order to ensure changes are visible on other processors.
 *          */
static __always_inline void
__clear_bit(int nr, volatile unsigned long *addr)
{
	if (IS_IMMEDIATE(nr)) {
		asm volatile(LOCK_PREFIX "andb %1,%0"
			: CONST_MASK_ADDR(nr, addr)
			: "iq" ((u8)~CONST_MASK(nr)));
	} else {
		asm volatile(LOCK_PREFIX "btr %1,%0"
			: BITOP_ADDR(addr)
			: "Ir" (nr));
	}
}
static __always_inline int constant_test_bit(unsigned int nr, const volatile unsigned long *addr)
{
	return ((1UL << (nr % __BITS_PER_LONG)) &
		(addr[nr / __BITS_PER_LONG])) != 0;
}

static inline int variable_test_bit(int nr, volatile const unsigned long *addr)
{
	int oldbit;

	asm volatile("bt %2,%1\n\t"
		     "sbb %0,%0"
		     : "=r" (oldbit)
		     : "m" (*(unsigned long *)addr), "Ir" (nr));

	return oldbit;
}
#define __test_bit(nr, addr)			\
	(__builtin_constant_p((nr))		\
	 ? constant_test_bit((nr), (addr))	\
	 : variable_test_bit((nr), (addr)))
static inline int test_bit(unsigned int nr, volatile const void *addr)
{
	return __test_bit(nr, (volatile const unsigned long *)addr);
}
static inline void set_bit(unsigned int nr, volatile void *addr)
{
	__set_bit(nr, (volatile unsigned long *)addr);
}
static inline void clear_bit(unsigned int nr,volatile void *addr)
{
	__clear_bit(nr, (volatile unsigned long *)addr);
}

#else

static inline int test_bit(unsigned int nr, volatile const void *addr)
{
        unsigned int *p; 

        if(nr < 32){
                return (*(volatile unsigned long *)addr & (1 << nr)) != 0;
        }   
        else{
                p = (unsigned int *)addr;
#if __BYTE_ORDER == __LITTLE_ENDIAN
                return (*(p + 1) & (1 << (nr - 32))) != 0;
#else
                return (*(p - 1) & (1 << (nr - 32))) != 0;
#endif
        }   
}

static inline void set_bit(unsigned int nr, volatile void *addr)
{
        unsigned int *p; 

        if(nr < 32){
                *(volatile unsigned long *)addr |= (1 << nr);
        }   
        else{
                p = (unsigned int *)addr;    
#if __BYTE_ORDER == __LITTLE_ENDIAN
                *(p + 1) |= (1 << (nr - 32));
#else
                *(p - 1) |= (1 << (nr - 32));
#endif
        }   
}

static inline void clear_bit(unsigned int nr, volatile void *addr)
{
        unsigned int *p;

        if(nr < 32){
                *(volatile unsigned long *)addr &= ~(1 << nr);
        }
        else{
                p = (unsigned int *)addr;
#if __BYTE_ORDER == __LITTLE_ENDIAN
                *(p + 1) &= ~(1 << (nr - 32));
#else
                *(p - 1) &= ~(1 << (nr - 32));
#endif
        }
}

#endif

#endif 
