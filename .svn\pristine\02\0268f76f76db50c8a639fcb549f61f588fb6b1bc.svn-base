

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
DO_UPX = 0
LINUX_COMPILER = _SSC339G_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
CFLAGS += -D__MRTC__
CFLAGS += -D_SSC339G_
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/kdssl-ext
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/nvrgnss
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/capptz
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/webrtc
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g/appcltlib

LIBS +=	nvrcfg nvrlog nvrcap nvrcustcap nvrusrmgr nvrnetwork nvrsys nvrproto protobufc sqlite3wrapper malloc debuglog netcbb mbnet  \
    ddnsc ddnscext upnpc drv pthread nvrgeo nvrcoi sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu nvrpcap nvrprobe\
    nvrftp httpclient mxml ghttp go rtspclient kdmposa osp netpacket kdmtsps kdvencrypt stdc++ nvrqueue ispctrl mediactrl nvralarm  \
    nvrdev nvrupgrade nvrcrc ftpc dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp airp kdvsys dl ais algctrl pubsecstack curl nvrunifiedlog\
    lwshelper\
    websockets\
	mixer_sigma_ssc339g_linux \
	spe_sigma_ssc339g_linux \
	videomanage_sigma_ssc339g_linux \
	audproc_plus_sigma_ssc339g_linux \
	speechexcit_sigma_ssc339g_linux \
	resample_sigma_ssc339g_linux \
	extexp_sigma_ssc339g_linux \
	audcodec_sigma_ssc339g_linux \
	g7221c_sigma_ssc339g_linux \
	adpcm_sigma_ssc339g_linux \
	g711_sigma_ssc339g_linux \
	g722_sigma_ssc339g_linux \
	aaclcenc_sigma_ssc339g_linux \
	aaclcdec_sigma_ssc339g_linux \
	g726_sigma_ssc339g_linux \
	aaclddec_sigma_ssc339g_linux \
	aacldenc_sigma_ssc339g_linux \
	amr_nb_sigma_ssc339g_linux \
	g719_sigma_ssc339g_linux \
	g728_sigma_ssc339g_linux \
	g729_sigma_ssc339g_linux \
	mp3dec_sigma_ssc339g_linux \
	mp3enc_sigma_ssc339g_linux \
	opus_sigma_ssc339g_linux \
	mp2_sigma_ssc339g_linux\
	aec_mulresample_sigma_ssc339g_linux \
	mi_ive \
	basicintelligent_sigmastar \
	asd_sigma_ssc339g_linux \
	smartcodec_sigmastar_linux \
	md5_sigmastar_linux \
	m \
	pcap\
	kdmssl\
	kdmcrypto\
	cjson\
	udm\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrdynamicplugin\
    nvrstitch\
	lwshelper\
	websockets\
	nvrmd5\
	kdssl-ext\
	SDEF\
	mediaswitch\
	mrtc\
	lcamclt\
	wmf\

ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif

# 非车载类设备,使用普通mediaswitch
#LDFLAGS += $(TOP)/../../10-common/lib/release/ssc339g/libmediaswitch.so

## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/hal/drvlib\
		$(CURDIR)/../../../10-common/include/hal/drvlib/system

#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif

CFLAGS += -g
CFLAGS += -D_NOTINITMPU_

INSTALL_APP_PATH := ../../../10-common/version/release/ssc339g/bin_ptzipc/
INSTALL_NO_UPX_APP_PATH := ../../../10-common/version/release/ssc339g/bin_ptzipc_noupx

include $(COMM_DIR)/common.mk


