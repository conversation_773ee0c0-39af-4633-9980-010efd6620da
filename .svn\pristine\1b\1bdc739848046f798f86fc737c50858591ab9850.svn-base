#include "mapp.h"
#include "mapperror.h"
#include "mappstream.h"
#include "mapprec.h"
#include "mappcfg.h"
#include "mappnotice.h"
#include "dmsrv.h"
#include "nvrsys.h"


#define MAPPASSERT(p)\
if (NULL == p)			\
{\
	MAPPERR("MAPP CMD ASSERT faild line:%d\n", __LINE__);	\
	return -1;									\
}\

TMappManager g_tMappMgr;
TNvrMappUidCfg g_tMappUidInfo;


static int g_SID = 0; ///<当前获取通道时候sid。注意是否要信号量控制？
static int g_nOtaAvIndex = -1;          ///<ota时avindex
static unsigned int g_nOtaFileSize = 0; ///<ota文件大小
static unsigned int g_nOneSize = 0;     ///<ota更新一次进度文件大小

static BOOL32 g_abTkApp[5] = {0,0,0,0,0};



static char g_achAccount[NEW_MAXSIZE_VIEWACC] = "admin";
static char g_achPwd[NEW_MAXSIZE_VIEWPWD] = "";//888888";
static char g_achAuthkey[IOTC_AUTH_KEY_LENGTH+1] = "QgxVhw21"; 


static void mapptest(int i,int j,char *p);
static void mappspecialtest(int i,int j,char *p);




///<init log
static int MappInitLog();

///<全局变量初始化
static int MappInit();
static void *MappListenThread(void);




static int MappChangePwdCB(int av_index, const char *account,const char *old_password,const char *new_password,const char *new_iotc_authkey);


static void Handle_IOCTRL_Cmd(int SID, int avIndex, char *buf, int type);

///<为每个通道（avindex）创建一个接收消息线程
static void *MappAVSrvStartRecvCmdThread(void *arg);

///<关闭session
//static void MappCloseSession(int nSId);
///<获取通道名称
static int MappGetChnName(int SID, int avIndex, char *buf);

///<获取通道数量
static int MappGetChnNum(int SID, int avIndex, char *buf);
///<get created av channel
static int MappGetCreatedAvChanel(int SID, int avIndex, char *buf);
///<create av channel
static int MappCreateAvChannel(int SID, int avIndex, char *buf);
///<get support event list capability
static int MappGetSupEventListCap(int SID, int avIndex, char *buf);
///<get event list ENABLE config
static int MappGetEventListEnableCfg(int SID, int avIndex, char *buf);
///<set event list ENABLE config
static int MappSetEventListEnableCfg(int SID, int avIndex, char *buf);



///<ptz控制
static void MappPtzCtrl(SMsgAVIoctrlPtzCmd *p);
static void MappDeviceAndHDInfoCmd(int SID, int avIndex, char *buf);
///<ota升级
static int MappOtaWriteCB(void *contents, int size, int nmemb, FILE *fd);
static void MappOtaCmd(int SID, int avIndex, char *buf);

///<event opt
#if 0
static int MappEventListOpt(u16 wChnId,ENUM_MAPPEVENTTYPE eInputType, int OptType, BOOL32 *pbEnable)
{
    ///<OptType 1:getcap ,2: getparam, 3:setparam,1,2时pbEnable是出参，3时pbEnable是出入参
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet = 0;  ///< 0:succ,1:param err; 2: cap nonsupport, -1:error
    EAppCltDeviceCapType eType = APP_CLT_DEV_CAP_BASE;   
    
    switch (eInputType)
    {
        case MAPP_EVENT_MOTIVE_ALARM:
        {
            if(OptType==1)
            {
                TAppCltDevCapAlmMd tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_CFG_MD;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapAlmMd));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = tParam.bSup;
                }
            }
            else
            {
                eType = NVR_PUI_MD_PARAM;
                TNvrPuiMDParam tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.bEnable != *pbEnable)
                    {
                        tParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.bEnable;
                    }
                }                
            }
        }            
        break;
        case MAPP_EVENT_COVER_IMAGE:
        {
            if(OptType==1)
            {
                TAppCltDevCapAlmOverlay tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_CFG_OVERLAY;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapAlmOverlay));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = tParam.bSup;
                }
            }
            else
            {
                eType = NVR_PUI_OVERLAY_PARAM;
                TNvrPuiOverlayAreaParam tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.bEnable != *pbEnable)
                    {
                        tParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.bEnable;
                    }
                }                
            }
        }            
        break;
        case MAPP_EVENT_TRIP_LINE_ALARM:///<拌线检测  Guard Line 
        {
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupCordon;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_CORDON;
                TNvrPuiCordonParam tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tCordonParam.bEnable != *pbEnable)
                    {
                        tParam.tCordonParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tCordonParam.bEnable;
                    }
                }                
            }
        }            
        break;
        case MAPP_EVENT_DEFOCUS_ALARM:   ///<虚焦检测  Defocus
        {
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupVFDetect;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_VIRTUAL_FOCUS;
                TNvrPuiCfgVirtualFocus tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tParam.tDeFocus.bEnable != *pbEnable)
                    {
                        tParam.tParam.tDeFocus.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tParam.tDeFocus.bEnable;
                    }
                }                
            }
        }            
        break;
    	case MAPP_EVENT_SCENE_CHANGE_ALARM:    ///<场景变换检测  Scene Change
    	{
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupSceneChange;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_SCENE_CHANGE;
                TNvrPuiSceneChange tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tParam.tSceneChg.bEnable != *pbEnable)
                    {
                        tParam.tParam.tSceneChg.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tParam.tSceneChg.bEnable;
                    }
                }                
            }
        }            
        break;
    	case MAPP_EVENT_REGION_INVASION_ALARM:   ///<区域入侵检测  Entry Guard Area
    	{
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupRegionAccess;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_AREA_ENTER;
                TNvrPuiAreaEnter tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tParam.bEnable != *pbEnable)
                    {
                        tParam.tParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tParam.bEnable;
                    }
                }                
            }
        }            
        break;
    	case MAPP_EVENT_REGION_LEAVING_ALARM:   ///<区域离开检测  Exit Guard Area
    	{
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupRegionDeparture;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_AREA_LEAVE;
                TNvrPuiAreaLeave tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tParam.bEnable != *pbEnable)
                    {
                        tParam.tParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tParam.bEnable;
                    }
                }                
            }
        }            
        break;
    	case MAPP_EVENT_OBJECT_TAKEN_ALARM:   ///<物品拿取检测  Object Removal
    	{
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupObjectPick;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_OBJECT_PICK;
                TNvrPuiCfgObjectPick tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tParam.bEnable != *pbEnable)
                    {
                        tParam.tParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tParam.bEnable;
                    }
                }                
            }
        }            
        break;
    	case MAPP_EVENT_OBJECT_LEFT_ALARM:   ///<物品遗留检测  Object Left
    	{
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupObjectLeft;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_OBJECT_LEFT;
                TNvrPuifgObjectLeft tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tParam.bEnable != *pbEnable)
                    {
                        tParam.tParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tParam.bEnable;
                    }
                }                
            }
        }            
        break;
    	case MAPP_EVENT_PEOPLE_GATHER_ALARM:   ///<人员聚集检测  Gathering
    	{
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupGather;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_PEOPLE_GATHER;
                TNvrPuiPeopleGather tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tParam.bEnable != *pbEnable)
                    {
                        tParam.tParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tParam.bEnable;
                    }
                }                
            }
        }            
        break;
    	case MAPP_EVENT_AUDIO_ABNORMAL_ALARM :   ///<声音异常      Audio Surge
    	{
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupAbnomalVoice;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_AUD_ABNORMAL;
                TNvrPuiAudAbnormal tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tParam.bEnable != *pbEnable)
                    {
                        tParam.tParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tParam.bEnable;
                    }
                }                
            }
        }            
        break;    	
    	case MAPP_EVENT_REGION_ENTER_ALARM:    ///<进入区域  Enter Guard Area
    	{
            if(OptType==1)
            {
                TAppCltDevCapIntel tParam;
                mzero(tParam);
                eType = APP_CLT_DEV_CAP_BASIC_INTEL;
                eRet = NvrPuiGetDevCap(wChnId,0,eType,&tParam,sizeof(TAppCltDevCapIntel));
                if(NVR_ERR__OK == eRet)
                {
                    *pbEnable = (BOOL32)tParam.tCap.bySupRegionIntrusion;
                }
            }
            else
            {
                eType = NVR_PUI_CFG_BASIC_INTEL_AREA_INVASION;
                TNvrPuiAreaInvasion tParam;
                mzero(tParam);
                
                eRet = NvrPuiGetDevParam(wChnId,0,eType,&tParam);
                if(NVR_ERR__OK == eRet)
                {
                    if(OptType==3 && tParam.tParam.bEnable != *pbEnable)
                    {
                        tParam.tParam.bEnable = *pbEnable;
                        eRet = NvrPuiSetDevParam(wChnId,0,eType,&tParam);
                    } 
                    else
                    {
                        *pbEnable = tParam.tParam.bEnable;
                    }
                }                
            }
        }            
        break;
        default:
            nRet = 1;
            break;            
    }

    if(NVR_ERR__CAP_NO_SUPPORT == eRet)
    {
        nRet = 2;
        if(OptType==1 || OptType==2)
        {
            *pbEnable = FALSE;
        }
        MAPPERR("no sup\n");
    }
    else if(NVR_ERR__OK != eRet) 
    {
        nRet = -1;
    }

    MAPPERR("chn:%d, inputtype:%d,opt:%d,enable:%d,eRet:%d,nRet:%d\n",wChnId,eInputType, OptType, *pbEnable,eRet,nRet);
    
    return nRet;
}

#endif



int MappInitLog()
{
    int nRet = 0;
    TDebugLogStillParam tStillParam;
    DebugLogRegist(DEBUG_LOG_MOD_UMSP,"[MAPP]","mapp");

    ///<默认记录文件， 
    DebugLogSetLogFileLen(DEBUG_LOG_MOD_UMSP, (u32)(1024*1024*2)); ///<1M
    memset(&tStillParam, 0, sizeof(tStillParam));
    DebugLogGetStillParam(&tStillParam);
    tStillParam.nModId = DEBUG_LOG_MOD_UMSP;
    snprintf(tStillParam.achLogFileName,sizeof(tStillParam.achLogFileName),"mapp");
    snprintf(tStillParam.achFileDir,sizeof(tStillParam.achFileDir),NVR_ROOT_PATH"/log/");
    DebugLogSetStillParam(&tStillParam);
    DebugLogSetToFileLevel(DEBUG_LOG_MOD_UMSP, LOG_LEVEL_IMP); 

    DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_UMSP,LOG_LEVEL_FILE,TRUE);
    //DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_UMSP,LOG_LEVEL_IMP,TRUE);

    MAPPERR("=============begin start mapp:%s,%s========\n",__DATE__, __TIME__ );
   
    
    ///<初始化iotc av log
    LogAttr logattr;
    mzero(logattr);

    logattr.path = NVR_ROOT_PATH"/log/mapp_iotc.log";
    logattr.file_max_size = 1024*2048; //2M
    logattr.file_max_count = 2;
    logattr.log_level = 0;
    nRet = IOTC_Set_Log_Attr(logattr);
    MAPPERR("IOTC_Set_Log_Attr ret:%d\n",nRet);

    logattr.path = NVR_ROOT_PATH"/log/mapp_av.log";
    nRet = AV_Set_Log_Attr(logattr);
    MAPPERR("AV_Set_Log_Attr ret:%d\n",nRet);

    logattr.path = NVR_ROOT_PATH"/log/mapp_tutk.log";
    nRet = TUTK_Set_Log_Attr(logattr);
    MAPPERR("AV_Set_Log_Attr ret:%d\n",nRet);

    

    return 0;
}
static int MappSpecialDealUid()
{
    int nRet = -1;
    TNvrSysDevInfo tDevInfo;
    
        
    if(0 != access("/usr/config/uid.ini", 0))
    {
        MAPPERR("uid no exist");
        return -1;
    }
    
    FILE* pfd = fopen("/usr/config/uid.ini", "r");
    if (pfd == NULL) 
    {
        MAPPERR("Failed to open file uid.ini");
        return -1;
    }
 
    char line[100];
    mzero(tDevInfo);        
    NvrSysGetDevInfo(&tDevInfo);
    while (fgets(line, sizeof(line), pfd)) 
    {
        char* semicolon = strchr(line, ';');
        if (semicolon != NULL) 
        {            
            *semicolon = 0;  
            char* pchKey = line;
            char* pchValue = semicolon + 1;            
            if (strcmp(pchKey, tDevInfo.achDevSerialNum) == 0) 
            {
                snprintf(g_tMappUidInfo.achUID,21,"%s",pchValue);
                g_tMappUidInfo.achUID[21] = '\0';
                mappspecialtest(8,8,g_tMappUidInfo.achUID);
                fclose(pfd);
                pfd = NULL;
                unlink("/usr/config/uid.ini");
                nRet = 0;
                break;
            }
        }
    }
    if(NULL != pfd)
    {
        fclose(pfd);
    }
    return nRet;
}

int MappReadUid()
{
    FILE *fd = NULL;
    int nLen = 0;
    
    if(0 == access(NVR_DEFAULT_MUID_CFG, 0))
    {
        fd = fopen(NVR_DEFAULT_MUID_CFG,"rb+");
        if(NULL == fd)
        {
            MAPPERR("fopen %s failed\n",NVR_DEFAULT_MUID_CFG);
            return -1;
        }
        else
        {
            fseek(fd, 0, SEEK_SET);
        	nLen = fread(&g_tMappUidInfo, 1,sizeof(g_tMappUidInfo), fd);
        	fclose(fd);
        	fd = NULL;
            MAPPERR("read succ ret:%d,%s\n",nLen,g_tMappUidInfo.achUID);
        }
    }
    else
    {
        MAPPERR("%s file not exist\n",NVR_DEFAULT_MUID_CFG);
        if(0 == access(NVR_DEFAULT_MUID_BK_CFG, 0))
        {
            fd = fopen(NVR_DEFAULT_MUID_BK_CFG,"rb+");
            if(NULL == fd)
            {
                MAPPERR("bk fopen %s failed\n",NVR_DEFAULT_MUID_BK_CFG);
                return -1;
            }
            else
            {
                fseek(fd, 0, SEEK_SET);
            	nLen = fread(&g_tMappUidInfo, 1,sizeof(g_tMappUidInfo), fd);
            	fclose(fd);
            	fd = NULL;
                MAPPERR("bk read succ ret:%d,%s\n",nLen,g_tMappUidInfo.achUID);
            }
            ///<写到主文件
            fd = fopen(NVR_DEFAULT_MUID_CFG,"wb+");
            if(NULL == fd)
            {
                MAPPERR("bk fopen %s failed\n",NVR_DEFAULT_MUID_CFG);
                return -1;
            }                            

            fwrite(&g_tMappUidInfo, sizeof(g_tMappUidInfo), 1, fd);
            
            if(fd!=NULL)
            {
                fclose(fd);
                fd = NULL;
            }
        }
        else
        {
            MAPPERR("%s file not exist\n",NVR_DEFAULT_MUID_BK_CFG);
            if (0 != MappSpecialDealUid())
            {
                g_tMappMgr.byLoginListenThdStatus = FALSE;
            }
        }
    } 
    return 0;
}
int MappInit()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet = 0;
    int i = 0;
    int j = 0;    
    TNvrCapPuiInfo tCap;
    TNvrCapSysBasic tCapSysBasic;
    TNvrCapHwCapInfo tHwCap;
    
    mzero(g_tMappMgr);
    mzero(g_tMappUidInfo);

   
    

    
    g_tMappMgr.byLoginListenThdStatus = 1;
    
    if( !OsApi_SemBCreate(&g_tMappMgr.hLoginSem) )
    {
	    MAPPERR("create g_tMappMgr.hLoginSem failed\n");
        return -1;
    }
    if( !OsApi_SemBCreate(&g_tMappMgr.hMgrSem) )
    {
	    MAPPERR("create g_tMappMgr.hMgrSem failed\n");
        return -1;
    } 
    if( !OsApi_SemBCreate(&g_tMappMgr.hMgrSidSem) )
    {
	    MAPPERR("create g_tMappMgr.hMgrSidSem failed\n");
        return -1;
    }
    
    if( !OsApi_SemBCreate(&g_tMappMgr.hChnAvSem) )
    {
	    MAPPERR("create g_tMappMgr.hChnAvSem failed\n");
        return -1;
    }
    MAPP_SEMTAKE(g_tMappMgr.hChnAvSem, MAPP_SEM_CHNAV);

    mzero(tHwCap);
    eRet = NvrCapGetCapParam(NVR_CAP_ID_HW,(void *)&tHwCap);
    if(eRet != NVR_ERR__OK)
    {
        MAPPERR("get hwcap failed:%d\n",eRet);
		return -1;
    }
    if(tHwCap.eDevType != NVR_DEV_TYPE_NVR)
    {
        g_tMappMgr.byIsIpc = 1;
    }
    

    mzero(tCap);
    eRet = NvrCapGetCapParam(NVR_CAP_ID_PUI,(void *)&tCap);
    if(eRet != NVR_ERR__OK)
    {
        MAPPERR("get cap failed:%d\n",eRet);
		return -1;
    }
    g_tMappMgr.nChnNum = tCap.wChnMaxNum;
    if(g_tMappMgr.nChnNum>= MAPP_MAX_CHN_NUM)
    {
        g_tMappMgr.nChnNum = MAPP_MAX_CHN_NUM;
    }

    
    mzero(tCapSysBasic);		
	eRet = NvrCapGetDefCapParam(NVR_CAP_SYS_CAP_ID_BASIC,&tCapSysBasic);
    if(eRet != NVR_ERR__OK)
    {
        MAPPERR("get sysbasic cap failed:%d\n",eRet);
		tCapSysBasic.bySupMaxMappCnctNum = 3;///<default 3
    }
    g_tMappMgr.bySupAppNum = tCapSysBasic.bySupMaxMappCnctNum;

    for(i = 0; i<g_tMappMgr.bySupAppNum;i++)
    {
        for(j = 0;j<MAPP_MAX_AVINDEX_NUM;j++)
        {
            g_tMappMgr.atSidInfo[i].atAvIndexinfo[j].byEncId = 1;
        }
    }
    MAPPERR("total chn num:%d,supapp:%u\n",g_tMappMgr.nChnNum,g_tMappMgr.bySupAppNum);

    MappReadUid();
    
    return nRet;
}

static void MappUsrChangeCB(const u8 *pbyUserName, const u32 dwUserNameLen, const ENvrUserCfgChangeType eChangeType, void *pCBParam)
{
	s32 nRet = 0;
	
	char achUtf8Name[NVR_USER_NAME_LEN_MAX*4+1] = "";     

    if(pbyUserName == NULL)
    {
        return;
    }

    nRet = CharConvConvertUnicodetoUtf8(pbyUserName, dwUserNameLen, achUtf8Name, sizeof(achUtf8Name));
    if(0 != nRet)
    {
        MAPPERR("name unicode to utf8 failed:%d\n",nRet);
    }
    else
    {
        if(strcmp(achUtf8Name, g_achAccount) != 0)
        {
            MAPPIMP("account not match\n");
            return;
        }
    }
    if(NVR_USER_PASS_MDY == eChangeType)
    {
        NVRSTATUS eRet = NVR_ERR__OK;
        TNvrUserInfo tUsrInfo;
        mzero(tUsrInfo);            
        tUsrInfo.dwUserNameLen = dwUserNameLen;
        memcpy(tUsrInfo.abyUserName,pbyUserName,dwUserNameLen);  

        MAPPERR("pwd change CB\n");

        eRet = NvrUserGetUserInfo(&tUsrInfo);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("NvrUserGetUserInfo failed:%d\n",eRet);
            return;
        }
        strcpy(g_achPwd, tUsrInfo.achPassWd);
    }
    
}

int MappExPwdAuthCB(const char *account, char *pwd, unsigned int pwd_buf_size)
{
    int nRet = 0;
    
    do 
    {
        if (strcmp(account, g_achAccount) != 0)
        {
            MAPPERR("account not match\n");
            nRet = -1;
            break;
        }
        if(strlen(g_achPwd) == 0)
        {
            NVRSTATUS eRet = NVR_ERR__OK;
            TNvrUserInfo tUsrInfo;
            mzero(tUsrInfo);            
            tUsrInfo.dwUserNameLen = sizeof(tUsrInfo.abyUserName);
            nRet = CharConvConvertUtf8toUnicode("admin", tUsrInfo.abyUserName, &tUsrInfo.dwUserNameLen);
            if(nRet != 0)
            {
                MAPPERR("admin to unicode failed:%d\n",nRet);
                break;
            }  

            eRet = NvrUserGetUserInfo(&tUsrInfo);
            if(NVR_ERR__OK != eRet)
            {
                MAPPERR("NvrUserGetUserInfo failed:%d\n",eRet);
                nRet = -1;
                break;
            }
            strcpy(g_achPwd, tUsrInfo.achPassWd);
            NvrUserCfgChangeRegisterCB(MappUsrChangeCB);
        }
        if (pwd_buf_size <= strlen(g_achPwd))
        {
            MAPPERR("pwd buf size len:(%d<=%d) is err\n",pwd_buf_size,strlen(g_achPwd));
            nRet = -1;
            break;
        }
        //暂时默认888888,用于测试
        //strcpy(pwd, "888888");
        strcpy(pwd, g_achPwd);
        MAPPDBG("pwd CB,%d-%d\n",pwd_buf_size,strlen(g_achPwd));
        
    }while(0);
	

	

	return nRet;
}

int MappChangePwdCB(  int av_index, const char *account,const char *old_password,const char *new_password,const char *new_iotc_authkey)
{
    MAPPERR("not support app modify pwd\n");
    return -1;

	if (strcmp(account, g_achAccount) != 0)
		return -1;
	if (strcmp(old_password, g_achPwd) != 0)
		return -1;

    // please make sure the maximum password size is enough
	if (strlen(new_password) > NEW_MAXSIZE_VIEWPWD)
		return -1;
	strcpy(g_achPwd, new_password);

	//auth key has been changed inside the SDK, please save it
    memcpy(g_achAuthkey, new_iotc_authkey, IOTC_AUTH_KEY_LENGTH);

    IOTC_Device_Update_Authkey(g_achAuthkey);
    ///<密码后续不可调试
    //MAPPDBG("%s,%s,%s\n",g_achAccount,g_achPwd,g_achAuthkey);
	return 0; //success
}
static void ExAbilityRequestFn(int av_index, avServSendAbility send_ability)
{
    /*char *buf = NULL;

    if (readAbility(gAbilityFile, &buf) > 0) {
        send_ability(av_index, (const unsigned char*)buf, (unsigned int)strlen(buf)+1);
        releaseAbility(&buf);
    } else {
        send_ability(av_index, (const unsigned char*)"NULL", 4);
    }*/
}



void Handle_IOCTRL_Cmd(int SID, int avIndex, char *buf, int type)
{
    int nRet = 0;
	MAPPERR("Handle CMD:SID:%d,avindex:%d,type:0x%X\n ",SID,avIndex,type);
    if(NULL == buf)
    {
        //buf是sdk回调固定地址，不为空
        MAPPERR("Handle CMD:SID:%d,avindex:%d,type:0x%X,buf:%p\n ",SID,avIndex,type,buf);
    }
	switch(type)
	{ 
        case IOTYPE_USER_IPCAM_GET_CHANNEL_NAME_REQ:
        {
            // tutk app 测试用
            g_abTkApp[SID]  = 1;
        }
        break;
        ///<获取设备通道数，0x0328
        case IOTYPE_USER_IPCAM_GETSUPPORTSTREAM_REQ:
        { 
            MappGetChnNum(SID, avIndex, buf);
        }
        break;
        ///<0x0322 获取图像质量
        case IOTYPE_USER_IPCAM_GETSTREAMCTRL_REQ:
        {
            MappGetStreamQuality(SID, avIndex, buf);            
        }
        break;
        ///<0x0320 设置图像质量
        case IOTYPE_USER_IPCAM_SETSTREAMCTRL_REQ:
        {
            MappSetStreamQuality(SID, avIndex, buf);
        }
        break;        
        ///<0x032A 获取音频格式
        case IOTYPE_USER_IPCAM_GETAUDIOOUTFORMAT_REQ:
		{
            u8 byAudEncNum = 0;
            u8 byAudDecNum = 0;
            int nChn = g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].nChnId;
            
            SMsgAVIoctrlGetAudioOutFormatReq *p = (SMsgAVIoctrlGetAudioOutFormatReq *)buf;
            SMsgAVIoctrlGetAudioOutFormatResp tResp;
            mzero(tResp);
            

            NVRSTATUS eRet = NvrPuiMappGetDevAudCap(nChn,&byAudEncNum,&byAudDecNum);
            MAPPERR("type[0x32A]SID:%d,avindex:%d,chn:%d-%d get aud format,enc:%u,dec:%u\n",SID,avIndex,p->channel,nChn,byAudEncNum,byAudDecNum);
            if(NVR_ERR__OK != eRet || 0 == byAudDecNum )
            {
                MAPPERR("type[0x32A]SID:%d,avindex:%d,chn:%d-%d get aud format,not sup\n",SID,avIndex,p->channel,nChn);
                tResp.channel = -1;   ///<与app特殊约定，-1表示改通道不支持呼叫，
            }
            else
            {
                tResp.channel = nChn;//p->channel;
            }
            
            tResp.codecId = MEDIA_CODEC_AUDIO_G711U;
            tResp.sample_rate = AUDIO_SAMPLE_8K;
            tResp.bitdata = AUDIO_DATABITS_16;
            tResp.channels = AUDIO_CHANNEL_MONO;
            //tResp.channels =  ; ///怎么赋值？
            tResp.avservchannel = 1;//怎么赋值？app根据该值要不要另启avstart通道
            nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_GETAUDIOOUTFORMAT_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlGetAudioOutFormatResp));
            if(AV_ER_NoERROR == nRet)
            {
                MAPPERR("type[0x32A]SID:%d,av:%d chn:%d get aud format avSendIOCtrl succ\n",SID,avIndex,tResp.channel);
            }
            else
            {
                char achStr[NVR_MAX_STR256_LEN];
                mzero(achStr);
                MappAvErrToStr(nRet,achStr);
                MAPPERR("type[0x32A]SID:%d,av:%d chn:%d get aud format avSendIOCtrl faild,%s\n",SID,avIndex,tResp.channel,achStr);
            }
		}
		break;
        
        ///<0x01FF  视频开始发送
	    case IOTYPE_USER_IPCAM_START:  
		{           
			MappStartVideoSnd(SID, avIndex, buf);
		}
		break;
        ///<0X02FF  视频停止
	    case IOTYPE_USER_IPCAM_STOP:
		{
            MappStopVideoSnd(SID, avIndex, buf);
		}
		break;
        ///<0x0300  音频发送
	    case IOTYPE_USER_IPCAM_AUDIOSTART:
		{
            MappStartAudioSnd(SID, avIndex, buf);			
		}
		break;
        ///<0x0301 音频停止发送
	    case IOTYPE_USER_IPCAM_AUDIOSTOP:
		{
            MappStopAudioSnd(SID, avIndex, buf);			
		}
		break;
        ///<ptz 0x1001
        case IOTYPE_USER_IPCAM_PTZ_COMMAND:
        {
            SMsgAVIoctrlPtzCmd *p = (SMsgAVIoctrlPtzCmd *)buf;
            MAPPIMP("PTZ_CMD ch:%d,control:%d,speed:%d,point:%d,limit:%d,aux:%d\n",p->channel,p->control,p->speed,p->point,p->limit,p->aux);
            MappPtzCtrl(p);
            
        }
        break;
		///<aud call 0x0350
		case IOTYPE_USER_IPCAM_SPEAKERSTART:
		{
			MappStartAudCall(SID, avIndex, buf);
		}
		break;
        ///<aud call stop 0x0351
	    case IOTYPE_USER_IPCAM_SPEAKERSTOP:
		{
			MappStopAudCall(SID, avIndex, buf);
		}
		break;
        ///<query rec  0x0318
	    case IOTYPE_USER_IPCAM_LISTEVENT_REQ:
		{
            MappRecQuery_t(SID, avIndex, buf);
		}
		break;
        ///<start rec play 031A
	    case IOTYPE_USER_IPCAM_RECORD_PLAYCONTROL:
		{
            MappRecPlayCmd_t(SID, avIndex, buf);
		}
		break;
        ///<get device info and TF card info 0x8015
        case IOTYPE_USER_IPCAM_DEVICE_INFO_REQ:
        {
            MappDeviceAndHDInfoCmd(SID, avIndex, buf);
        }
        break;
        ///<support OTA 0x800A
        case IOTYPE_USER_IPCAM_DEVICE_SUPPORT_OTA_REQ:
        {
            SMsgAVIoctrlDeviceSupportOTAResp tResp;

            mzero(tResp);
            tResp.isSupport = 1;
            nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_DEVICE_SUPPORT_OTA_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlDeviceSupportOTAResp));
            if(AV_ER_NoERROR != nRet)
            {
                MAPPERR("type:0x800A support ota resq failed:%d\n",nRet);
            }
            else
            {
               MAPPIMP("type:0x800A SID:%d,av:%d support:%d ota resq succ\n",SID,avIndex,tResp.isSupport);
            }
            
        }
        break;
        ///<ota升级 0x8001
        case IOTYPE_USER_IPCAM_OTA_REQ:
        {
            MappOtaCmd(SID, avIndex, buf);            
        }
        break;
        ///<以下自定义消息
        ///<获取通道名称，自定义，0x2001
        case IOTYPE_USER_EDGEOS_GET_CHANNEL_NAME_REQ:
        {
            MappGetChnName(SID, avIndex, buf);            
        }
        break;
        ///< query rec  0x2003
        case IOTYPE_USER_EDGEOS_QUERY_RECORD_REQ:
        {
            MappRecQuery(SID, avIndex, buf);           
        }
        break;
        ///<start rec play 0x2005
        case IOTYPE_USER_EDGEOS_RECORD_PLAYCONTROL_REQ:
        {
            MappRecPlayCmd(SID, avIndex, buf);            
        }
        break;
        ///<get created av channels 0x2007
        case IOTYPE_USER_EDGEOS_GET_CREATEDAVCHANNEL_REQ:
        {
            MappGetCreatedAvChanel(SID, avIndex, buf);
        }
        break;
        ///<When the AV channel fails, recreate it. Only for real-time stream 0x2009
        case IOTYPE_USER_EDGEOS_CREATE_AVCHANNEL_REQ:
        {
            MappCreateAvChannel(SID, avIndex, buf);            
        }
        break;
        ///<get support event list capability 0x2011
        //case IOTYPE_USER_EDGEOS_GET_SUPEVENTLISTCAP_REQ:
        {
            //MappGetSupEventListCap(SID, avIndex, buf);
        }
        //break;

        //get event list ENABLE config 0x2013        
        case IOTYPE_USER_EDGEOS_GET_EVENTLISTENABLE_REQ:
        {
            MappNtyGetEventListEnableCfg(SID, avIndex, buf);
        }
        break;

        //set event list ENABLE config 0x2015
        case IOTYPE_USER_EDGEOS_SET_EVENTLISTENABLE_REQ:
        {
            MappNtySetEventListEnableCfg(SID, avIndex, buf);
        }
        break;
        //query alarm event 0x2017
        case IOTYPE_USER_EDGEOS_QUERY_ALARM_EVENT_REQ:
        {
            MappNtyQueryAlarmEvent(SID, avIndex, buf);
        }
        break;
        //query channel record montyly view 0x2019
        case IOTYPE_USER_EDGEOS_QUERY_REC_MONTHVIEW_REQ:
        {
            MappRecQueryMonthlyView(SID, avIndex, buf);
        }
        break;
	    default:
		    MAPPIMP("avIndex %d: non-handle type[%X]\n", avIndex, type);
		break;
		
	}
}



/**登录服务处理线程*/
static void *MappDevLoginThread(void)
{
    int nRet = 0;
    int nTime = 0;
    char achStr[NVR_MAX_STR256_LEN];
    DeviceLoginInput auth_option = {};    

    prctl(PR_SET_NAME, "mapplogin", 0, 0, 0);

    auth_option.cb = sizeof(DeviceLoginInput);
    auth_option.authentication_type = AUTHENTICATE_BY_KEY;
    memcpy(auth_option.auth_key, g_achAuthkey, IOTC_AUTH_KEY_LENGTH);
  

    while(1)
    {
        OsApi_SemTakeByTime(g_tMappMgr.hLoginSem,nTime*1000);
        MAPPFRQ("============take end\n");
        if(0 == g_tMappMgr.byLoginListenThdStatus)
        {
            MAPPERR("login thread begin exit\n");
            g_tMappMgr.byModeInit = MAPP_MODE_STATUS_NO_INIT;
            break;
        }
        ///<IOTC初始化
        do
        {
            if(MAPP_MODE_STATUS_INITSUCC == g_tMappMgr.byModeInit)
            {
                break;
            }
            // use which Master base on location, port 0 means to get a random port
            MAPPERR("begin IOTC_Initialize2\n");
            nRet = IOTC_Initialize2(0);
            if(IOTC_ER_NoERROR != nRet)
            {
                mzero(achStr);
                MappIotcErrToStr (nRet,achStr);
                MAPPERR("IOTC_Initialize2 faild,achStr:%s\n",achStr);
                nRet = IOTC_DeInitialize();
                MAPPERR("IOTC_DeInitialize ret:%d\n",nRet);
                g_tMappMgr.nInitErr = nRet;
                break;
            }
            
            MAPPERR("IOTCAPI version[%s] AVAPI version[%s]\n", IOTC_Get_Version_String(), avGetAVApiVersionString());
            // alloc MAX_CLIENT_NUMBER*3 for every session av data/speaker/play back
            nRet = avInitialize(MAPP_MAX_AVINDEX_NUM);
            MAPPERR("avInitialize ret:%d\n",nRet);
            /*if(nRet != IOTC_ER_NoERROR) //不需判断，感觉返回值与MAPP_MAX_APP_NUM*3一致
            {
                mzero(achStr);
                MappIotcErrToStr (nRet,achStr);
                MAPPERR("avInitialize session num:%d faild:%s\n",MAPP_MAX_APP_NUM*3,achStr);
                nRet = avDeInitialize();
                MAPPERR("avDeInitialize ret:%d\n",nRet);
                break;
            }*/
            ///<该回调会不及时，建议通过状态获取
            //IOTC_Get_Login_Info_ByCallBackFn(LoginInfoCB);
            g_tMappMgr.byModeInit = MAPP_MODE_STATUS_INITSUCC;
            MAPPERR("modle init succ\n");
            
        }while(0);


        ///<开始login
        do 
        {
            if(MAPP_MODE_STATUS_INITSUCC != g_tMappMgr.byModeInit)
            {
                MAPPIMP("modle not init\n");
                break;
            }
            if(MAPP_LOGIN_SUCC == g_tMappMgr.byLogin)
            {
                MAPPFRQ("The device has been logined\n");
                break;
            }
            MAPPIMP("begin IOTC_Device_LoginEx:%d\n",g_tMappMgr.byLogin);
            nRet = IOTC_Device_LoginEx(g_tMappUidInfo.achUID, &auth_option);
            //nRet = IOTC_Device_LoginEx("F9KUA53MUF349G6GU1DJ", &auth_option);
            if(IOTC_ER_NoERROR != nRet) 
            {
                if(IOTC_ER_LOGIN_ALREADY_CALLED == nRet)
                {
                    g_tMappMgr.byLogin = MAPP_LOGIN_SUCC;
                    MAPPERR("dev under login\n");
                    break;
                }
                mzero(achStr);
                MappIotcErrToStr (nRet,achStr);
                MAPPDBG("IOTC_Device_LoginEx failed nRet:%d,%s\n",nRet,achStr);
                g_tMappMgr.nInitErr = nRet;
                ///<2s不断尝试
                nTime = 2;
                //sleep(2);
                break;
            }
            
            g_tMappMgr.nInitErr = 0;
            g_tMappMgr.byLogin = MAPP_LOGIN_SUCC;
            MAPPERR("login succ,%d\n",g_tMappMgr.byLogin);
        }while(0);
        

        ///<不断检测状态
        do 
        {
            if(MAPP_LOGIN_SUCC != g_tMappMgr.byLogin)
            {
                break;
            }
            
            nTime = 5;            
            //sleep(5);
            nRet = IOTC_Get_Login_Info(&g_tMappMgr.nLoginState);
            if(nRet < IOTC_ER_NoERROR)
            {
                MAPPERR("IOTC_Get_Login_Info not init :%d\n",nRet);
                g_tMappMgr.byModeInit = MAPP_MODE_STATUS_NO_INIT;
                g_tMappMgr.byLogin = MAPP_LOGIN_FAILED;
                break;
            }
            if(nRet>4)
            {
                ///<无用户在线，网络正常，退出监听,退出如何在登录？暂时不处理，只不断尝试连接
                MAPPERR("IOTC_Get_Login_Info fail nret :%d loginstate:%d loginfailcount:%d\n",nRet,g_tMappMgr.nLoginState,g_tMappMgr.byLoginFaildCount);
                g_tMappMgr.byLoginFaildCount++;
                if(g_tMappMgr.byLoginFaildCount>6)
                {
                    nRet = IOTC_ReInitSocket(0);
                    MAPPERR("=====IOTC_ReInitSocket ret:%d\n",nRet);
                    g_tMappMgr.byLoginFaildCount = 0;
                }
                
                //MappDisConnectCloudSrv();
                //break;
            }
            MAPPFRQ("IOTC_Get_Login_Info,ret:%d,loginstate:%d\n",nRet,g_tMappMgr.nLoginState);
            if (7 == g_tMappMgr.nLoginState)
            {                
                g_tMappMgr.byLoginFaildCount = 0;                 
                break;
            }
            
            
        }while(0);

                    
    }
   

    OsApi_TaskExit();
    MAPPERR("login thread exit\n");

    return;    
}
void *MappAVSrvStartRecvCmdThread(void *arg)
{
    char achStr[NVR_MAX_STR256_LEN];        
        
    TMappSidChIdInfo tSidChIdInfo = *(TMappSidChIdInfo *)arg;
	int SID = tSidChIdInfo.SID;
    int nChnId = tSidChIdInfo.nChId;
	
	free(arg);
	int ret;
	unsigned int ioType;
	char ioCtrlBuf[AV_MAX_IOCTRL_DATA_SIZE];
	struct st_SInfoEx Sinfo;

    prctl(PR_SET_NAME, "mappAVSrvCmd", 0, 0, 0);

	MAPPIMP("SID:%d chnId:%d Start AV server and recv IOCtrl cmd thread begin\n", SID,nChnId);

	AVServStartInConfig avStartInConfig;
    AVServStartOutConfig avStartOutConfig;

    memset(&avStartInConfig, 0, sizeof(AVServStartInConfig));
    avStartInConfig.cb               = sizeof(AVServStartInConfig);
    avStartInConfig.iotc_session_id  = SID;
    avStartInConfig.iotc_channel_id  = nChnId;
    avStartInConfig.timeout_sec      = 30;
    avStartInConfig.password_auth    = MappExPwdAuthCB;
    avStartInConfig.server_type      = SERVTYPE_STREAM_SERVER;
    avStartInConfig.resend           = ENABLE_RESEND;
    avStartInConfig.change_password_request = MappChangePwdCB;
    avStartInConfig.ability_request  = ExAbilityRequestFn;
#if ENABLE_TOKEN_AUTH
// Advance use of authentication.
// Users can enable or disable these function depends on the actual situation.
    avStartInConfig.token_auth       = ExTokenAuthCallBackFn;
    avStartInConfig.token_delete     = ExTokenDeleteCallBackFn;
    avStartInConfig.token_request    = ExTokenRequestCallBackFn;
    avStartInConfig.identity_array_request = ExGetIdentityArrayCallBackFn;
#endif
    
#if ENABLE_DTLS
// Enable DTLS encryption of AV data, otherwise use AV_SECURITY_SIMPLE
    avStartInConfig.security_mode = AV_SECURITY_DTLS; 
#else
    avStartInConfig.security_mode = AV_SECURITY_SIMPLE;
#endif

    avStartOutConfig.cb              = sizeof(AVServStartOutConfig);

    int avIndex = avServStartEx(&avStartInConfig, &avStartOutConfig);

    if(avIndex < 0)
	{
        mzero(achStr);
        MappAvErrToStr(avIndex,achStr);
        MAPPERR("avServStartEx failed,thread exit,SID:%d,chn:%d errno:%s\n",SID,nChnId, achStr);        
		pthread_exit(0);
	}
    Sinfo.size = sizeof(struct st_SInfoEx);
	if(IOTC_Session_Check_Ex(SID, &Sinfo) == IOTC_ER_NoERROR)
	{
		char *mode[3] = {"P2P", "RLY", "LAN"};
		// print session information(not a must)
		if( isdigit( Sinfo.RemoteIP[0] ))
		{
			MAPPERR("Client is from IP:%s, Port:%d Mode:%s VPG[%d:%d:%d] VER[%X] NAT[%d] AES[%d]\n", Sinfo.RemoteIP, Sinfo.RemotePort, mode[(int)Sinfo.Mode], Sinfo.VID, Sinfo.PID, Sinfo.GID, Sinfo.IOTCVersion, Sinfo.RemoteNatType, Sinfo.isSecure);
		}
	}

    MAPPERR("avServStartEx succ,sid:%d,chnid:%d avIndex:%d, resend:%d two_way_streaming:%d,auth_type:%d account_or_identity:%s\n",
    SID,nChnId,avIndex, avStartOutConfig.resend, avStartOutConfig.two_way_streaming,avStartOutConfig.auth_type, avStartOutConfig.account_or_identity);


    MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
    if(1==avStartOutConfig.two_way_streaming)
    {
        g_tMappMgr.atSidInfo[SID].byTwoWayStream = 1;
    }
    else
    {
        g_tMappMgr.atSidInfo[SID].byTwoWayStream = 0;
    }
    g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].byAvIsVaild = 1;
    g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].avIndex = avIndex;
    g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].SID = SID;
    g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].nChnId = nChnId; 
    g_tMappMgr.atSidInfo[SID].atChnToAvInfo[nChnId].avNo = avIndex+1;
     
    MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
    
	
#if ENABLE_DASA
    InitDasaSetting(avIndex);
#else
    avServSetResendSize(avIndex, 1024);
#endif

	while(1)
	{
		ret = avRecvIOCtrl(avIndex, &ioType, (char *)ioCtrlBuf, AV_MAX_IOCTRL_DATA_SIZE, 1000);
		if(ret >= 0)
		{
			Handle_IOCTRL_Cmd(SID, avIndex, ioCtrlBuf, ioType);
		}
		else if(ret != AV_ER_TIMEOUT)
		{
			MAPPERR("SID:%d,chnid:%d,avIndex:%d, avRecvIOCtrl failed %d exit.\n",SID,nChnId,avIndex,ret);
			break;
		}
	}
  	
	avServStop(avIndex);
	MAPPERR("SID:%d,avIndex:%d,chnid:%d AvRecvCmdThread exit!!\n", SID,nChnId,avIndex);

    MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
     
    g_tMappMgr.atSidInfo[SID].atChnToAvInfo[nChnId].avNo = 0;  ///<防止app单独停某一个路av
    mzero(g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex]);
    g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].byEncId = 1;
     
    MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);

	

	pthread_exit(0);
    return;
}


void MappCloseSession(int nSId)
{
    int i = 0;
    
    MAPPERR("SID:%d,vaild:%d\n",nSId,g_tMappMgr.atSidInfo[nSId].nSidVaild);
    if(1 != g_tMappMgr.atSidInfo[nSId].nSidVaild)
    {

        MAPPDBG("SID:%d invaild:%d\n",nSId,g_tMappMgr.atSidInfo[nSId].nSidVaild);
        return;
    }
    
    ///<防止资源泄漏
    MappStreamReleaseSource(nSId);
    MappRecRelease(nSId);
    
    MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
    mzero(g_tMappMgr.atSidInfo[nSId]); 
    for(i = 0;i<MAPP_MAX_AVINDEX_NUM;i++)
    {
        g_tMappMgr.atSidInfo[nSId].atAvIndexinfo[i].byEncId = 1;
    }
    MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
    

    
    MAPP_SEMTAKE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);      
    if(g_tMappMgr.nOnlineNum>0)
    {
        g_tMappMgr.nOnlineNum--;
        MAPPERR("online:%d\n",g_tMappMgr.nOnlineNum);
    }
    else
    {
        MAPPERR("==online:%d\n",g_tMappMgr.nOnlineNum);
    }
    MAPP_SEMGIVE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);

    IOTC_Session_Close(nSId);
    
}



/****
Thread - Start AV server and recv IOCtrl cmd for every new av idx
*/
static void *MappMainAVSrvStartThread(void *arg)
{   
	int SID = *(int *)arg;	
	free(arg);
    
    char achStr[NVR_MAX_STR256_LEN];
	int ret;
	unsigned int ioType;
	char ioCtrlBuf[AV_MAX_IOCTRL_DATA_SIZE];
	struct st_SInfoEx Sinfo;

    prctl(PR_SET_NAME, "mainAvCmdTread", 0, 0, 0);

	MAPPIMP("SID:%d Start AV server and recv IOCtrl cmd thread begin\n", SID);

	AVServStartInConfig avStartInConfig;
    AVServStartOutConfig avStartOutConfig;

    memset(&avStartInConfig, 0, sizeof(AVServStartInConfig));
    avStartInConfig.cb               = sizeof(AVServStartInConfig);
    avStartInConfig.iotc_session_id  = SID;
    avStartInConfig.iotc_channel_id  = 0;
    avStartInConfig.timeout_sec      = 30;
    avStartInConfig.password_auth    = MappExPwdAuthCB;
    avStartInConfig.server_type      = SERVTYPE_STREAM_SERVER;
    avStartInConfig.resend           = ENABLE_RESEND;
    avStartInConfig.change_password_request = MappChangePwdCB;
    avStartInConfig.ability_request  = ExAbilityRequestFn;
#if ENABLE_TOKEN_AUTH
// Advance use of authentication.
// Users can enable or disable these function depends on the actual situation.
    avStartInConfig.token_auth       = ExTokenAuthCallBackFn;
    avStartInConfig.token_delete     = ExTokenDeleteCallBackFn;
    avStartInConfig.token_request    = ExTokenRequestCallBackFn;
    avStartInConfig.identity_array_request = ExGetIdentityArrayCallBackFn;
#endif
    
#if ENABLE_DTLS
// Enable DTLS encryption of AV data, otherwise use AV_SECURITY_SIMPLE
    avStartInConfig.security_mode = AV_SECURITY_DTLS; 
#else
    avStartInConfig.security_mode = AV_SECURITY_SIMPLE;
#endif

    avStartOutConfig.cb              = sizeof(AVServStartOutConfig);

    int avIndex = avServStartEx(&avStartInConfig, &avStartOutConfig);

    if(avIndex < 0)
	{
        mzero(achStr);
        MappAvErrToStr(avIndex,achStr);
        MAPPERR("avServStartEx failed,thread exit,SID:%d,errno:%s\n",SID, achStr);		
		MappCloseSession(SID);
		pthread_exit(0);
	}
    Sinfo.size = sizeof(struct st_SInfoEx);
	if(IOTC_Session_Check_Ex(SID, &Sinfo) == IOTC_ER_NoERROR)
	{
		char *mode[3] = {"P2P", "RLY", "LAN"};
		// print session information(not a must)
		if( isdigit( Sinfo.RemoteIP[0] ))
		{
			MAPPERR("Client is from IP:%s, Port:%d Mode:%s VPG[%d:%d:%d] VER[%X] NAT[%d] AES[%d]\n", Sinfo.RemoteIP, Sinfo.RemotePort, mode[(int)Sinfo.Mode], Sinfo.VID, Sinfo.PID, Sinfo.GID, Sinfo.IOTCVersion, Sinfo.RemoteNatType, Sinfo.isSecure);
		}
	}

    MAPPERR("avServStartEx succ,SID:%d,avIndex:%d, resend:%d two_way_streaming:%d,auth_type:%d account_or_identity:%s\n",
    SID,avIndex, avStartOutConfig.resend, avStartOutConfig.two_way_streaming,avStartOutConfig.auth_type, avStartOutConfig.account_or_identity);
    MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
    //g_tMappMgr.atSidInfo[nSessionID].nAvIndexNum++; 
    if(1==avStartOutConfig.two_way_streaming)
    {
        g_tMappMgr.atSidInfo[SID].byTwoWayStream = 1;
    }
    else
    {
        g_tMappMgr.atSidInfo[SID].byTwoWayStream = 0;
    }
    g_tMappMgr.atSidInfo[SID].wRemotePort = Sinfo.RemotePort;
    mcopy(g_tMappMgr.atSidInfo[SID].achRemoteIP, Sinfo.RemoteIP);
    g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].byAvIsVaild = 1;
    g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].avIndex = avIndex;
    g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].SID = SID;
    g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].nChnId = 0; 
    g_tMappMgr.atSidInfo[SID].atChnToAvInfo[0].avNo = avIndex+1;
     
    MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
    
    MappOptLog(SID,NVR_LOG_LOGIN_IN,0,0);
	
#if ENABLE_DASA
    InitDasaSetting(avIndex);
#else
    avServSetResendSize(avIndex, 1024);
#endif

	while(1)
	{
		ret = avRecvIOCtrl(avIndex, &ioType, (char *)ioCtrlBuf, AV_MAX_IOCTRL_DATA_SIZE, 1000);
		if(ret >= 0)
		{
			Handle_IOCTRL_Cmd(SID, avIndex, ioCtrlBuf, ioType);
		}
		else if(ret != AV_ER_TIMEOUT)
		{
			MAPPERR("SID:%d,avIndex:%d, avRecvIOCtrl failed %d exit.\n",SID,avIndex,ret);
            MappOptLog(SID,NVR_LOG_LOGIN_WEB_OUT,0,0);
			break;
		}
	}   	
	avServStop(avIndex);    
    
	MAPPERR("SID:%d, avIndex:%d, Main AvRecvCmdThread exit!!\n", SID, avIndex);


	if(IOTC_Session_Check_Ex(SID, &Sinfo) != IOTC_ER_NoERROR)
	{
        MappCloseSession(SID);
        MAPPERR("sid:%d close\n",SID);
	}	

	pthread_exit(0);
}
/**多源设备通道av服务启动处理线程*/
static void *MappChnAvThread(void)
{
    int nRet = 0;
    int j = 0;

    prctl(PR_SET_NAME, "mappchnavdeal", 0, 0, 0);
    
    while(1)
    {
        MAPP_SEMTAKE(g_tMappMgr.hChnAvSem, MAPP_SEM_CHNAV);
        MAPPERR("type[0x328] get chn num:%d begin create avsrv and cmd thread\n",g_tMappMgr.nChnNum);
        
        for (j=1;j<g_tMappMgr.nChnNum;j++)
        {
            TMappSidChIdInfo *ptSidChIdInfo = (TMappSidChIdInfo *)malloc(sizeof(TMappSidChIdInfo));
            ptSidChIdInfo->nChId = j;
            ptSidChIdInfo->SID = g_SID;

            pthread_t Thread_ID;
            nRet = pthread_create(&Thread_ID, NULL, &MappAVSrvStartRecvCmdThread, (void *)ptSidChIdInfo);
            if(nRet < 0) 
            {
                MAPPERR("type[0x328] get chn num SID:%d,chid:%d pthread_create failed ret:%d\n", g_SID,j, nRet);
            }
            else 
            {
                pthread_detach(Thread_ID);
                MAPPERR("type[0x328] get chn num SID:%d,chid:%d thread create succ\n",g_SID,j);
            } 
        }
    }

    return;    
}

    
/**监听服务处理线程*/
void *MappListenThread(void)
{
    int SID = 0;
    int nRet;

    prctl(PR_SET_NAME, "mapplisten", 0, 0, 0);

    
    while(1)
    {
        if(0 == g_tMappMgr.byLoginListenThdStatus)
        {
            MAPPERR("listen thread begin exit\n");
            break;
        }
        // Accept connection only when IOTC_Listen() calling
        SID = IOTC_Listen(1000);        
        if (SID < 0) 
        {            
            if (SID == IOTC_ER_EXCEED_MAX_SESSION) 
            {
                sleep(5);
            }
            continue;
        }
        MAPPERR("listen:SID:%d\n",SID);
       
    
        int *sid = (int *)malloc(sizeof(int));
        if(sid == NULL)
        {
            MAPPERR("malloc failed\n");
            continue;
        }
        *sid = SID;
        pthread_t Thread_ID;
        nRet = pthread_create(&Thread_ID, NULL, &MappMainAVSrvStartThread, (void *)sid);
        if(nRet < 0) 
        {
            MAPPERR("pthread_create failed ret:%d\n", nRet);
        } 
        else 
        {
            pthread_detach(Thread_ID);

            
            MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
            g_tMappMgr.atSidInfo[SID].nSidVaild = 1;
            MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);

            MAPP_SEMTAKE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);
            g_tMappMgr.nOnlineNum++;
            MAPPERR("online:%d\n",g_tMappMgr.nOnlineNum);
            MAPP_SEMGIVE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);
        }            
    
    }

    OsApi_TaskExit();
    MAPPERR("listen thread exit\n");

    return;    
}


void StartApp()
{
    int nRet = 0;
    const char *license_key ="AQAAAD5/eLyR2TCRrgOj7hw9Uic13UR+c7qyXK3U+b4nP2c3ATZKsT7lUNYO5kWtluqdrYd6yqXrRW0I5qDYkJSUTQPGRSmonQ3ryEXk3r9AJi8Dg5bRF+o8p3cU5Evr0OhBJS4AmBMiKZP4pe7ei8jSN94gaQhyz+wckn3Ok/VfkINbauuDib7ATH6ki7ft5w2PfRlWlygHCS2Jplvnlteowawb";

   
    do
    { 
        MappInitLog();

        MappInit();
        MappStreamInit();
        MappRecInit();
        MappNoticeInit();
        
        ///<模块初始化
        nRet = TUTK_SDK_Set_License_Key(license_key);
        
        if (0 != nRet)
        {
            MAPPERR("TUTK_SDK_Set_License_Key failed :%d\n",nRet);
            break;
        }
        g_tMappMgr.byModeInit = MAPP_MODE_STATUS_KEYSUCC;
        ///<设置最大连接数
        nRet = IOTC_Set_Max_Session_Number(g_tMappMgr.bySupAppNum);
        if (0 != nRet)
        {
            MAPPERR("IOTC_Set_Max_Session_Number failed :%d\n",nRet);
            break;
        }
        BOOL32 bEnable = FALSE;
        nRet = MappCfgRegistFunToAppbase(&bEnable);
        if(0 != nRet)
        {
            MAPPERR("MappCfgRegistFunToAppbase failed\n");
            break;
        }
        if(bEnable && g_tMappMgr.byLoginListenThdStatus==1)
        {
            MAPPERR("begin create connect thread\n");
            MappConnectCloudSrv();
        }
        

        if(g_tMappMgr.nChnNum>1)
        {
           
        	if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)MappChnAvThread, "mappchnavdeal", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
        	{
        		MAPPERR("create MappListenThread failed\n");            
                break;
        	}
        }

        OsApi_RegCommand("mapp", (void *)mapptest, "mapptest"); 
        OsApi_RegCommand("mappspecial", (void *)mappspecialtest, "mappstest"); 
        
    }while(0);
    
    return ;   
}
int  MappDisConnectCloudSrv()
{
    int nRet1 = 0;
    int nRet2 = 0;
    int i = 0;
    
    g_tMappMgr.byLoginListenThdStatus = FALSE;  
    OsApi_SemGive(g_tMappMgr.hLoginSem);
    
    
    
    for(i = 0;i<g_tMappMgr.bySupAppNum;i++)
    {
        MappCloseSession(i);
    }
    
    IOTC_Listen_Exit();
    nRet1 = avDeInitialize();
    nRet2 = IOTC_DeInitialize();
    g_tMappMgr.byModeInit = MAPP_MODE_STATUS_NO_INIT;
    g_tMappMgr.byLogin = MAPP_LOGIN_FAILED;
    g_tMappMgr.nLoginState = 0;

    MAPPIMP("avDeInitialize:%d,IOTC_DeInitialize:%d\n",nRet1,nRet2);
    
    return nRet2;
}

int MappConnectCloudSrv()
{
    int nRet = 0;
    g_tMappMgr.byLoginListenThdStatus = TRUE;
    
    
   if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)MappDevLoginThread, "mapplogin", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
   {
   	    MAPPERR("create MappDevLogin failed\n");
        nRet = -1;
   }
   
   if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)MappListenThread, "mapplisten", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
   {
   	    MAPPERR("create MappListenThread failed\n");  
        nRet = -1;
   }
   MAPPIMP("create login/listen thread succ\n");
   return nRet;
}
unsigned short MappConverPlayload(u8 byPayload)
{
     switch(byPayload)
     {
         case 106:
         case 96:
             return MEDIA_CODEC_VIDEO_H264;
             break;
         case 26:
             return MEDIA_CODEC_VIDEO_MJPEG;
             break;
         case 111:
             return MEDIA_CODEC_VIDEO_HEVC;
             break;            
         case 97:
             return MEDIA_CODEC_VIDEO_MPEG4;
             break;
         case 107:
             return MEDIA_CODEC_UNKNOWN;
             break;
         default:
             return MEDIA_CODEC_VIDEO_H264;
     }
}
unsigned short MappAudConverPlayload(u8 byPayload)
{
    
    switch (byPayload)
    {
        case MEDIA_TYPE_AACLC:
            return MEDIA_CODEC_AUDIO_AAC_RAW; //0x86/87/88都正常
            break;
        case MEDIA_TYPE_PCMU:
            return MEDIA_CODEC_AUDIO_G711U;
            break;
        case MEDIA_TYPE_PCMA:
            return MEDIA_CODEC_AUDIO_G711A;
            break;
        case MEDIA_TYPE_ADPCM:
            return MEDIA_CODEC_AUDIO_ADPCM;
            break;            
        case MEDIA_TYPE_MP3:
            return MEDIA_CODEC_AUDIO_MP3;
            break;
        case MEDIA_TYPE_G726_16:
        case MEDIA_TYPE_G726_24:
        case MEDIA_TYPE_G726_32:
        case MEDIA_TYPE_G726_40:
            return MEDIA_CODEC_AUDIO_G726;
            break;
        default:
            return MEDIA_CODEC_UNKNOWN;
    }
}



int MappGetChnName(int SID, int avIndex, char *buf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet = 0;
    u16 wNum = 0;  
    MappMsgAVIoctrlGetChannelNameReq *p = (MappMsgAVIoctrlGetChannelNameReq *)buf; 
    MappMsgAVIoctrlGetChannelNameResp tResp;

    mzero(tResp);

    
    wNum = MIN(p->count,(g_tMappMgr.nChnNum-p->channel));
    MAPPERR("type[0x2001] get chn name,SID:%d avindex:%d,chn:%d,count:%d,num:%u\n",SID,avIndex,p->channel,p->count,wNum);
    
    do 
    {
        if(p->channel>=g_tMappMgr.nChnNum)
        {
            MAPPERR("type[0x2001] get chn name,channel is error\n");
            eRet = NVR_ERR__PARAM_INVALID;
            break;
        }
        
        TNvrPuiDevParam atDevList[MAPP_MAX_CHN_NUM];
        mzero(atDevList);

        eRet = NvrPuiGetDevList(p->channel,atDevList,&wNum);
        if(eRet != NVR_ERR__OK)
        {
            MAPPERR("NvrPuiGetDevList failed:%d\n",eRet)
            break;
        }
        
        int i = 0;        
        
        for(i = 0;i<wNum;i++)
        {
            tResp.atChannelInfo[i].channel = p->channel+i;
            if(atDevList[i].tChnInfo.wDevId != 0)
            {
                CharConvConvertUnicodetoUtf8(atDevList[i].tChnInfo.abyNvrChnName,atDevList[i].tChnInfo.wNvrChnNameLen, tResp.atChannelInfo[i].name, 128);
                
                
                if(atDevList[i].tDevStatus.eConnectStatus == NVR_PUI_ONLINE)
                {
                    tResp.atChannelInfo[i].isonline = 1;
                }
                else
                {
                    tResp.atChannelInfo[i].isonline = 0;
                }
            }
            else
            {
                if(0 != strlen(atDevList[i].tChnInfo.abyNvrChnName))
                {
                    CharConvConvertUnicodetoUtf8(atDevList[i].tChnInfo.abyNvrChnName,atDevList[i].tChnInfo.wNvrChnNameLen, tResp.atChannelInfo[i].name, 128);
                }
                tResp.atChannelInfo[i].isonline = 2;
            }
            MAPPDBG("NO.%d,chn:%d,online:%d %d,%s\n",i,tResp.atChannelInfo[i].channel,tResp.atChannelInfo[i].isonline,strlen(tResp.atChannelInfo[i].name),tResp.atChannelInfo[i].name);
            tResp.atChannelInfo[i].name[strlen(tResp.atChannelInfo[i].name)+1] = '\0';                       
        }
        
        tResp.count = (u8)wNum;
        
    }while(0);

    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_GET_CHANNEL_NAME_RESP, (char *)&tResp, sizeof(MappMsgAVIoctrlGetChannelNameResp));
    MAPPERR("type[0x2001]get chn name,SID:%d avindex:%d,chn:%d,count:%d,ret:%d\n",SID,avIndex,p->channel,tResp.count,nRet);

    return 0;    
}



int MappGetChnNum(int SID, int avIndex, char *buf)
{
    int nRet = 0; 
    SMsgAVIoctrlGetSupportStreamResp tResp;
    mzero(tResp);

    MAPPERR("type[0x328] get chn num,SID:%d avindex:%d,num:%d\n",SID,avIndex,g_tMappMgr.nChnNum);

    tResp.number = g_tMappMgr.nChnNum;

    if(g_abTkApp[SID])
    {
        //兼容tutk app
        MAPPERR("type[0x328]tk app sid:%d\n",SID);
        g_abTkApp[SID] = 0;
        SStreamDef aStreams[32];
        int i = 0;
        for(i = 0;i<g_tMappMgr.nChnNum;i++)
        {
            aStreams[i].index = i;
            aStreams[i].channel = i;  
        }
        memcpy(&tResp.streams[0], &aStreams[0], sizeof(SStreamDef)*g_tMappMgr.nChnNum);
        nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_GETSUPPORTSTREAM_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlGetSupportStreamResp)+sizeof(SStreamDef)*(g_tMappMgr.nChnNum-1));
    }
    else
    {
        // aipv app
        tResp.streams[0].index = 0;
        tResp.streams[0].channel = 0;
        nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_GETSUPPORTSTREAM_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlGetSupportStreamResp));
    }
    
    
    if(AV_ER_NoERROR == nRet)
    {        
        if(g_tMappMgr.nChnNum>=2 && 0 == g_tMappMgr.atSidInfo[SID].byAvCreate) ///<防止重启创建av
        {
            MAPPERR("type[0x328] SID:%d get chn num:resp:%d bcreate:%u avSendIOCtrl succ\n",SID,tResp.number,g_tMappMgr.atSidInfo[SID].byAvCreate);

            MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
            g_tMappMgr.atSidInfo[SID].byAvCreate = 1;
            MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
            g_SID = SID;
            
            MAPP_SEMGIVE(g_tMappMgr.hChnAvSem,MAPP_SEM_CHNAV); 
            
        }
        else
        {
            MAPPERR("type[0x328] get chn SID:%d,avindex:%d,chunum:%d,av1_valid:%d,av2_vaild:%d,repreat create:%u av return\n",
                SID,avIndex,g_tMappMgr.nChnNum,g_tMappMgr.atSidInfo[SID].atAvIndexinfo[1].byAvIsVaild,g_tMappMgr.atSidInfo[SID].atAvIndexinfo[2].byAvIsVaild,g_tMappMgr.atSidInfo[SID].byAvCreate);
            return nRet;
        }
    }
    else
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);
        MAPPERR("type[0x328] get chn num avSendIOCtrl failed:%s\n",achStr);
    }
    

    return nRet;
}
///<get created av channel
int MappGetCreatedAvChanel(int SID, int avIndex, char *buf)
{
    int nRet = 0; 
    int i = 0;
 
    MappMsgAVIoctrlGetCreatedAvChannelResp tResp;
    mzero(tResp);
    MAPPERR("type[0x2007]get created avchn SID:%d avindex:%d\n",SID,avIndex);
    
    tResp.totalChannel = g_tMappMgr.nChnNum;
    for(i = 0;i<g_tMappMgr.nChnNum;i++)
    {
        if(g_tMappMgr.atSidInfo[SID].atChnToAvInfo[i].avNo != 0)
        {            
            tResp.streams[tResp.createdAvNumber].avIndex = g_tMappMgr.atSidInfo[SID].atChnToAvInfo[i].avNo - 1;
            tResp.streams[tResp.createdAvNumber].channel = i;
            tResp.createdAvNumber++;
            MAPPDBG("No.%d,chn:%d,avindex:%d\n",tResp.createdAvNumber,i,tResp.streams[tResp.createdAvNumber].avIndex);
        }
    }

    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_GET_CREATEDAVCHANNEL_RESP, (char *)&tResp, sizeof(MappMsgAVIoctrlGetCreatedAvChannelResp));
    MAPPERR("type[0x2007]get created avchn,SID:%d avindex:%d,tatalchn:%d,avcount:%d,ret:%d\n",SID,avIndex,tResp.totalChannel,tResp.createdAvNumber,nRet);

    return nRet;
    
}
///<create av channel
int MappCreateAvChannel(int SID, int avIndex, char *buf)
{
    int nRet = 0;
    MappMsgAVIoctrlCreatedAvChannelReq *p = (MappMsgAVIoctrlCreatedAvChannelReq *)buf; 
    MappMsgAVIoctrlCreatedAvChannelResp tResp;

    MAPPERR("type[0x2009]create av SID:%d avindex:%d channel:%d\n",SID,avIndex,p->channel);
    
    if(g_tMappMgr.atSidInfo[SID].atChnToAvInfo[p->channel].avNo != 0)
    {
        tResp.result = 1;
    }
    else
    {
        TMappSidChIdInfo *ptSidChIdInfo = (TMappSidChIdInfo *)malloc(sizeof(TMappSidChIdInfo));
        ptSidChIdInfo->nChId = p->channel;
        ptSidChIdInfo->SID = SID;

        pthread_t Thread_ID;
        nRet = pthread_create(&Thread_ID, NULL, &MappAVSrvStartRecvCmdThread, (void *)ptSidChIdInfo);
        if(nRet < 0) 
        {
            MAPPERR("type[0x2009]SID:%d,chid:%d pthread_create failed ret:%d\n", SID,p->channel, nRet);
            tResp.result = -1;
        }
        else 
        {
            pthread_detach(Thread_ID);
            MAPPERR("type[0x2009]SID:%d,chid:%d thread create succ\n",SID,p->channel);
            tResp.result = 0;
        } 
    }
    
    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_CREATE_AVCHANNEL_RESP, (char *)&tResp, sizeof(MappMsgAVIoctrlCreatedAvChannelResp));
    MAPPERR("type[0x2009]create av channel,SID:%d avindex:%d chn:%d,result:%d,ret:%d\n",SID,avIndex,p->channel,tResp.result,nRet);

    return nRet;
}

#if 0
int MappGetSupEventListCap(int SID, int avIndex, char *buf)
{
    int nRet = 0;
    BOOL32 bEnable = FALSE;
    MappMsgAVIoctrlGetSupEventListCapReq *p = (MappMsgAVIoctrlGetSupEventListCapReq *)buf; 
    MappMsgAVIoctrlGetSupEventListCapResp tResp;

    MAPPERR("type[0x2011]get eventlist cap SID:%d avindex:%d chn:%d,event:%d\n",SID,avIndex,p->channel,p->event);

    mzero(tResp);

    nRet = MappEventListOpt(p->channel,p->event, 1, &bEnable);
    tResp.result = nRet;
    tResp.event = p->event;
    tResp.support = (unsigned char)bEnable;

    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_GET_SUPEVENTLISTCAP_RESP, (char *)&tResp, sizeof(MappMsgAVIoctrlGetSupEventListCapResp));
    MAPPERR("type[0x2011]get eventlist cap SID,SID:%d avindex:%d chn:%d,event:%d cap:%d result:%d,ret:%d\n",SID,avIndex,p->channel,p->event,tResp.support,tResp.result,nRet);
    return nRet;
}

int MappGetEventListEnableCfg(int SID, int avIndex, char *buf)
{
    int nRet = 0;
    BOOL32 bEnable = FALSE;
    MappMsgAVIoctrlGetEventLisEnableReq *p = (MappMsgAVIoctrlGetEventLisEnableReq *)buf; 
    MappMsgAVIoctrlGetEventListEnableResp tResp;

    MAPPERR("type[0x2013]get event enable cfg SID:%d avindex:%d chn:%d,event:%d\n",SID,avIndex,p->channel,p->event);
    mzero(tResp);

    nRet = MappEventListOpt(p->channel,p->event, 2, &bEnable);
    tResp.result = nRet;    
    tResp.event = p->event;
    tResp.isEnable = (unsigned char)bEnable;

    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_GET_EVENTLISTENABLE_RESP, (char *)&tResp, sizeof(MappMsgAVIoctrlGetEventListEnableResp));
    MAPPERR("type[0x2013]get event enable cfg SID:%d avindex:%d chn:%d,event:%d,enable:%d,result:%d,ret:%d\n",SID,avIndex,p->channel,p->event,tResp.isEnable,tResp.result,nRet);
    return nRet;
}
int MappSetEventListEnableCfg(int SID, int avIndex, char *buf)
{
    int nRet = 0;
    BOOL32 bEnable = 0;
    MappMsgAVIoctrlSetEventListEnableReq *p = (MappMsgAVIoctrlSetEventListEnableReq *)buf; 
    MappMsgAVIoctrlSetEventListEnableResp tResp;

    MAPPERR("type[0x2015]set event enable cfg SID:%d avindex:%d chn:%d,event:%d,isenable:%d\n",SID,avIndex,p->channel,p->event,p->isEnable);
    mzero(tResp);

    bEnable = p->isEnable;
    nRet = MappEventListOpt(p->channel,p->event, 3, &bEnable);
    tResp.result = nRet; 

    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_SET_EVENTLISTENABLE_RESP, (char *)&tResp, sizeof(MappMsgAVIoctrlGetEventListEnableResp));
    MAPPERR("type[0x2015]set event enable cfg SID:%d avindex:%d chn:%d,event:%d,isenable:%d,result:%d,nret:%d\n",SID,avIndex,p->channel,p->event,p->isEnable,tResp.result,nRet);

    return nRet;
}

#endif


///<ptz控制
void MappPtzCtrl(SMsgAVIoctrlPtzCmd *p)
{
    NVRSTATUS eRet = NVR_ERR__OK;

	TNvrPuiPtzTaskParam tPtzTaskParam;
    TNvrPtzCtrlInfo tPtzInfo;
	mzero(tPtzTaskParam);
    mzero(tPtzInfo);

	tPtzTaskParam.eSrcType = ENVR_PUI_PTZ_SRC_PLATFORM;
	eRet = NvrPuiCreatePtzTask(&tPtzTaskParam);
    if(NVR_ERR__OK != eRet)
    {
		MAPPERR("NvrPuiCreatePtzTask failed:%d\n",eRet);
		return ;
    }
    MAPPDBG("chn:%d ptz cmd:%d,speed:%d,point:%d\n",p->channel,p->control,p->speed,p->point);
    tPtzInfo.wPanSpeed = p->speed;
    tPtzInfo.wTilSpeed = p->speed;  
    tPtzInfo.wIspSpeed = p->speed;
    tPtzInfo.wNumber = p->point;
    tPtzInfo.eCtrlType = p->control;    
    
	eRet =  NvrPuiPtzCtrl(&tPtzTaskParam, p->channel, &tPtzInfo);
	if(NVR_ERR__OK != eRet)
	{
    	MAPPERR("NvrPuiPtzCtrl failed:%d\n",eRet);
	}
    //task创建和释放要匹配
	eRet = NvrPuiClearPtzTask(&tPtzTaskParam);
    if(NVR_ERR__OK != eRet)
    {
		MAPPERR("NvrPuiClearPtzTask failed:%d\n",eRet);
    }
	return ;
}

void MappDeviceAndHDInfoCmd(int SID, int avIndex, char *buf)
{ 
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet = 0;
    TNvrSysDevInfo tDevInfo;
    TNvrSysParam tSysParam;
    SMsgAVIoctrlDeviceInfoRespEx tResp;

    MAPPERR("type[0x8015]get device and HD info CMD SID:%d,avindex:%d\n",SID,avIndex);

    
    mzero(tResp);
    mzero(tDevInfo);
    eRet =  NvrSysGetDevInfo(&tDevInfo);
    if(NVR_ERR__OK != eRet)
    {
        MAPPERR("get dev info err:%d\n",eRet);
    }
    else
    {
        mcopy(tResp.model, tDevInfo.achDevType);
        mcopy(tResp.product, "IPC");
        mcopy(tResp.vender, "seavan");
        char *p = NVR_SYS_SOFT_VER;
        char *q = strrchr(p,'.');
        if(q != NULL)
        {
            q++;
            ///<815=>8.1.5
            tResp.version = 815*10000+atoi(q);
        }        
    }
   

     nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_DEVICE_INFO_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlDeviceInfoRespEx));
     if(AV_ER_NoERROR != nRet)
     {
         MAPPERR("type:0x8015 get devinfo HD info resq failed:%d\n",nRet);
     }
     else
     {
        MAPPIMP("type:0x8015 SID:%d,av:%d get devinfo model:%s product:%s\n",SID,avIndex,tResp.model,tResp.product);
     }
    
}
int MappOtaWriteCB(void *contents, int size, int nmemb, FILE *fd) 
{  
    static unsigned int m_nTotal = 0;
    static unsigned int m_ntnpTotal = 0;
    static unsigned int m_nCount = 1;
    SMsgAVIoctrlOTAResp tResp;
    
    
    
    int nLen = fwrite(contents, size, nmemb, fd); 
    m_nTotal += nLen;
    m_ntnpTotal += nLen;
    if(m_nTotal == g_nOtaFileSize)
    {
        m_nTotal = 0;
        tResp.progress = 99;
        tResp.endflag = 0;
        m_nCount  = 1;
        m_ntnpTotal = 0;
        avSendIOCtrl(g_nOtaAvIndex, IOTYPE_USER_IPCAM_OTA_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlOTAResp));
        MAPPDBG("=====finish %d,%d-%d-%d,%d\n",nLen,m_nTotal,m_ntnpTotal,g_nOtaFileSize,tResp.progress)
               
    }
    else if(m_ntnpTotal >= g_nOneSize)
    { 
        m_nCount++;
        tResp.progress = m_nCount;
        tResp.endflag = 0;
        m_ntnpTotal = 0;
        avSendIOCtrl(g_nOtaAvIndex, IOTYPE_USER_IPCAM_OTA_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlOTAResp));
        MAPPFRQ("=====%d,%d-%d,%d\n",nLen,m_nTotal,m_ntnpTotal,tResp.progress)
    }
    
    return nLen;  
}  

 

void MappOtaCmd(int SID, int avIndex, char *buf)
{
    int nRet = -1;
    CURL *curl = NULL;
    CURLcode res;
    FILE *fd = NULL;
    SMsgAVIoctrlOTAResp tResp;
    static BOOL32 bOta = FALSE;
    u32 dwSize = 0;

    mzero(tResp);
    #if 1
    do 
    {
        if(bOta)
        {
            MAPPERR("otaing,return\n");
            tResp.progress = 100;
            tResp.endflag = 1;
            avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_OTA_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlOTAResp));            
            return;
        }
        
        if(NULL == buf)
        {
            MAPPERR("SID:%d,avindex:%d,buf is null\n",SID,avIndex);
            break;
        }
        
        SMsgAVIoctrlOTAReq *p = (SMsgAVIoctrlOTAReq *)buf;
        MAPPERR("SID:%d,avindex:%d,checksum:%s,size:%d,url:%s\n",SID,avIndex,p->file_checksum,p->file_size,p->url);
        dwSize = p->file_size;

        bOta = TRUE;
        g_nOtaAvIndex = avIndex;
        g_nOtaFileSize = p->file_size;
        g_nOneSize = g_nOtaFileSize/98;
        
        
        curl = curl_easy_init();
        if(NULL == curl) 
        {
            MAPPERR("init curl failed\n");
            break;
        }
        
        fd = fopen(NVR_MAPP_OTA_PKG_PATH,"wb");
        if(!fd)
        {
            MAPPERR("fopen failed\n");
            break;
        }        
        
        curl_easy_setopt(curl, CURLOPT_URL, p->url);  
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_DNS_USE_GLOBAL_CACHE, 1L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
        
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, MappOtaWriteCB); 
        
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, fd); 
        tResp.progress = 0;
        tResp.endflag = 0;
        nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_OTA_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlOTAResp));
        MAPPERR("progress 0 avSendIOCtrl:%d\n",nRet);
  
         
        res = curl_easy_perform(curl);  
        if(res != CURLE_OK)
        {
            MAPPERR("curl_easy_perform faild:%s\n", curl_easy_strerror(res));
            break;
        }          
         
        curl_easy_cleanup(curl);         
        nRet = 0; 
        tResp.progress = 100;
       
    }while(0);

    if(fd != NULL)
    {
        fclose(fd); 
        fd = NULL;        
    }
    bOta = FALSE;
    g_nOtaAvIndex = -1;
    g_nOtaFileSize = 0;
    g_nOneSize = 0;

    tResp.endflag = 1;

    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_OTA_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlOTAResp));
    if(AV_ER_NoERROR == nRet)
    {
        MAPPERR("type[0x8001] SID:%d,avindex:%d ota avSendIOCtrl succ\n",SID,avIndex);
    }
    else
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);
        MAPPERR("type[0x8001] SID:%d,avindex:%d ota avSendIOCtrl faild,%s\n",SID,avIndex);
    }

    if(nRet == 0)
    {
        TNvrCapHwCapInfo tHwCap;
        TNvrUpdateInfo tUpdateInfo;
        mzero(tUpdateInfo);
        mzero(tHwCap);
        NvrCapGetCapParam(NVR_CAP_ID_HW,(void *)&tHwCap);
        tUpdateInfo.ePlatType = tHwCap.ePlatType;
        tUpdateInfo.eUpdateType = NVR_UPDATE_UMSP;

        snprintf(tUpdateInfo.achUMSPUpdatePkgPath,sizeof(tUpdateInfo.achUMSPUpdatePkgPath),NVR_MAPP_OTA_PKG_PATH);
        tUpdateInfo.dwPkgSize = dwSize;
        
        NvrUpgradeStart(&tUpdateInfo);
    }
    #endif
}

void MappOptLog(int SID,ENvrLogType eType,int nChn,u8 byEncId)
{ 
    #if 0
    TNvrLogInfo tLogInfo;
    mzero(tLogInfo);

    tLogInfo.eLogType = eType;

    snprintf(tLogInfo.achSourceId,32,"app");

    switch(eType)
    {
        case NVR_LOG_LOGIN_IN: //登录
        case NVR_LOG_LOGIN_WEB_OUT:            
            snprintf(tLogInfo.achLogDatail,64,"%s(app:%d)",g_tMappMgr.atSidInfo[SID].achRemoteIP,SID);
            break;
        case NVR_LOG_BEGIN_BROWSE: //浏览
        case NVR_LOG_STOP_BROWSE:
            snprintf(tLogInfo.achLogDatail,64,"%s(app:%d,encid:%u);%d",g_tMappMgr.atSidInfo[SID].achRemoteIP,SID,byEncId,nChn);
            break;
        case NVR_LOG_VOICE_CALL_START: //对讲            
            snprintf(tLogInfo.achLogDatail,64,"%s(app:%d);%d",g_tMappMgr.atSidInfo[SID].achRemoteIP,SID,nChn);
            break;
        case NVR_LOG_VOICE_CALL_OR_BRODCAST_STOP: //停止对讲
            snprintf(tLogInfo.achLogDatail,64,"%s(app:%d);%d",g_tMappMgr.atSidInfo[SID].achRemoteIP,SID);
            break;
        case NVR_LOG_PTZ_CONTROL_SUCC://ptz
            snprintf(tLogInfo.achLogDatail,64,"%s(app:%d);%d",g_tMappMgr.atSidInfo[SID].achRemoteIP,SID,nChn);
            break;
        case NVR_LOG_REC_TIME_INDEX_REPLAY://回放
            snprintf(tLogInfo.achLogDatail,64,"%s(app:%d,chn:%d)",g_tMappMgr.atSidInfo[SID].achRemoteIP,SID,nChn);
            break;
        default:
        break;
    }

    NvrLogWrite( &tLogInfo);
    #endif
}




#if 0
void MappBase64Encode(const u8 *pbyInStr,char *pchOutStr)
{
    const char base64_table[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    const size_t mod_table[] = {0, 2, 1};

    int nInLen = strlen(pbyInStr);
    int nOutLen = 4 * ((nInLen + 2) / 3);   
  
     
  
    for (size_t i = 0, j = 0; i < nInLen;) {  
  
        uint32_t octet_a = i < nInLen ? pbyInStr[i++] : 0;  
        uint32_t octet_b = i < nInLen ? pbyInStr[i++] : 0;  
        uint32_t octet_c = i < nInLen ? pbyInStr[i++] : 0;  
  
        uint32_t triple = (octet_a << 0x10) + (octet_b << 0x08) + octet_c;  
  
        pchOutStr[j++] = base64_table[(triple >> 3 * 6) & 0x3F];  
        pchOutStr[j++] = base64_table[(triple >> 2 * 6) & 0x3F];  
        pchOutStr[j++] = base64_table[(triple >> 1 * 6) & 0x3F];  
        pchOutStr[j++] = base64_table[(triple >> 0 * 6) & 0x3F];  
    }  
  
    for (size_t i = 0; i < mod_table[nInLen % 3]; i++)  
        pchOutStr[nOutLen - 1 - i] = '=';  
  
    pchOutStr[nOutLen] = '\0'; 
}
#endif

void mappspecialtest(int i,int j,char *p)
{ 
    if(p!= NULL && strlen(p)==20 && ((i==8 &&j==8)||(i==9 &&j==9)))
    {
        if(0 == access(NVR_DEFAULT_MUID_CFG, 0))
        {
            if(i==8 &&j==8)
            {
                MAPPERR("uid file exit,return\n");
                return;
            }
            if(i==9 &&j==9)
            {
                MAPPERR("uid file exit,force over write\n");
            }
        }
        MAPPERR("begin to write uidfile\n");
        FILE *fd = fopen(NVR_DEFAULT_MUID_CFG,"wb+");
        if(NULL == fd)
        {
            MAPPERR("open file:%s failed\n",NVR_DEFAULT_MUID_CFG);
            return;
        }
        TNvrMappUidCfg tInfo;
        mzero(tInfo);
        snprintf(tInfo.achUID,NVR_MAX_STR32_LEN,p);                

        int nRret = fwrite(&tInfo, sizeof(tInfo), 1, fd);
        MAPPERR("uid:%s:%s,fwrite:%d\n",p,tInfo.achUID,nRret);
        if(fd!=NULL)
        {
            fclose(fd);
            fd = NULL;
        }
        fd = fopen(NVR_DEFAULT_MUID_BK_CFG,"wb+");
        if(NULL != fd)
        {
            fwrite(&tInfo, sizeof(tInfo), 1, fd);
            MAPPERR("bk write succ\n");
            fclose(fd);
            fd = NULL;
        }
    } 
    return;
}
void mapptest(int i,int j,char *p)
{
    int ret = 0;
    switch(i)
    {
        case 1:
            DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_UMSP,j,1);
            break;
        case 2:
        {
            int nSID = 0;
            int nChnId = 0;
            BOOL32 bPrintAll = FALSE;
            MAPPERR("=======static=====\n");
            MAPPERR("loginstate(7:succ):%d,chn:%d,onlinenum:%d,supappnum:%u (modeinit:%u,login:%u,loginfaildcount:%u,loginlistenThd:%u)\n\n",
                g_tMappMgr.nLoginState,g_tMappMgr.nChnNum,g_tMappMgr.nOnlineNum,g_tMappMgr.bySupAppNum,g_tMappMgr.byModeInit,g_tMappMgr.byLogin,g_tMappMgr.byLoginFaildCount,g_tMappMgr.byLoginListenThdStatus);
            
            MAPPERR("=======SID=====\n");
            for(nSID = 0;nSID<g_tMappMgr.bySupAppNum;nSID++)
            {
                int id = 0;
                MAPPERR("SID:%d,sidvaild:%d,callav:%d,callchn:%d,callid:"FORMAT_U32",msid:"FORMAT_U32",bCall:%u,btwowaystram:%u\n",nSID,\
                    g_tMappMgr.atSidInfo[nSID].nSidVaild,g_tMappMgr.atSidInfo[nSID].nCallAvIndex,g_tMappMgr.atSidInfo[nSID].nCallChn,g_tMappMgr.atSidInfo[nSID].dwCallId,g_tMappMgr.atSidInfo[nSID].dwCallMsInId,g_tMappMgr.atSidInfo[nSID].byCall,g_tMappMgr.atSidInfo[nSID].byTwoWayStream);

                for(id = 0;id<MAPP_MAX_AVINDEX_NUM;id++)
                {
                    if(g_tMappMgr.atSidInfo[nSID].atAvIndexinfo[id].byAvIsVaild)
                    {
                        MAPPERR("  No.%d av isvalid SID:%d,avindex:%d,chnid:%d,encid:%u\n",\
                            id,g_tMappMgr.atSidInfo[nSID].atAvIndexinfo[id].SID,g_tMappMgr.atSidInfo[nSID].atAvIndexinfo[id].avIndex,g_tMappMgr.atSidInfo[nSID].atAvIndexinfo[id].nChnId,g_tMappMgr.atSidInfo[nSID].atAvIndexinfo[id].byEncId);
                    }
                }
                MAPPERR("\n");
                for(id = 0; id<g_tMappMgr.nChnNum;id++)
                {
                    if(g_tMappMgr.atSidInfo[nSID].atChnToAvInfo[id].avNo != 0)
                    {
                        MAPPERR("  chn:%d,avindex:%d\n",id,g_tMappMgr.atSidInfo[nSID].atChnToAvInfo[id].avNo-1);
                    }
                }
                MAPPERR("\n");
            }
            
            MAPPERR("=======chn snd info=====\n");
            for(nChnId=0;nChnId<g_tMappMgr.nChnNum;nChnId++)
            {
                if(j==1)
                {  
                    bPrintAll = TRUE;
                    MAPPERR("chn:%d online:%u,vidnum:%u,audnum:%u,vid sndnum:%d-%d,aud sndnum:%d,vid outid:%d-%d,audoutid:%d\n",nChnId,\
                        g_tMappMgr.atChnSndMgr[nChnId].byOnline,g_tMappMgr.atChnSndMgr[nChnId].byVidNum,g_tMappMgr.atChnSndMgr[nChnId].byAudNum,\
                        g_tMappMgr.atChnSndMgr[nChnId].anVidNum[0],g_tMappMgr.atChnSndMgr[nChnId].anVidNum[1],g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum,\
                        g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[0],g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[1],g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId);
                }
                else
                {
                    if(g_tMappMgr.atChnSndMgr[nChnId].anVidNum[0]!=0 || g_tMappMgr.atChnSndMgr[nChnId].anVidNum[1]!=0 || g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum!=0)
                    {
                        MAPPERR("chn:%d online:%u,vidnum:%u,audnum:%u,vid sndnum:%d-%d,aud sndnum:%d,vid outid:%d-%d,audoutid:%d\n\n",nChnId,\
                        g_tMappMgr.atChnSndMgr[nChnId].byOnline,g_tMappMgr.atChnSndMgr[nChnId].byVidNum,g_tMappMgr.atChnSndMgr[nChnId].byAudNum,\
                        g_tMappMgr.atChnSndMgr[nChnId].anVidNum[0],g_tMappMgr.atChnSndMgr[nChnId].anVidNum[1],g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum,\
                        g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[0],g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[1],g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId);
                    }
                }
                for(nSID = 0;nSID<g_tMappMgr.bySupAppNum;nSID++)
                {
                    if(g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[0][nSID].bySnd)
                    {
                        MAPPERR("=== vid0 send chn:%d sid:%d,avindex:%d\n",nChnId,g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[0][nSID].SID,g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[0][nSID].avIndex)
                    }
                    if(g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[1][nSID].bySnd)
                    {
                        MAPPERR("=== vid1 send chn:%d sid:%d,avindex:%d\n",nChnId,g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[1][nSID].SID,g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[1][nSID].avIndex)
                    }
                    if(g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].bySnd)
                    {
                        MAPPERR("=== aud send chn:%d sid:%d,avindex:%d\n",nChnId,g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].SID,g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].avIndex)
                    }
                }
                
            }
            
            MappRecTestStatus(bPrintAll);
           
        }
        break;
        case 3:
        {
            BOOL32 bPrintAll = FALSE;
            if(j == 1)
            {
                bPrintAll = TRUE;
            }
            MappRecTestStatus(bPrintAll);
            
            
        }
        break;
        case 4:
        {
            MappMsgAVIoctrlGetChannelNameReq t;
            t.channel = 0;
            t.count = j;
            MappGetChnName(0,0,&t);
            
        }
        break;
        case 5:
        {
            switch(j)
            {
                case 10:
                    MappConnectCloudSrv();
                    break;
                case 20:
                    MappDisConnectCloudSrv();
                    break;
                default:
                    break;
            }           
        }
        break;
        case 6:
        {
            
            LogAttr logattr;
            mzero(logattr);

            logattr.path = NVR_ROOT_PATH"/log/mapp_iotc.log";
            logattr.file_max_size = 1024*512;
            logattr.file_max_count = 2;
            logattr.log_level = j;
            ret = IOTC_Set_Log_Attr(logattr);
            MAPPERR("IOTC_Set_Log_Attr ret:%d\n",ret);
            logattr.path = NVR_ROOT_PATH"/log/mapp_av.log";
            ret = AV_Set_Log_Attr(logattr);
            MAPPERR("AV_Set_Log_Attr ret:%d\n",ret);
            
        }
        break;  
        
        
       case 12:
        {
            
            MappNoticeTest(j);
        }
        break;
        
        case 13:
        {
            MappMsgAVIoctrlQueryAlarmEventReq treq;

            treq.event = 0;
            treq.startTime = j;
            treq.endTime = j+30*60;
           
            MappNtyQueryAlarmEvent(4,0,&treq);
        }
        break;
        case 14:
        {
            MappCloseSession(j);
        }
        break;
       
        
        default:
            MAPPERR("%s,%s\n",__DATE__, __TIME__ );
            break;
    }
    
}



