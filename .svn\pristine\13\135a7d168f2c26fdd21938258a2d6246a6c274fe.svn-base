

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
DO_UPX = 0
LINUX_COMPILER = _HIS3559A_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
#CFLAGS += -D__MRTC__
CFLAGS += -D_HIS3559A_
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/his3559a
LIB_PATH += $(TOP)/../../10-common/lib/release/his3559a/audlib
#LIB_PATH += $(TOP)/../../10-common/lib/release/his3559a/webrtc
LIB_PATH += $(TOP)/../../10-common/lib/release/his3559a/wifim

##todo: aec_3A_mulresample_armhi3559a_linux should be replaced by armhi3519 version

LIBS +=	nvrcfg udm nvrlog nvrcap nvrcustcap nvrusrmgr nvredgeosunitif nvrnetwork nvrftp nvrsys nvrproto protobufc sqlite3wrapper malloc debuglog netcbb mbnet wifi ddnsc upnpc drv pthread nvrgeo \
	sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu nvrprobe httpclient mxml ghttp go osp mediaswitch netpacket kdmtsps kdvencrypt stdc++ nvrqueue mediactrl ispctrl nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp airp nvrpcap kdssl-ext dl ais algctrl kdvsys SDEF pcap pubsecstack curl nvrcoi msc nvrunifiedlog\
	aec_mulresample_armhi3559a_linux audcodec_armhi3559a_linux stdg722_armhi3559a_linux mp2_armhi3559a_linux audproc_armhi3559a_linux \
	g711_armhi3559a_linux g722_armhi3559a_linux g726_armhi3559a_linux g7221c_armhi3559a_linux \
	extexp_armhi3559a_linux resamplev2_armhi3559a_linux \
	adpcm_armhi3559a_linux aaclcdec_armhi3559a_linux aaclcenc_armhi3559a_linux \
	spe_armhi3559a_linux asd_armhi3559a_linux md5lib_armhisi3559a_linux \
	mixer_armhi3559a_linux g728_armhi3559a_linux g729_armhi3559a_linux g719_armhi3559a_linux \
	mp3dec_armhi3559a_linux mp3enc_armhi3559a_linux aaclddec_armhi3559a_linux aacldenc_armhi3559a_linux \
	opus_armhi3559a_linux amr_nb_armhi3559a_linux \
	ive basicintelligent_his3559A videomanage_armhi3559a_linux \
	smartcodec_armhi3559a_linux \
	m \
	kdmssl\
	kdmcrypto\
	cjson\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrdynamicplugin\
	encoder\
	nvrstitch\
	lwshelper\
	websockets\
	nvrmd5\
	checksshpwd
#    heapcheck\
#    tcmalloc
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif




## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/tts \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/cbb/kdssl-ext\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/system
		
#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_NO_UPX_APP_PATH := ../../../10-common/version/release/his3559a/bin_noupx
INSTALL_APP_PATH := ../../../10-common/version/release/his3559a/bin_ptzipc

include $(COMM_DIR)/common.mk


