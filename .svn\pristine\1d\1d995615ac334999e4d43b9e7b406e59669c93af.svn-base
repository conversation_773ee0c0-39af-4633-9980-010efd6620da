#ifndef _SCSI_H_
#define _SCSI_H_

#define SAT_ATA_PASSTHROUGH_12 0xa1
#define SAT_ATA_PASSTHROUGH_16 0x85

#define DXFER_NONE        0
#define DXFER_FROM_DEVICE 1
#define DXFER_TO_DEVICE   2
#pragma pack(push)
#pragma pack(4)

struct scsi_cmnd_io {
	unsigned char * cmnd; 	/* [in]: ptr to SCSI command block (cdb) */
	size_t  cmnd_len;   	/* [in]: number of bytes in SCSI command */
	int dxfer_dir;      	/* [in]: DXFER_NONE, DXFER_FROM_DEVICE, or
					DXFER_TO_DEVICE */
	unsigned char * dxferp; /* [in]: ptr to outgoing or incoming data buffer */
	size_t dxfer_len;	/* [in]: bytes to be transferred to/from dxferp */
	unsigned char * sensep; /* [in]: ptr to sense buffer, filled when
                                 CHECK CONDITION status occurs */
	size_t max_sense_len; 	/* [in]: max number of bytes to write to sensep */
	unsigned timeout;   	/* [in]: seconds, 0-> default timeout (60 seconds?) */
	int pack_id;
	size_t resp_sense_len;  /* [out]: sense buffer length written */
	unsigned char scsi_status;	/* [out]: 0->ok, 2->CHECK CONDITION, etc ... */
	int resid;          	/* [out]: Number of bytes requested to be transferred less actual number transferred (0 if not supported) */
};

struct scsi_sense_disect {
	unsigned char resp_code;
	unsigned char sense_key;
	unsigned char asc;
	unsigned char ascq;
	int progress; /* -1 -> N/A, 0-65535 -> available */
};
#pragma pack(pop)

/* defines for useful SCSI Status codes */
#define SCSI_STATUS_CHECK_CONDITION     0x2

/* defines for useful Sense Key codes */
#define SCSI_SK_NO_SENSE                0x0
#define SCSI_SK_RECOVERED_ERR           0x1
#define SCSI_SK_NOT_READY               0x2
#define SCSI_SK_MEDIUM_ERROR            0x3
#define SCSI_SK_HARDWARE_ERROR          0x4
#define SCSI_SK_ILLEGAL_REQUEST         0x5
#define SCSI_SK_UNIT_ATTENTION          0x6
#define SCSI_SK_ABORTED_COMMAND         0xb

/* defines for useful Additional Sense Codes (ASCs) */
#define SCSI_ASC_NOT_READY              0x4     /* more info in ASCQ code */
#define SCSI_ASC_NO_MEDIUM              0x3a    /* more info in ASCQ code */
#define SCSI_ASC_UNKNOWN_OPCODE         0x20
#define SCSI_ASC_INVALID_FIELD          0x24
#define SCSI_ASC_UNKNOWN_PARAM          0x26
#define SCSI_ASC_WARNING                0xb
#define SCSI_ASC_IMPENDING_FAILURE      0x5d

#define SCSI_ASCQ_ATA_PASS_THROUGH      0x1d

/* Simplified error code (negative values as per errno) */
#define SIMPLE_NO_ERROR                 0
#define SIMPLE_ERR_NOT_READY            1
#define SIMPLE_ERR_BAD_OPCODE           2
#define SIMPLE_ERR_BAD_FIELD            3       /* in cbd */
#define SIMPLE_ERR_BAD_PARAM            4       /* in data */
#define SIMPLE_ERR_BAD_RESP             5       /* response fails sanity */
#define SIMPLE_ERR_NO_MEDIUM            6       /* no medium present */
#define SIMPLE_ERR_BECOMING_READY       7       /* device will be ready soon */
#define SIMPLE_ERR_TRY_AGAIN            8       /* some warning, try again */
#define SIMPLE_ERR_MEDIUM_HARDWARE      9       /* medium or hardware error */
#define SIMPLE_ERR_UNKNOWN              10      /* unknown sense value */
#define SIMPLE_ERR_ABORTED_COMMAND      11      /* most likely transport error */

/* SCSI command timeout values (units are seconds) */
#define SCSI_TIMEOUT_DEFAULT    20  // should be longer than the spin up time
                                    // of a disk in standby mode.


extern int scsi_simple_sense_filter(struct scsi_sense_disect *sinfo);
extern void scsi_do_sense_disect(struct scsi_cmnd_io * in, struct scsi_sense_disect *out);

extern int scsi_std_inquiry(int fd, unsigned char *req_buff, unsigned int req_len);

unsigned char *sg_scsi_sense_desc_find(unsigned char * sensep, int sense_len, int desc_type);

#endif 
