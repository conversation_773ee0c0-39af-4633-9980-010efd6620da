#include "mapp.h"
#include "mappstream.h"
#include "mapperror.h"



#define MAPP_MSG_ONLINE 1
#define MAPP_MSG_OFFLINE 2

#define MAPP_MSG_PWD_CHANGE_TYPE 512
#define MAPP_MSG_PWD_CHANGE 3




 ///<deal chn online or offline
 static PTNvrDoubleList g_ptMappDealQueue = NULL;


 static NVRSTATUS MappPushQueueOpt(u32 dwOptType,u8 byMergerType,u32 dwLen,s8 *pBuf);
 static void MappDealChnDisconnect(u16 wChnId, ENvrStreamDiscnctType eDiscnctType, void* pContext);
 static void MappPwdChange(const u8 *pbyUserName, const u32 dwUserNameLen, const ENvrUserCfgChangeType eChangeType, void *pCBParam);



static void MappVidFrameDateCB(void* pData, void* pvContext)
{    
	s32 nRet = 0; 
    TMSFrame *ptFrameDate = NULL;   
    int i = 0;
    int nChn = -1;
    u16 wEncId = 0;
    int nCount = 0;
	
	
    
    if(NULL == pData)
    {
        return;
    }
    if(NULL != pvContext)
    {
        int n = 0;
        memcpy(&n, pvContext, sizeof(s32));

        wEncId = n>>16;
        nChn = n & 0xFFFF;
        MAPPFRQ("chnid:%d,encid:%d\n",nChn,wEncId); 
    }
    else
    {
        ptFrameDate = pData;
        if(NULL !=ptFrameDate)
        {
            ptFrameDate->MSFreeFrame(ptFrameDate);
        }
        MAPPIMP("pvContext is null\n");        
        return;
    }
    
    
    ptFrameDate = pData;
    if (g_tMappMgr.nOnlineNum==0)
    {
        if(NULL !=ptFrameDate)
        {
            ptFrameDate->MSFreeFrame(ptFrameDate);
        }
        MAPPFRQ("not usr online\n");        
        return;
    }
    

    FRAMEINFO_t frameInfo;
    // *** set Video Frame info here ***
    memset(&frameInfo, 0, sizeof(FRAMEINFO_t));
    frameInfo.codec_id = MappConverPlayload(ptFrameDate->m_tFrame.m_byMediaType);
    frameInfo.flags = 0x00;
    if(ptFrameDate->m_tFrame.x.m_tVideoParam.m_bKeyFrame)

    {
        frameInfo.flags = 0x01;
    }
      
    frameInfo.onlineNum = g_tMappMgr.nOnlineNum;
    
    

    for(i = 0;i<g_tMappMgr.bySupAppNum;i++)
    {
       MAPPFRQ("chnId:%d,encid:%u,bsnd:%d,avindex:%d\n",nChn,wEncId,g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].bySnd,g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].avIndex);  
       if(g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].bySnd == 1)
       {
            if(g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].dwLastRtpTime == 0)
            {
                g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].dwLastRtpTime = ptFrameDate->m_tFrame.m_dwTimeStamp;
                g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].dwRtpTime = ptFrameDate->m_tFrame.m_dwTimeStamp;
                frameInfo.timestamp = ptFrameDate->m_tFrame.m_dwTimeStamp;
                MAPPDBG("first snd time:%u\n",frameInfo.timestamp);
            }
            else
            {
                //MAPPDBG("delt time:%u\n",(ptFrameDate->m_tFrame.m_dwTimeStamp-g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].dwLastRtpTime)/90);
                g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].dwRtpTime += (ptFrameDate->m_tFrame.m_dwTimeStamp-g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].dwLastRtpTime)/90;
                g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].dwLastRtpTime = ptFrameDate->m_tFrame.m_dwTimeStamp;
                frameInfo.timestamp = g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].dwRtpTime;
            }
            nRet = avSendFrameData(g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].avIndex, ptFrameDate->m_tFrame.m_pData, ptFrameDate->m_tFrame.m_dwDataSize, &frameInfo, sizeof(FRAMEINFO_t));
            nCount++;  
            if(AV_ER_NoERROR !=nRet)
            {
                char achStr[NVR_MAX_STR256_LEN];
                mzero(achStr);
                MappAvErrToStr(nRet,achStr);
                MAPPFRQ("chnId:%d,encid:%u,avindex %d vid snd,cont:%d-%d,ret%s\n",
                    nChn,wEncId,g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].avIndex,g_tMappMgr.atChnSndMgr[nChn].anVidNum[wEncId],nCount,achStr);
                
            }
            else
            {
                MAPPFRQ("chnId:%d,encid:%u,avindex %d vid snd,cont:%d-%d,ret%d,time:%u\n",
                    nChn,wEncId,g_tMappMgr.atChnSndMgr[nChn].aatVidSndInfo[wEncId][i].avIndex,g_tMappMgr.atChnSndMgr[nChn].anVidNum[wEncId],nCount,nRet,frameInfo.timestamp);
            }
            if(nCount == g_tMappMgr.atChnSndMgr[nChn].anVidNum[wEncId])
            {
                break;
            }
       }     
        
    }    
    
    if(NULL !=ptFrameDate)
    {
        ptFrameDate->MSFreeFrame(ptFrameDate);
    }
    

   
    return;
}
static void MappAudFrameDateCB(void* pData, void* pvContext)
{    
	s32 nRet = 0; 
    TMSFrame *ptFrameDate = NULL;   ///<帧数据;
    int nChn = 0;
    int i= 0;
    int j = 0;	
	ENUM_AUDIO_SAMPLERATE eSample = AUDIO_SAMPLE_8K;
    ENUM_AUDIO_DATABITS eDataBits = AUDIO_DATABITS_16;  ///<ipc都是16位
    ENUM_AUDIO_CHANNEL eChannel = AUDIO_CHANNEL_MONO;   ///<ipc都是单声道，aaclc只有在svr产品可配
	
    
    if(NULL == pData)
    {
        return;
    }
    
    if(NULL != pvContext)
    {
        memcpy(&nChn, pvContext, sizeof(s32));
        MAPPFRQ("chnid:%d\n",nChn); 
    }
    else
    {
        ptFrameDate = pData;
        if(NULL !=ptFrameDate)
        {
            ptFrameDate->MSFreeFrame(ptFrameDate);
        }
        MAPPIMP("pvContext is null\n");        
        return;
    }

    
    ptFrameDate = pData;
    if (g_tMappMgr.nOnlineNum==0)
    {
        if(NULL !=ptFrameDate)
        {
            ptFrameDate->MSFreeFrame(ptFrameDate);
        }
        MAPPFRQ("nOnlineNum is 0\n");        
        return;
    }
    

    FRAMEINFO_t frameInfo;
    // *** set Video Frame info here ***
    memset(&frameInfo, 0, sizeof(FRAMEINFO_t));
    
    frameInfo.codec_id = MappAudConverPlayload(ptFrameDate->m_tFrame.m_byMediaType); //sdk自适应，不需要转换
    switch (ptFrameDate->m_tFrame.x.m_tAudioParam.m_dwSample)
    {
        case 8000:
            eSample = AUDIO_SAMPLE_8K;
            break;
        case 16000:
            eSample = AUDIO_SAMPLE_16K;
            break;
        case 32000:
            eSample = AUDIO_SAMPLE_32K;
            break;
        case 44000:
            eSample = AUDIO_SAMPLE_44K;
            break;
        case 48000:
            eSample = AUDIO_SAMPLE_48K;
            break;
        default:
            break;
    }

    frameInfo.dataBit = eDataBits;
    if(MEDIA_TYPE_G726_32 == ptFrameDate->m_tFrame.m_byMediaType)
    {
        frameInfo.dataBit = AUDIO_DATABITS_32;
        eDataBits = AUDIO_DATABITS_16;
        MAPPFRQ("aud databit:%u-%d,mediatye:%u\n",ptFrameDate->m_tFrame.x.m_tAudioParam.m_wBitsPerSample,eDataBits,ptFrameDate->m_tFrame.m_byMediaType);
    }

    MAPPFRQ("aud databit:%u,mediatye:%u\n",ptFrameDate->m_tFrame.x.m_tAudioParam.m_wBitsPerSample,ptFrameDate->m_tFrame.m_byMediaType);
    ///<k ipc都是固定16和单
    #if 0  
    if(16 == ptFrameDate->m_tFrame.x.m_tAudioParam.m_wBitsPerSample)
    {
        eDataBits = AUDIO_DATABITS_16;
    }
    else
    {
        eDataBits = AUDIO_DATABITS_8;
    }
    
    if (1 == ptFrameDate->m_tFrame.x.m_tAudioParam.m_wChannel)
    {
        eChannel = AUDIO_CHANNEL_MONO;        
    }
    else
    {
        eChannel = AUDIO_CHANNEL_STERO;
        
    }
    #endif
   
    frameInfo.flags = (eSample << 2) | (eDataBits << 1) | eChannel;
    //frameInfo.dataBit = eDataBits;
    MAPPFRQ("sample:%d,datebits:%d,chanel:%d,flags:%d,%d\n",eSample,eDataBits,eChannel,frameInfo.flags,frameInfo.dataBit);
    //frameInfo.timestamp = getTimeStamp();
    frameInfo.onlineNum = g_tMappMgr.nOnlineNum;    
    

    for(i = 0;i<g_tMappMgr.atChnSndMgr[nChn].nAudStreamNum;i++)
    {
        for(j=0;j<g_tMappMgr.bySupAppNum;j++)
        {
            if(g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].bySnd)
            {
                #if 1
                if(g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].dwAudLastRtpTime == 0)
                {
                    g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].dwAudLastRtpTime = ptFrameDate->m_tFrame.m_dwTimeStamp;
                    g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].dwAudRtpTime = ptFrameDate->m_tFrame.m_dwTimeStamp;
                    frameInfo.timestamp = ptFrameDate->m_tFrame.m_dwTimeStamp;
                    MAPPDBG("first snd time:%u\n",frameInfo.timestamp);
                }
                else
                {
                    u32 dwSample = ptFrameDate->m_tFrame.x.m_tAudioParam.m_dwSample/1000;
                    if(0 != dwSample)
                    {
                        //MAPPDBG("delt time:%u\n",(ptFrameDate->m_tFrame.m_dwTimeStamp-g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].dwAudLastRtpTime)/dwSample);
                        g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].dwAudRtpTime += (ptFrameDate->m_tFrame.m_dwTimeStamp-g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].dwAudLastRtpTime)/dwSample;
                        g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].dwAudLastRtpTime = ptFrameDate->m_tFrame.m_dwTimeStamp;
                        frameInfo.timestamp = g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].dwAudRtpTime;
                    }                    
                }
                #endif
                
                nRet = avSendAudioData(g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].avIndex, ptFrameDate->m_tFrame.m_pData, ptFrameDate->m_tFrame.m_dwDataSize, &frameInfo, sizeof(FRAMEINFO_t));
                if(AV_ER_NoERROR !=nRet)
                {
                    char achStr[NVR_MAX_STR256_LEN];
                    mzero(achStr);
                    MappAvErrToStr(nRet,achStr);
                    MAPPFRQ("send aud chn:%d,avindex:%d ret:%s\n",nChn,g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].avIndex,achStr);
                    
                }
                else
                {
                    MAPPFRQ("send aud chn:%d,avindex:%d datasize:"FORMAT_U32" ret:%d,time:%u\n",nChn,g_tMappMgr.atChnSndMgr[nChn].atAudSndInfo[j].avIndex,ptFrameDate->m_tFrame.m_dwDataSize,nRet,frameInfo.timestamp);
                }
                
            }
        }        
    }

    
    
    if(NULL !=ptFrameDate)
    {
        ptFrameDate->MSFreeFrame(ptFrameDate);
    }
   
    return;
}

static NVRSTATUS MappCreateVidMsout(u16 wChnId,u16 wEncId,u32 *pdwMsOutId,u32 *pdwPipelineId)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrVtduStreamFrameParams tParam;
    
    
    mzero(tParam);
    tParam.eMediaType = NVR_MEDIA_VEDIO;
    tParam.eBrowseytpe = NVR_REQ_FRAME_SND_BY_UMSP;
    tParam.wEncId = wEncId;
    tParam.pSndDiscnctCB = MappDealChnDisconnect;
    tParam.bSetFrameCB = TRUE;
    tParam.tFrameCBParam.tTrackId.dwTrackIndx = 0;
    tParam.tFrameCBParam.tTrackId.eMediaType = NVR_MEDIA_VEDIO;
    tParam.tFrameCBParam.eDataPackFormat = NVR_DATAPACK_FORMAT_FRAME;
    tParam.tFrameCBParam.pfDataCallBackProc = MappVidFrameDateCB;
    
    tParam.tFrameCBParam.pvContext = (void *)&g_tMappMgr.atChnSndMgr[wChnId].anChn[wEncId];
    eRet = NvrVtduCtrlSetFrameStreamParam(wChnId, &tParam, pdwMsOutId,pdwPipelineId);
    if(NVR_ERR__OK != eRet)
    {
        MAPPERR("NvrVtduCtrlSetFrameStreamParam vid chnid:%u encid:%u failed,ret:%d\n",wChnId,wEncId,eRet);
    }
    else
    {
        MAPPIMP("NvrVtduCtrlSetFrameStreamParam vid chnid:%u encid:%u outid:"FORMAT_U32",pipeline:"FORMAT_U32" succ\n",wChnId,wEncId,*pdwMsOutId,*pdwPipelineId);
        NvrVtduCtrlForceKeyFrame(wChnId,wEncId);
    }
    return eRet;   
                   
}

static NVRSTATUS MappCreateAudMsout(u16 wChnId,u32 *pdwMsOutId,u32 *pdwPipelineId)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrVtduStreamFrameParams tParam;
    mzero(tParam);
    
    tParam.eMediaType = NVR_MEDIA_AUDIO;
    tParam.eBrowseytpe = NVR_REQ_FRAME_SND_BY_UMSP;
    tParam.wEncId = 0;
    tParam.pSndDiscnctCB = MappDealChnDisconnect;
    tParam.bSetFrameCB = TRUE;
    tParam.tFrameCBParam.tTrackId.dwTrackIndx = 0;
    tParam.tFrameCBParam.tTrackId.eMediaType = NVR_MEDIA_AUDIO;
    tParam.tFrameCBParam.eDataPackFormat = NVR_DATAPACK_FORMAT_FRAME;
    tParam.tFrameCBParam.pfDataCallBackProc = MappAudFrameDateCB;
      
    tParam.tFrameCBParam.pvContext = (void *)&g_tMappMgr.atChnSndMgr[wChnId].nAudChn;
    eRet = NvrVtduCtrlSetFrameStreamParam(wChnId, &tParam, pdwMsOutId,pdwPipelineId);
    if(NVR_ERR__OK != eRet)
    {
        MAPPERR("NvrVtduCtrlSetFrameStreamParam aud chnid:%u failed,ret:%d\n",wChnId,eRet);
    }
    else
    {
        MAPPIMP("NvrVtduCtrlSetFrameStreamParam aud chnid:%u context:"FORMAT_U32" msoutid:"FORMAT_U32" succ\n",wChnId,g_tMappMgr.atChnSndMgr[wChnId].nAudChn,*pdwMsOutId,*pdwPipelineId);
    }
    
    return eRet;
}

///<增加发送节点
static int MappAddChnSndNode(int nChnId,int nSID,int avIndex,u8 byVid,u8 byEncId)
{
    int nRet = 0;

    MAPPIMP("chid:%d,sid:%d,avIndex:%d,bVid:%u,encId:%u\n",nChnId,nSID,avIndex,byVid,byEncId);
    //合法性校验？
    
    ///<新增节点

    MAPP_SEMTAKE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);
    
    do 
    {
        
        if(byVid)
        {
            if(0 == g_tMappMgr.atChnSndMgr[nChnId].byOnline || 0 == g_tMappMgr.atChnSndMgr[nChnId].byVidNum)
            {
                NVRSTATUS eRet = NVR_ERR__OK;
                TNvrPuiDevParam atDevParam[1];
                u16 wNum = 1;
                
                mzero(atDevParam);
                eRet = NvrPuiGetDevList(nChnId,atDevParam,&wNum);
                if(NVR_ERR__OK == eRet)
                {
                    if(atDevParam[0].tDevStatus.eConnectStatus == NVR_PUI_ONLINE)
                    {
                        g_tMappMgr.atChnSndMgr[nChnId].byOnline = 1;
                        g_tMappMgr.atChnSndMgr[nChnId].byVidNum = (u8)atDevParam[0].tDevInfo.wVidEncNum;
                        g_tMappMgr.atChnSndMgr[nChnId].byAudNum = (u8)atDevParam[0].tDevInfo.wAudEncNum; 
                    }                    
                }
            }
            
            if(1 == g_tMappMgr.atChnSndMgr[nChnId].byOnline && byEncId>= g_tMappMgr.atChnSndMgr[nChnId].byVidNum)
            {                
                MAPPERR("chn:%d,sup vidnum:%u,encid:%d change to 0\n",nChnId,g_tMappMgr.atChnSndMgr[nChnId].byVidNum,byEncId);
                byEncId = 0;
            }
            //防止app重复请求
            if(0!=g_tMappMgr.atChnSndMgr[nChnId].anVidNum[byEncId] && 1 == g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[byEncId][nSID].bySnd)
            {
                MAPPERR("repeat snd,chid:%d,sid:%d,avIndex:%d,encId:%u,vidsndnum:%d,bysnd:%u\n",nChnId,nSID,avIndex,byEncId,g_tMappMgr.atChnSndMgr[nChnId].anVidNum[byEncId],g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[byEncId][nSID].bySnd);
                break;
            }
            g_tMappMgr.atChnSndMgr[nChnId].anChn[byEncId] = (byEncId<<16)|nChnId;
            MAPPIMP("chn:%d,encid:%d sndvidnum:%d,outid:%d\n",nChnId,byEncId,g_tMappMgr.atChnSndMgr[nChnId].anVidNum[byEncId],g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[byEncId]);
            if(0==g_tMappMgr.atChnSndMgr[nChnId].anVidNum[byEncId] && 0==g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[byEncId])
            { 
                //MAPPDBG("chn:%d,encid:%d begin create vid msout\n",nChnId,byEncId);
                MappCreateVidMsout(nChnId,byEncId,&g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[byEncId],&g_tMappMgr.atChnSndMgr[nChnId].adwVidPilelineId[byEncId]);
            }
            g_tMappMgr.atChnSndMgr[nChnId].anVidNum[byEncId]++;
            g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[byEncId][nSID].avIndex = avIndex;
            g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[byEncId][nSID].SID = nSID;
            g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[byEncId][nSID].bySnd = 1;

            MappOptLog(nSID,NVR_LOG_BEGIN_BROWSE,nChnId,byEncId);
        }
        else
        {
            g_tMappMgr.atChnSndMgr[nChnId].nAudChn = nChnId;  
            //防止app重复请求
            if(0!=g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum && 1 == g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].bySnd)
            {
                MAPPERR("repeat snd,chid:%d,sid:%d,avIndex:%d,audsndnum:%d,bysnd:%u\n",nChnId,nSID,avIndex,g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum,g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].bySnd);
                break;
            }
            MAPPIMP("chn:%d,sndaudnum:%d,outid:%d\n",nChnId,g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum,g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId);
            if(0==g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum && 0==g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId)
            {
                //MAPPIMP("chn:%d,begin create aud msout\n",nChnId);
                MappCreateAudMsout(nChnId,&g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId,&g_tMappMgr.atChnSndMgr[nChnId].dwAudPilelineId);
            }
            g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum++;
            g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].avIndex = avIndex;
            g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].SID = nSID;
            g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].bySnd = 1;
        }
    }while(0);

    MAPP_SEMGIVE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);
    return nRet;
}
///<删除发送节点
static int MappDelChnSndNode(int nChnId,int nSID,int avIndex,u8 byVid)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int i = 0;

    MAPPIMP("chid:%d,sid:%d,avIndex:%d,bVid:%u\n",nChnId,nSID,avIndex,byVid);
    //合法性校验？
    
    ///<删除节点
    MAPP_SEMTAKE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);

    if(byVid)
    {
        for(i = 0;i < MAPP_MAX_ENC_NUM;i++)
        {
            MAPPIMP("vidchn:%d,encid:%d,sid:%d-%d,avIndex:%d-%d,snd:%d,sndnum:%d\n",nChnId,i,nSID,g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[i][nSID].SID,
                avIndex,g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[i][nSID].avIndex,g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[i][nSID].bySnd,g_tMappMgr.atChnSndMgr[nChnId].anVidNum[i]);
            if((1 == g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[i][nSID].bySnd) &&
                (g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[i][nSID].avIndex == avIndex) &&
                (g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[i][nSID].SID == nSID))
            {
                g_tMappMgr.atChnSndMgr[nChnId].anVidNum[i]--;
                mzero(g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[i][nSID]);

                MAPPDBG("chn:%d,encid:%d,vid snd num:%d,outid:"FORMAT_U32"\n",nChnId,i,g_tMappMgr.atChnSndMgr[nChnId].anVidNum[i],g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[i]);
                if(0==g_tMappMgr.atChnSndMgr[nChnId].anVidNum[i] && 0!=g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[i])
                {
                    eRet = NvrVtduCtrlReleaseFrameStream(nChnId, NVR_REQ_FRAME_SND_BY_UMSP, NVR_MEDIA_VEDIO, g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[i],g_tMappMgr.atChnSndMgr[nChnId].adwVidPilelineId[i]);
                    MAPPIMP("release vid stream:chn:%d,encid:%d,outid:%d,ret:%d\n",nChnId,i,g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[i],eRet);
                    g_tMappMgr.atChnSndMgr[nChnId].adwVidOutId[i] = 0;
                    g_tMappMgr.atChnSndMgr[nChnId].adwVidPilelineId[i] = 0;
                }
                ///<暂时码流不停止
                MAPPIMP("Del vid succ\n");
                MappOptLog(nSID,NVR_LOG_STOP_BROWSE,nChnId,i);
                break;
            }
        }
    }
    else
    {
        if((1 == g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].bySnd) &&
            (g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].avIndex == avIndex) &&
            (g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].SID == nSID))
        {
            g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum--;
            g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].bySnd = 0;
            g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].avIndex = 0;
            g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[nSID].SID = 0;
            MAPPDBG("chn:%d,audsnd num:%d,outid:"FORMAT_U32"\n",nChnId,g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum,g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId);
            if(0==g_tMappMgr.atChnSndMgr[nChnId].nAudStreamNum && 0!=g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId)
            {
                eRet = NvrVtduCtrlReleaseFrameStream(nChnId, NVR_REQ_FRAME_SND_BY_UMSP, NVR_MEDIA_AUDIO, g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId,g_tMappMgr.atChnSndMgr[nChnId].dwAudPilelineId);
                MAPPIMP("release aud stream,chn:%d,outid:"FORMAT_U32",ret:%d\n",nChnId,g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId,eRet);
                g_tMappMgr.atChnSndMgr[nChnId].dwAudOutId = 0;
                g_tMappMgr.atChnSndMgr[nChnId].dwAudPilelineId = 0;
            }
            MAPPIMP("Del aud succ\n");
            
        }
        
    }
    
    

    MAPP_SEMGIVE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);
    return 0;
}

NVRSTATUS MappPushQueueOpt(u32 dwOptType,u8 byMergerType,u32 dwLen,s8 *pBuf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrDoubleListPushAttr tNodeAttr;

    mzero(tNodeAttr);

    tNodeAttr.byPriority = NVR_QUEUE_NODE_PRIORITY_NORMAL;
    tNodeAttr.byMergerType = byMergerType;
    tNodeAttr.dwType = dwOptType;
    tNodeAttr.pchDataBuf = pBuf;
    tNodeAttr.dwDataLen = dwLen;

    MAPPDBG("opttype:"FORMAT_U32",priority:%u,mergertype:%u,datalen:"FORMAT_U32"\n",\
        dwOptType,tNodeAttr.byPriority,tNodeAttr.byMergerType,tNodeAttr.dwDataLen);

    eRet = NvrQueuePush(g_ptMappDealQueue, &tNodeAttr);
    if(NVR_ERR__OK != eRet)
    {
        MAPPERR("NvrQueuePush failed ret:%d\n",eRet);
    }
    else
    {
        MAPPDBG("NvrQueuePush type:"FORMAT_U32" succ\n",dwOptType);
    }
    return eRet;
}

static void MappChnStatusCB(ENvrPuiStatusChangeType eStatusType,u16 wNode, void *pContext)
{
    char achBuf[NVR_MAX_STR32_LEN];

    MAPPDBG(" eStatusType=%d,wNode=%u.\n", eStatusType, wNode);
    ///<下线后一些跟通道相关配置或状态需要恢复默认值
    if(NVR_PUI_CHN_DEV_DEL == eStatusType && wNode<NVR_MAX_CHN_NUM)
    {
        mzero(achBuf);
        snprintf(achBuf,NVR_MAX_STR32_LEN,"%u",MAPP_MSG_OFFLINE);        
        MappPushQueueOpt(wNode,NVR_QUEUE_NODE_MERGER_NONE,strlen(achBuf)+1,achBuf);
    }
    return;
}
static void MappDealChnConnect(u16 wChnId, ENvrVtduStreamPreType eType,void *pContext)
{
    char achBuf[NVR_MAX_STR32_LEN];
    
    MAPPDBG("chnid:%u,connect\n",wChnId); 

    mzero(achBuf);
    snprintf(achBuf,NVR_MAX_STR32_LEN,"%u",MAPP_MSG_ONLINE);
    MappPushQueueOpt(wChnId,NVR_QUEUE_NODE_MERGER_NONE,strlen(achBuf)+1,achBuf);

    return;
}
void MappDealChnDisconnect(u16 wChnId, ENvrStreamDiscnctType eDiscnctType, void* pContext)
{
    MAPPIMP("chnid:%u,discontype:%d\n",wChnId,eDiscnctType); 

    char achBuf[NVR_MAX_STR32_LEN];

    mzero(achBuf);
    snprintf(achBuf,NVR_MAX_STR32_LEN,"%u",MAPP_MSG_OFFLINE);
    MappPushQueueOpt(wChnId,NVR_QUEUE_NODE_MERGER_NONE,strlen(achBuf)+1,achBuf);   

    return;
}

static void MappDevOnlineDeal(u16 wChnId)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u16 wNum = 1;
    TNvrPuiDevParam tDevParam;
    u8 byVidNum = MAPP_MAX_ENC_NUM;
    int i = 0;
    int j = 0;
    
    MAPP_SEMTAKE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);
    do 
    {
        if(wChnId>=MAPP_MAX_CHN_NUM)
        {
            MAPPERR("chnid:%u is over range:%d\n",wChnId,MAPP_MAX_CHN_NUM);
            break;
        }
        g_tMappMgr.atChnSndMgr[wChnId].byOnline = 1;
        
        mzero(tDevParam);

        eRet = NvrPuiGetDevList(wChnId, &tDevParam, &wNum);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("NvrPuiGetDevList failed:%d\n",eRet);
            break;
        }
        g_tMappMgr.atChnSndMgr[wChnId].byVidNum = (u8)tDevParam.tDevInfo.wVidEncNum;
        g_tMappMgr.atChnSndMgr[wChnId].byAudNum = (u8)tDevParam.tDevInfo.wAudEncNum;

        if(g_tMappMgr.atChnSndMgr[wChnId].byVidNum<MAPP_MAX_ENC_NUM)
        {
            byVidNum = g_tMappMgr.atChnSndMgr[wChnId].byVidNum;
        }

        for(i = 0;i<byVidNum;i++)
        {
            for(j = 0;j<g_tMappMgr.bySupAppNum;j++)
            {
                if(1 == g_tMappMgr.atChnSndMgr[wChnId].aatVidSndInfo[i][j].bySnd 
                    && 0 == g_tMappMgr.atChnSndMgr[wChnId].adwVidOutId[i])
                {
                    MappCreateVidMsout(wChnId, i, &g_tMappMgr.atChnSndMgr[wChnId].adwVidOutId[i], &g_tMappMgr.atChnSndMgr[wChnId].adwVidPilelineId[i]);
                    break;
                }
            }
        }
        
        if(g_tMappMgr.atChnSndMgr[wChnId].byAudNum>0 
            && g_tMappMgr.atChnSndMgr[wChnId].nAudStreamNum>0)
        {
            for(j = 0;j<g_tMappMgr.bySupAppNum;j++)
            {
                if(1== g_tMappMgr.atChnSndMgr[wChnId].atAudSndInfo[j].bySnd 
                    && 0 == g_tMappMgr.atChnSndMgr[wChnId].dwAudOutId)
                {
                    MappCreateAudMsout(wChnId, &g_tMappMgr.atChnSndMgr[wChnId].dwAudOutId, &g_tMappMgr.atChnSndMgr[wChnId].dwAudPilelineId);
                    break;
                }
            }
        }
        
    }while(0);


    MAPP_SEMGIVE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);
    
}
static void MappDevOfflineDeal(u16 wChnId)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int i = 0;
    
    MAPP_SEMTAKE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);
    do 
    {
        if(wChnId>=MAPP_MAX_CHN_NUM)
        {
            MAPPERR("chnid:%u is over range:%d\n",wChnId,MAPP_MAX_CHN_NUM);
            break;
        }
        g_tMappMgr.atChnSndMgr[wChnId].byOnline = 0;
        
        g_tMappMgr.atChnSndMgr[wChnId].byVidNum = 0;
        g_tMappMgr.atChnSndMgr[wChnId].byAudNum = 0;

        for(i = 0;i<MAPP_MAX_ENC_NUM;i++)
        {
            if(0 != g_tMappMgr.atChnSndMgr[wChnId].adwVidOutId[i])
            {
                eRet = NvrVtduCtrlReleaseFrameStream(wChnId, NVR_REQ_FRAME_SND_BY_UMSP, NVR_MEDIA_VEDIO, g_tMappMgr.atChnSndMgr[wChnId].adwVidOutId[i],g_tMappMgr.atChnSndMgr[wChnId].adwVidPilelineId[i]);
                MAPPIMP("release vid stream:%d\n",eRet);
                g_tMappMgr.atChnSndMgr[wChnId].adwVidOutId[i] = 0;
                g_tMappMgr.atChnSndMgr[wChnId].adwVidPilelineId[i] = 0;
                break;
            }           
        }

        if(0 != g_tMappMgr.atChnSndMgr[wChnId].dwAudOutId)
        {
            eRet = NvrVtduCtrlReleaseFrameStream(wChnId, NVR_REQ_FRAME_SND_BY_UMSP, NVR_MEDIA_AUDIO, g_tMappMgr.atChnSndMgr[wChnId].dwAudOutId,g_tMappMgr.atChnSndMgr[wChnId].dwAudPilelineId);
            MAPPIMP("release vid stream:%d\n",eRet);
            g_tMappMgr.atChnSndMgr[wChnId].dwAudOutId = 0;
            g_tMappMgr.atChnSndMgr[wChnId].dwAudPilelineId = 0;
            
        }
        
        
    }while(0);


    MAPP_SEMGIVE(g_tMappMgr.hMgrSem, MAPP_SEM_MGR);
    
}

void *MappDevUpDwnThread()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrDoubleListPopAttr tPopAttr;
    s8 achBuf[NVR_MAX_STR32_LEN] = { 0 };
    s32 nStatus = 0;
    
    TNvrPuiDevParam atDevParam[MAPP_MAX_CHN_NUM];
    u16 wNum = g_tMappMgr.nChnNum;
    int i = 0;

    prctl(PR_SET_NAME, "mappUpDwnDeal", 0, 0, 0);
    

    
    mzero(atDevParam);
    eRet = NvrPuiGetDevList(0,atDevParam,&wNum);
    if(NVR_ERR__OK == eRet)
    {
        for(i = 0;i<wNum;i++)
        {
            if(atDevParam[i].tDevStatus.eConnectStatus == NVR_PUI_ONLINE)
            {
                g_tMappMgr.atChnSndMgr[i].byOnline = 1;
                g_tMappMgr.atChnSndMgr[i].byVidNum = (u8)atDevParam[i].tDevInfo.wVidEncNum;
                g_tMappMgr.atChnSndMgr[i].byAudNum = (u8)atDevParam[i].tDevInfo.wAudEncNum; ///<根据源，有音频就请求第一路
            } 
        }
    }
    else
    {
        MAPPERR("NvrPuiGetDevList failed ret:%d\n",eRet);
    }

    mzero(tPopAttr);
    tPopAttr.byBlockMode = NVR_QUEUE_POP_BLOCK;
    tPopAttr.pchDataBuf = achBuf;
    tPopAttr.dwDataLen = NVR_MAX_STR32_LEN;

    while(TRUE)
    {
        eRet = NvrQueuePop(g_ptMappDealQueue, &tPopAttr);
        if(NVR_ERR__OK == eRet)
        {
            MAPPDBG("tpye:"FORMAT_U32"\n",tPopAttr.dwType);
            if(tPopAttr.dwType<MAPP_MAX_CHN_NUM)
            {
                ///<buf为上线或下线,tPopAttr.dwType为通道号
                nStatus = atoi(achBuf);
                MAPPDBG("chnid:"FORMAT_U32",(1-online,2-offline):%d\n",tPopAttr.dwType,nStatus);
                switch(nStatus)
                {
                    case MAPP_MSG_ONLINE:                  
                        MappDevOnlineDeal(tPopAttr.dwType);
                        break;
                    case MAPP_MSG_OFFLINE:
                        MappDevOfflineDeal(tPopAttr.dwType);                        
                        break;
                    default:
                        break;
                }
                
            }            
            else 
            { 
                if(MAPP_MSG_PWD_CHANGE_TYPE == tPopAttr.dwType)
                {
                    int i = 0;
                    nStatus = atoi(achBuf);
                    if(MAPP_MSG_PWD_CHANGE == nStatus)
                    {                        
                        for(i=0;i<g_tMappMgr.bySupAppNum;i++)
                        {
                            MappCloseSession(i);
                        }
                    }
                } 
                else
                {
                    MAPPDBG("tpye:"FORMAT_U32" is error\n",tPopAttr.dwType); 
                }
            }

        }
        else
        {
            MAPPERR("pop failed%d\n",eRet);
        }

    }
    return ;
}

void *MappAudCallReceiveThread(void *arg)
{
	int SID = *((int *)arg);
	free(arg);

    int avIndex = 0;

    if(1 == g_tMappMgr.atSidInfo[SID].byTwoWayStream)
    {
        avIndex = g_tMappMgr.atSidInfo[SID].nCallAvIndex;
        MAPPIMP("app suppt two say stream, sid:%d,callchn:%d,avindex:%d\n",SID,g_tMappMgr.atSidInfo[SID].nCallChn,avIndex);
    }
    else
    {
        AVClientStartInConfig avCltInCfg;
        AVClientStartOutConfig avCltOutCfg;

        mzero(avCltInCfg);
        mzero(avCltOutCfg);

        avCltInCfg.cb               = sizeof(AVClientStartInConfig);
        avCltInCfg.iotc_channel_id  = g_tMappMgr.atSidInfo[SID].nCallChn;
        avCltInCfg.iotc_session_id  = SID;
        avCltInCfg.timeout_sec      = 20;
        avCltInCfg.account_or_identity = "";
        avCltInCfg.password_or_token = "";
        avCltInCfg.resend           = 0;
        avCltInCfg.security_mode    = AV_SECURITY_SIMPLE;
        avCltInCfg.auth_type        = AV_AUTH_PASSWORD;
        avCltOutCfg.cb = sizeof(AVClientStartOutConfig);
        avIndex = avClientStartEx(&avCltInCfg, &avCltOutCfg);
        
        MAPPIMP("avClientStartEx sid:%d,callchn:%d,avindex:%d, resend:%d,twowaystrem:%d\n",SID,g_tMappMgr.atSidInfo[SID].nCallChn, avIndex, avCltOutCfg.resend, avCltOutCfg.two_way_streaming);
    }

        

	if(avIndex > -1)
	{
		char achBuf[AUDIO_BUF_SIZE];
		FRAMEINFO_t frameInfo;
		unsigned int frmNo = 0;
        u64 qwUtcMSeconds = 0;
		       

		if(1 != g_tMappMgr.atSidInfo[SID].byTwoWayStream) 
        {
            avClientCleanAudioBuf(g_tMappMgr.atSidInfo[SID].nCallChn);
		}
        
        
        
		while(g_tMappMgr.atSidInfo[SID].byCall)
		{            
			int nLen = avRecvAudioData(avIndex, achBuf, AUDIO_BUF_SIZE, (char *)&frameInfo, sizeof(FRAMEINFO_t), &frmNo);
            if(AV_ER_NoERROR > nLen)
            {
                char achStr[NVR_MAX_STR256_LEN];
                mzero(achStr);
                MappAvErrToStr(nLen,achStr);
                MAPPFRQ("sid:%d,callchn:%d,avindex:%d avRecvAudioData %s\n",SID,g_tMappMgr.atSidInfo[SID].nCallChn,avIndex,achStr);
            }
            else
            {
                TUMPFrame tFrame;                
                tFrame.m_pData = (u8 *)achBuf;
                tFrame.m_byMediaType = MEDIA_TYPE_PCMU; //同给的值
                tFrame.m_dwDataSize = nLen;
                tFrame.m_dwFrameID = frmNo;
                tFrame.m_dwTimeStamp = frameInfo.timestamp;
                //tFrame.m_dwSSRC = g_tMappMgr.atSidInfo[SID].nCallChn;
                
        		qwUtcMSeconds = NvrSysGetCurTimeMSec();
        		tFrame.m_llNTPTime = Cbb_UtcToNtpTime(qwUtcMSeconds);///<编码帧结构体赋值ntp时间戳
               
                
                MSRESULT nMsRet = MSInInputData(g_tMappMgr.atSidInfo[SID].dwCallMsInId,E_UMP_DataPackFormat_Frame,(void *)&tFrame, sizeof(TUMPFrame),NULL,0, NULL);
                MAPPFRQ("sid:%d,callchn:%d,avindex:%d size:%d,msret:%d\n",SID,g_tMappMgr.atSidInfo[SID].nCallChn,avIndex,nLen,nMsRet)
            }
            if(0 ==g_tMappMgr.atSidInfo[SID].byCall)
            {
                break;
            }
		}
	}
    else
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(avIndex,achStr);
        MAPPERR("sid:%d,callchn:%d,avindex %s\n",SID,g_tMappMgr.atSidInfo[SID].nCallChn,achStr);
    }

    MAPPERR("sid:%d,callchn:%d,thread exit\n",SID,g_tMappMgr.atSidInfo[SID].nCallChn);
    
    
	if(1 != g_tMappMgr.atSidInfo[SID].byTwoWayStream) 
    {
	    avClientStop(avIndex);
        MAPPERR("sid:%d,callchn:%d,avIndex:%d avClientStop.\n",SID,g_tMappMgr.atSidInfo[SID].nCallChn,avIndex);
	}
	pthread_exit(0);
}

void MappPwdChange(const u8 *pbyUserName, const u32 dwUserNameLen, const ENvrUserCfgChangeType eChangeType, void *pCBParam)
{
    s32 nRet = 0;
    char achBuf[16];
    u32 dwLen = 16;
    
    
    do 
    {
        if(NVR_USER_PASS_MDY != eChangeType)
        {
            break;
        }
        mzero(achBuf);
        nRet = CharConvConvertUnicodetoUtf8(pbyUserName, dwUserNameLen, achBuf, dwLen);
        if(nRet != 0)
        {
            break;
        }

        if (strcmp(achBuf, "admin") != 0)
        {
            break;
        }
        char achBuf[NVR_MAX_STR32_LEN];
        mzero(achBuf);
        snprintf(achBuf,NVR_MAX_STR32_LEN,"%u",MAPP_MSG_PWD_CHANGE);
        MappPushQueueOpt(MAPP_MSG_PWD_CHANGE_TYPE,NVR_QUEUE_NODE_MERGER_NONE,strlen(achBuf)+1,achBuf);
    }while(0);
    
}


/******************以上为内部函数，以下为mappstream.h定义函数*************************/
int MappStreamInit()
{
    NVRSTATUS eRet = NVR_ERR__OK;
   
    ///创建处理上下线及保存,先创建以便可以接受消息
    MAPPERR("begin MappStreamInit create queue\n");
    eRet = NvrQueueCreate(&g_ptMappDealQueue);
    if (NVR_ERR__OK != eRet)
    {
        MAPPERR(" NvrQueueCreate g_ptMappDealQueue failed ret:%d\n",eRet);
        return -1;
    }    

    NvrPuiStatusChangeCallBack(MappChnStatusCB,NVR_APP_PROTO_UMSP);
    NvrVtduCtrlDevStreamPrepareCallBack(MappDealChnConnect);
    NvrUserCfgChangeRegisterCB(MappPwdChange);

    
    ///<创建设备上下线处理线程
    if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)MappDevUpDwnThread, "mappUpDwnDeal", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
    {
    	MAPPERR("create MappDevUpDwnThread failed\n");
        return -1;
    }
    
    MAPPERR("MappStreamInit succ\n");

    return 0;
}
int MappStreamReleaseSource(int SID)
{
    int nChnId = 0;
    u8 byEncid = 0;
    
    if(g_tMappMgr.nOnlineNum == 0 || g_tMappMgr.atSidInfo[SID].nSidVaild == 0)
    {
        MAPPERR("no app online:%d or sid:%d invaild:%d\n",g_tMappMgr.nOnlineNum,SID,g_tMappMgr.atSidInfo[SID].nSidVaild);
        return 0;
    }

    ///<停码流
    for(nChnId = 0; nChnId<g_tMappMgr.nChnNum;nChnId++)
    {
        for(byEncid = 0;byEncid<MAPP_MAX_ENC_NUM;byEncid++)
        {
            if(1 == g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[byEncid][SID].bySnd && SID == g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[byEncid][SID].SID)
            {
                MAPPERR("SID:%d,avindex:%d chn:%d  vid encid:%d is snding,begin to stop\n",SID,g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[byEncid][SID].avIndex,nChnId,byEncid);
                MappDelChnSndNode(nChnId,SID,g_tMappMgr.atChnSndMgr[nChnId].aatVidSndInfo[byEncid][SID].avIndex,1);
            }
        }
        if(1 == g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[SID].bySnd)
        {
             MAPPERR("SID:%d,avindex:%d chn:%d aud is snding,begin to stop\n",SID,g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[SID].avIndex,nChnId);
             MappDelChnSndNode(nChnId,SID,g_tMappMgr.atChnSndMgr[nChnId].atAudSndInfo[SID].avIndex,0);            
        }
    }

    ///<停呼叫

    if(g_tMappMgr.atSidInfo[SID].byCall)
    {
        MAPPERR("SID:%d stop aud call\n",SID);
    	MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
        g_tMappMgr.atSidInfo[SID].byCall = 0;
        MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);	
        NvrVtduCtrltAudCallStop(g_tMappMgr.atSidInfo[SID].dwCallId);
    }
    

    
    
    return 0;
    
    
}


int MappGetStreamQuality(int SID, int avIndex, char *buf)
{
    int nRet = 0;
    int avNo = 0;
    SMsgAVIoctrlGetStreamCtrlReq *p = (SMsgAVIoctrlGetStreamCtrlReq *)buf;    
    
    SMsgAVIoctrlSetStreamCtrlReq tResp;
    mzero(tResp);
    tResp.channel = p->channel;
    tResp.quality = 0x03;

    avNo = g_tMappMgr.atSidInfo[SID].atChnToAvInfo[p->channel].avNo;

    MAPPERR("type[0x322] SID:%d,avindex:%d get stream quality channel:%d,chn2avNo:%d\n",SID,avIndex,p->channel,avNo);
    
    
    if(0 != avNo && p->channel == g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avNo-1].nChnId)
    {
        if(1 == g_tMappMgr.atChnSndMgr[p->channel].byVidNum && 0 != g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avNo-1].byEncId)
        {
            tResp.quality = 0x01;
            MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
            g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avNo-1].byEncId = 0;
            MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
        }
        if(0 == g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avNo-1].byEncId)
        {
            tResp.quality = 0x01;
        } 
        MAPPERR("SID:%d avindex:%d,chn:%d encid:%u\n",SID,avNo-1,p->channel,g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avNo-1].byEncId);
    }
               
    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_GETSTREAMCTRL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlSetStreamCtrlReq));
    if(AV_ER_NoERROR == nRet)
    {
        MAPPERR("type[0x322] SID:%d,avindex:%d-%d get chn:%d encnum:%u stream quality(1:HD,3:SD):%d avSendIOCtrl succ\n",\
            SID,avIndex,avNo-1,tResp.channel,g_tMappMgr.atChnSndMgr[p->channel].byVidNum,tResp.quality);
    }
    else
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);
        MAPPERR("type[0x322] SID:%d,avindex:%d-%d get chn:%d stream quality avSendIOCtrl faild,%s\n",SID,avIndex,avNo-1,tResp.channel,achStr);
    }
    return nRet;    
}
int MappSetStreamQuality(int SID, int avIndex, char *buf)
{
    int nRet = 0;
    int i = 0;
    SMsgAVIoctrlSetStreamCtrlReq *p = (SMsgAVIoctrlSetStreamCtrlReq *)buf;
    SMsgAVIoctrlSetStreamCtrlResp tResp;
    mzero(tResp);

    MAPPERR("type[0x320]chid:%d,avindex:%d,chn2avNo:%d set stream quality(1:HD,3:SD):%d\n",p->channel,avIndex,g_tMappMgr.atSidInfo[SID].atChnToAvInfo[p->channel].avNo,p->quality);

    
    i = g_tMappMgr.atSidInfo[SID].atChnToAvInfo[p->channel].avNo-1;
    if(0 == g_tMappMgr.atSidInfo[SID].atChnToAvInfo[p->channel].avNo || p->channel != g_tMappMgr.atSidInfo[SID].atAvIndexinfo[i].nChnId)
    {
        MAPPERR("SID:%d chn:%d avindex:%d,avNo:%d is error\n",SID,p->channel,avIndex,g_tMappMgr.atSidInfo[SID].atChnToAvInfo[p->channel].avNo);
        if(i>=0)
        {
           MAPPERR("av2chn:%d\n",g_tMappMgr.atSidInfo[SID].atAvIndexinfo[i].nChnId); 
        }
        tResp.result = 1;
        i = avIndex;
    }
    else    
    {
        if(3 == p->quality && 1 == g_tMappMgr.atChnSndMgr[p->channel].byVidNum)
        {
            ///<当只有一路码流时，切换辅流，则返回错误码为2，表示不支持
            tResp.result = 2;
        }
        else
        {
            MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
            g_tMappMgr.atSidInfo[SID].atAvIndexinfo[i].byEncId = 1;
            if(1 == p->quality)
            {
                ///<0x01主流
                g_tMappMgr.atSidInfo[SID].atAvIndexinfo[i].byEncId = 0;
            }
            MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
        }
        
        MAPPDBG("SID:%d set avindex:%d-%d,chn:%d,encid:%d,result:%d\n",SID,avIndex,i,g_tMappMgr.atSidInfo[SID].atAvIndexinfo[i].nChnId,g_tMappMgr.atSidInfo[SID].atAvIndexinfo[i].byEncId,tResp.result);
    }

    

    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_SETSTREAMCTRL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlSetStreamCtrlResp));
    if(AV_ER_NoERROR == nRet)
    {
        MAPPERR("type[0x320] set stream quality chn:%d encid:%d result:%d avSendIOCtrl succ\n",g_tMappMgr.atSidInfo[SID].atAvIndexinfo[i].nChnId,g_tMappMgr.atSidInfo[SID].atAvIndexinfo[i].byEncId,tResp.result);
    }
    else
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);
        MAPPERR("type[0x320] set stream quality sid:%d avindex:%d channel:%d,avSendIOCtrl faild,%s\n",SID,avIndex,p->channel,achStr);
    }
    return nRet;
}
int MappStartVideoSnd(int SID, int avIndex, char *buf)
{
    SMsgAVIoctrlAVStream *p = (SMsgAVIoctrlAVStream *)buf;
    u8 byEncid = g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].byEncId;
	MAPPERR("type[0x1FF] chn:%d-%d,encid:%d,SID:%d,avIndex:%d start vidstream\n",p->channel,g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].nChnId,byEncid,SID,avIndex);
    MappAddChnSndNode(p->channel, SID, avIndex, 1, byEncid);
            			
    return 0;
}

int MappStopVideoSnd(int SID, int avIndex, char *buf)
{
    SMsgAVIoctrlAVStream *p = (SMsgAVIoctrlAVStream *)buf;
	MAPPERR("type[0x2FF] SID:%d,channel:%d, avIndex:%d stop vidstream\n\n",SID, p->channel, avIndex);
	MappDelChnSndNode(p->channel, SID, avIndex, 1);
    return 0;
}

int MappStartAudioSnd(int SID, int avIndex, char *buf)
{
    int nRet = 0;
    SMsgAVIoctrlAVStream *p = (SMsgAVIoctrlAVStream *)buf;
    
	MAPPERR("type[0x300] SID:%d ch:%d-%d, avIndex:%d start audstream\n",SID, p->channel,g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].nChnId, avIndex);
	MappAddChnSndNode(g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].nChnId, SID, avIndex, 0, 0);

    return nRet;
}

int MappStopAudioSnd(int SID, int avIndex, char *buf)
{
    int nRet = 0;
    SMsgAVIoctrlAVStream *p = (SMsgAVIoctrlAVStream *)buf;
    
	MAPPERR("type[0x301]SID:%d ch:%d-%d, avIndex:%d stop audstream\n",SID, p->channel,g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].nChnId, avIndex);
			
	MappDelChnSndNode(g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].nChnId, SID, avIndex, 0);

    return nRet;
}
int MappStartAudCall(int SID, int avIndex, char *buf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet = 0;
    u8 byAudEncNum = 0;
    u8 byAudDecNum = 0;
    int *npSid = NULL;
    pthread_t Thread_ID;
    TNvrVtduUsrName tUsrName;
    TNvrVtduAudCallParam tCallParam;
    u8 byStart = FALSE;
    int nChn = g_tMappMgr.atSidInfo[SID].atAvIndexinfo[avIndex].nChnId;
    
    
    SMsgAVIoctrlAVStream *p = (SMsgAVIoctrlAVStream *)buf;
    
	MAPPERR("type[0x350]SID:%d ch:%d-%d, avIndex:%d start aud call\n",SID, p->channel,nChn, avIndex);
   
    eRet = NvrPuiMappGetDevAudCap(nChn,&byAudEncNum,&byAudDecNum); 
    if(NVR_ERR__OK != eRet || 0 == byAudDecNum)
    {
        MAPPERR("type[0x350]ch:%d not sup aud dec:%u\n",nChn, byAudDecNum);
        return -1;
    }
           
            


    do 
    {

        mzero(tUsrName);
        tUsrName.dwNameLen = sizeof(tUsrName.abyUsrName);
        nRet = CharConvConvertUtf8toUnicode("admin", tUsrName.abyUsrName, &tUsrName.dwNameLen);
        if(nRet != 0)
        {
            MAPPERR("usrname to unicode failed:%d\n",nRet);
            break;
        }        

        mzero(tCallParam);
        tCallParam.bInputData = TRUE;
        tCallParam.eCallType = NVR_VTDU_CALL_TYPE_CHN;
        tCallParam.wNvrChnId = nChn;//p->channel;
        tCallParam.eEncType = NVR_AUD_TYPE_PCMA;
        tCallParam.eRate = NVR_AUD_SAMPLE_RATE_8K;
            

        eRet = NvrVtduCtrlSetAudCallParam(&tUsrName,&tCallParam,&g_tMappMgr.atSidInfo[SID].dwCallId);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("NvrVtduCtrlSetAudCallParam failed:%d\n",eRet);
            nRet = -1;
            break;
        }
        byStart = TRUE;        

        MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
        g_tMappMgr.atSidInfo[SID].dwCallMsInId = tCallParam.dwInId;
        g_tMappMgr.atSidInfo[SID].nCallAvIndex = avIndex;
        g_tMappMgr.atSidInfo[SID].byCall = 1;
        g_tMappMgr.atSidInfo[SID].nCallChn = nChn;//p->channel;
        MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);

        eRet = NvrVtduCtrltAudCallStart(g_tMappMgr.atSidInfo[SID].dwCallId);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("NvrVtduCtrltAudCallStart failed:%d\n",eRet);
            nRet = -1;
            break;
        }
        
        
    	npSid = (int *)malloc(sizeof(int));
    	*npSid = SID;
        nRet = pthread_create(&Thread_ID, NULL, &MappAudCallReceiveThread, (void *)npSid);
        if(nRet != 0)
        {
            MAPPERR("MappAudCallReceiveThread create failed:%d\n", nRet);
            break;
        }
    	pthread_detach(Thread_ID);
    }while(0);

    if(nRet != 0 && byStart)
    {
        eRet = NvrVtduCtrltAudCallStop(g_tMappMgr.atSidInfo[SID].dwCallId);
        MAPPERR("NvrVtduCtrltAudCallStop ret:%d\n",eRet);
        MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
        g_tMappMgr.atSidInfo[SID].byCall = 0;
        MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
    }

    return nRet;
}

int MappStopAudCall(int SID, int avIndex, char *buf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    
	MAPPERR("type[0x351]SID:%d avIndex:%d stop aud call\n",SID, avIndex);
	MAPP_SEMTAKE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);
    g_tMappMgr.atSidInfo[SID].byCall = 0;
    MAPP_SEMGIVE(g_tMappMgr.hMgrSidSem, MAPP_SEM_SID);	
    eRet = NvrVtduCtrltAudCallStop(g_tMappMgr.atSidInfo[SID].dwCallId);
    MAPPERR("NvrVtduCtrltAudCallStop ret:%d\n",eRet);
    return 0;
}








