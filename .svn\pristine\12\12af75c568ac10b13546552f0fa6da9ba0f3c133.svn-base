#ifndef __NAS_NETSVC_H
#define __NAS_NETSVC_H
#include "vslls.h"
#include "log.h"
#include "dm.h"
#include "netsvc.h"
#include "raid.h"
#include "lvm.h"

struct nas_private_info {
    char usr_dir[BUFF_SIZE];
    char vg_name[VG_NAME_LEN];
    char lv_name[LV_NAME_LEN];
    char mount_point[BUFF_SIZE];
    char lv_uuid[LVM_UUID_LEN];
    int state; // 0: mkfs success
    int reconfig;
};

int nas_config_lv(struct nas_private_info *nas_info_p);
int nas_info_set(nas_operation *nas_req);
int nas_info_query(nas_operation *nas_response, u32 oper_code);
int nas_create_check(nas_operation *nas_req);
#endif
