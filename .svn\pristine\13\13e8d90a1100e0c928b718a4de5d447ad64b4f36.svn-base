

TOP := ../

COMM_DIR := ./

SRC_DIR := $(TOP)/src


## Name and type of the target for this Makefile

SO_TARGET      := pubsecstack

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER= _HIS3516DV300_
PWLIB_SUPPORT = 0

CFLAGS += -D_LINUX -DLITTLE_ENDIAN

OBJS := $(SRC_DIR)/pubsecstack \
		$(SRC_DIR)/pubseccurl  \
		$(SRC_DIR)/pubsecstack_md5
        
	
## Libraries to include in shared object file
        

## Add driver-specific include directory to the search path
##ARC_LIBS += 
INC_PATH += ../../../10-common/include/system				\
			../../../10-common/include/cbb/httpclient		\
			../../../10-common/include/cbb/cjson			\
			../../../10-common/include/cbb					\
			../../../10-common/include/app					\
			../../../10-common/include/service				\
			../../../10-common/include/cbb/protobuf			\
			../../../10-common/include/cbb/debuglog			\
			../../../10-common/include/cbb/osp				\
			../../../30-cbb/curl/curl-7.56.1/include	    \
			../include		

INSTALL_LIB_PATH = ../../../10-common/lib/release/his3516dv300
				
LIB_PATH := ../../../10-common/lib/release/his3516dv300

	
LIBS :=	osp httpclient ghttp  cjson appbase curl

include $(COMM_DIR)/../../makelib.mk

