path="../../10-common/version/compileinfo/nvrlib_cv2x.txt"
date>>$path

cd ipc_ptz/prj_linux

echo ==============================================
echo =      algapp_linux for cv2x           =
echo ==============================================

echo "============compile libalgapp cv2x============">>../../$path

make -e DEBUG=0 -f makefile_cv2x clean
make -e DEBUG=0 -f makefile_cv2x 2>>../../$path

cp -L -r -f libalgapp.so ../../../../10-common/lib/release/cv2x/algptz/videoalg_base

cd ..
