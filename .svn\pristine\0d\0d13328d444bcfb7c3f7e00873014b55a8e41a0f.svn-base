path="../../10-common/version/compileinfo/his3536c_stk.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      his3536c_stk for his3536c           =
echo ==============================================

echo "============compile libairp his3536c============">>../$path

make -e DEBUG=0 -f makefile_his3536c_stk clean
make -e DEBUG=0 -f makefile_his3536c_stk 2>>../$path

cp -L -r -f libairp.so ../../../10-common/lib/release/his3536c/serviceextlib2/mnvr/

cd ..
