

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3516AV200_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

$(waring $(PLUGIN));

ifeq ($(PLUGIN), nvrdingsung)
OBJS := $(SRC_DIR)/nvrdingsung
SO_TARGET := nvrdingsungdynplugin
endif
ifeq ($(PLUGIN), nvrfilllight)
OBJS := $(SRC_DIR)/nvrfilllight
SO_TARGET := nvrfilllightdynplugin
endif
ifeq ($(PLUGIN), nvr2dradar)
OBJS := $(SRC_DIR)/nvr2dradar
SO_TARGET := nvrradardynplugin
endif   
ifeq ($(PLUGIN), nvrfqfilllight)
OBJS := $(SRC_DIR)/nvrfqfilllight
SO_TARGET := nvrfqfilllightdynplugin
endif 
ifeq ($(PLUGIN), nvrcount)
OBJS := $(SRC_DIR)/nvrcount
SO_TARGET := nvrcountdynplugin
endif 
ifeq ($(PLUGIN), nvrfastfish)
OBJS := $(SRC_DIR)/nvrfastfish
SO_TARGET := nvrfastfishdynplugin
endif 
ifeq ($(PLUGIN), nvrkedacom)
OBJS := $(SRC_DIR)/nvrkedacom
SO_TARGET := nvrkedacomdynplugin
endif 
ifeq ($(PLUGIN), nvrlift)
OBJS := $(SRC_DIR)/nvrlift
SO_TARGET := nvrliftdynplugin
endif 
ifeq ($(PLUGIN), nvrrchttrb)
OBJS := $(SRC_DIR)/nvrrchttrb
SO_TARGET := nvrrchttrbdynplugin
endif 
ifeq ($(PLUGIN), nvrjwst)
OBJS := $(SRC_DIR)/nvrjwst
SO_TARGET := nvrjwstdynplugin
endif 

## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../40-service/nvrcfg/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/ipcmediactrl\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/cbb/mxml\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64 \
		$(CURDIR)/../../../10-common/include/cbb/ftpc\
		$(CURDIR)/../../../10-common/include/cbb/ispctrl\
		$(CURDIR)/../../../40-service/nvrsys/include \
		$(CURDIR)/../../../40-service/nvrpui/include\
		$(CURDIR)/../../../40-service/nvrcap/include\
		$(CURDIR)/../../../40-service/lcamclt/include\
		$(CURDIR)/../../nvrfixcore/include
		

CFLAGS += -D_HIS3516AV200_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/his3516av200/fixipc

LDFLAGS += -L../../../10-common/lib/release/his3516av200/
LDFLAGS += -L../../../10-common/lib/release/his3516av200/appcltlib/
LDFLAGS += -L../../../10-common/lib/release/his3516av200/fixipc/
LDFLAGS += -L../../../10-common/lib/release/his3516av200/ipcmediactrl/

#LDFLAGS += -losp
#LDFLAGS += -llcamclt
#LDFLAGS += -lnvrfixcorepro
#LDFLAGS += -lispctrl

include $(COMM_DIR)/common.mk

