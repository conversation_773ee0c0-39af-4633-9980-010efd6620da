

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3536_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
#CFLAGS += -D__MRTC__
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/his3536
LIB_PATH += $(TOP)/../../10-common/lib/release/his3536/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/his3536/capnvr

LIBS +=	nvrcfg nvrlog nvrcap nvrcustcap nvrusrmgr nvrnetwork nvrsys nvrproto protobufc sqlite3wrapper malloc debuglog netcbb ddnsc ddnscext upnpc drv pthread nvrgeo nvrcoi \
	sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu nvrpcap nvrftp httpclient mxml ghttp go rtspclient kdmposa mca osp netpacket kdmtsps kdvencrypt mediaswitch stdc++ nvrqueue mediactrl_nvr nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp airp ais algctrl nvrextdev dl SDEF pubsecstack curl nvrunifiedlog\
	equalizer_ex_armhi3516a_linux \
	voicechanger_armhi3516a_linux \
    mixer_armhi3516a_linux \
    spe_armhi3516a_linux \
    videomanage_armhi3516a_linux \
    audproc_plus_armhi3516a_linux \
    speechexcit_armhi3516a_linux \
    resamplev2_armhi3516a_linux \
    extexp_armhi3516a_linux\
    multiaec_v202_armhi3536_linux \
    audcodec_armhi3516a_linux \
    dlydct_armhi3516a_linux \
    g7221c_armhi3516a_linux \
    adpcm_armhi3516a_linux \
    g711_armhi3516a_linux \
    g722_armhi3516a_linux\
	stdg722_armhi3516a_linux\
    aaclcenc_armhi3516a_linux \
    aaclcdec_armhi3516a_linux \
    g726_armhi3516a_linux \
    aaclddec_armhi3516a_linux \
    aacldenc_armhi3516a_linux \
    amr_nb_armhi3516a_linux \
    g719_armhi3516a_linux \
    g728_armhi3516a_linux \
    g729_armhi3516a_linux \
    mp3dec_armhi3516a_linux \
    mp3enc_armhi3516a_linux \
    opus_armhi3516a_linux \
    mp2_armhi3516a_linux \
	hive_RES m \
	videounit_armhisi3536_linux imagelib_armhisi3536_linux \
	svacdec_armhisi3536_linux videomanage_armhisi3536_linux \
	vidcomlib_armhisi3536_linux \
	agc_speechsense_armhi3516a_linux\
	pcap\
	kdmssl\
	kdmcrypto\
	cjson\
	udm\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrstitch\
	lwshelper\
	websockets\
	nvrmd5\
    #heapcheck\
    #tcmalloc
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../ais/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/app
		
CFLAGS += -D_HIS3536_
#CFLAGS += -D_TCMALLOC_
#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_APP_PATH := ../../../10-common/version/release/his3536/bin_nvr/his3536_its300_gk
include $(COMM_DIR)/common.mk


