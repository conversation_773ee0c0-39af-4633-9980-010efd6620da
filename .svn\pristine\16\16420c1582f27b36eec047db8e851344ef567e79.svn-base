#!/bin/bash
#set -x
path="../../../10-common/version/compileinfo/nvrlib_his3516av200.txt"
date>>$path

module_name=$(basename $PWD)
proj_dir=prj_linux

echo ==============================================
echo =      ${module_name}_linux for his3516av200           =
echo ==============================================

echo "============compile lib$module_name his3516av200============">>$path

make -C $proj_dir -e DEBUG=1 -f makefile_his3516av200 clean all 2>&1 1>/dev/null |tee -a $path


