#ifndef __LOG_H__
#define __LOG_H__

#include <pthread.h>

/*
	log return value:
		0 is ok,
		1 is not init,
		2 is already init,
		3 is invalid parameter
		4 not enough buffer
		255 is unknown error.
*/
#define LOG_RET_OK				0
#define LOG_RET_NOT_INIT		1
#define LOG_RET_ALREADY_INIT	2
#define LOG_RET_INVALID_PARAM	3
#define LOG_RET_NOT_ENOUGH		4
#define LOG_RET_UNKOWN			255

typedef struct
{
    pthread_cond_t      c;
    pthread_mutex_t     m;
    int              nCnt;
} log_sem;

typedef enum 
{
	LOG_ALL_LEVEL = 0,
	LOG_DEBUG_LEVEL,
	LOG_INFO_LEVEL,
	LOG_WARN_LEVEL,
	LOG_ERROR_LEVEL,
	LOG_FATAL_LEVEL
}ELOGLEVEL;

typedef void (*OSP_PRINTF_CALLBACK)(int bScreen, int bFile, const char *szFormat, ...);

/*
	szLogPath is the path of the log files.
	szLogName is the basic name of the log files.
	nLogFileCount is the max count of the log files.
	nLogFileSize is the max size(MB) of each log file.
	Initialize the module of log, the return value is defined on the head of this file.
	This interface is called by the application at it start. It's not necessary to call 
	this interface that you just want to use it in a library.
*/
int log_init(const char *szLogPath, const char *szLogName, int nLogFileCount, int nLogFileSize);

/*
	Uninitialize the module of log, the return value is defined on the head of this file.
	The same as log_init.
*/
int log_uninit();

/*
	1 is init and 0 is not.
*/
int log_isinit();

/*
	The logs which is greater or equal than emLevel will be written to file.
*/
int log_set_level(ELOGLEVEL emLevel);

/*
	Set callback of ospprintf, pCallback can be NULL
*/
int log_set_osp_callback(OSP_PRINTF_CALLBACK pCallback);

/*
	The logs will be writen to file except the log of debug level.
	the return value is defined on the head of this file.
*/
int write_log(ELOGLEVEL emLogLevel, const char * szFile, int szLine, const char * szFormat, ...);

#define CLOG_DEBUG(fmt, args...)	write_log(LOG_DEBUG_LEVEL, __FILE__, __LINE__, fmt, ##args)
#define CLOG_INFO(fmt, args...)	write_log(LOG_INFO_LEVEL, __FILE__, __LINE__, fmt, ##args)
#define CLOG_WARN(fmt, args...)	write_log(LOG_WARN_LEVEL, __FILE__, __LINE__, fmt, ##args)
#define CLOG_ERROR(fmt, args...)	write_log(LOG_ERROR_LEVEL, __FILE__, __LINE__, fmt, ##args)
#define CLOG_FATAL(fmt, args...)	write_log(LOG_FATAL_LEVEL, __FILE__, __LINE__, fmt, ##args)


#endif

