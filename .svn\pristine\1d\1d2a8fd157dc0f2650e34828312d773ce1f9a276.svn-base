1、openssl需要使用3.1.4版本，不可使用公司系统部发布版本。
2、代码需要使用utf-8编码，特别是通知模块mappnotice，否则部分语言通知内容会是乱码
3、编译，需要最后进行打包编译，以防影响其他版本，因为，编译需要将10-common\lib\release\ssc339g与10-common\lib\release\ssc339g\tutk相同的删除，链接tutk下的库,主要有ssl，crypto，curl，go，websocket，smtp，做pgk包，也需要删除这些相同库。
4、start.sh脚本，需要做一些看软连接
5、能力集放开支持云服务能力
6、产品能力，需要支持，读取默认配置修改最大app连接数。
7、对于IPC，由于app需要支持放像，ipc放像能力需要考虑放开几个，
8、为了mapp快速从设备获取一些配置，增加几个额外接口，nvrsys语言回调通知，nvralarm快速获取云服务上报配置，pui获取音频能力