path="../../10-common/version/compileinfo/nvrlib_mlu220.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_cfg_linux for mlu220        =
echo ==============================================

echo "============compile libnvrcfg mlu220============">>../$path

make -e DEBUG=0 -f makefile_mlu220 clean
make -e DEBUG=0 -f makefile_mlu220 2>>../$path

cp -L -r -f libnvrcfg.so ../../../10-common/lib/release/mlu220/

cd ..
