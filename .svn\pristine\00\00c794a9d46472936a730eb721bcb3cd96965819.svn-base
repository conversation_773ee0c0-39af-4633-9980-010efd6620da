#include "mapp.h"
#include "mapperror.h"
#include "mapprec.h"
#include "nvrrec.h"
#include "nvrrec_in.h"



typedef struct tagMappRecChnInfo
{
    u32 dwContext;         ///<SID+ChnID,各16位
    u8 byPlying;           ///<是否在放像
    u8 byPause;            ///<是否暂停
    u8 byDelayCount;       ///<延迟等待放像av创建成功次数，每次100ms
    u8 byFisrtFrmSnd;      ///<是否第一帧发送,默认0
    int nPlyChn;           ///<IOTC_Session_Get_Free_Channel获取空闲放像通道
    int avIndex;           ///<放像重新从新创建的axindex
    //SEMHANDLE hRecChnSem;  ///<放像通道信号量 
}TMappRecChnInfo;


typedef struct tagMappRecPlyInfo
{
    u8 byPlying;           ///<是否在放像
    u8 byPlyChnNum;        ///<放像通道数
    u8 abyRes[2];
    u32 dwTaskID;    
    
    TMappRecChnInfo atRecChnInfo[MAPP_MAX_CHN_NUM]; 
    
    
}TMappRecPlyInfo;


typedef struct tagMappRecManager
{
    u8 byAppNum;   
    u8 abyRes[3]; 
    SEMHANDLE hRecSem;          ///<Rec管理信号量 
    SEMHANDLE ahRecPlySem[MAPP_MAX_APP_NUM];  ///<开始信号量 ，放像做同步
    TMappRecPlyInfo atRecPlyInfo[MAPP_MAX_APP_NUM];   ///<下标为SID
}TMappRecMgr;

static TMappRecMgr g_tMappRecMgr;
extern TMappManager g_tMappMgr;



void MappRecTestStatus(BOOL32 bPrintAll)
{
    int i = 0;
    MAPPERR("===================recplay status onlinenum:%u===================\n",g_tMappRecMgr.byAppNum);
    for(i = 0;i<MAPP_MAX_APP_NUM;i++)
    {
        MAPPERR("SID:%d,plying:%u,playchnnum:%u,taskID:"FORMAT_U32"\n",i,g_tMappRecMgr.atRecPlyInfo[i].byPlying,g_tMappRecMgr.atRecPlyInfo[i].byPlyChnNum,g_tMappRecMgr.atRecPlyInfo[i].dwTaskID);
        if(g_tMappRecMgr.atRecPlyInfo[i].byPlying || bPrintAll)
        {
            int j = 0;  
            for(j = 0;j<g_tMappMgr.nChnNum;j++)
            {
                if(g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[j].byPlying || bPrintAll)
                {
                    MAPPERR("chn:%d,plying:%u,plyavindex:%d,plychn:%d,pause:%u\n",j,g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[j].byPlying,g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[j].avIndex,g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[j].nPlyChn,g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[j].byPause);
                }
            }
            MAPPERR("\n");            
        }
    }
    
    
}
static void MappRecEventSwitch(ENvrRecEventType eRecEventType,ENUM_MAPPEVENTTYPE *eSdkEventType)
{
    if((eRecEventType>=NVR_REC_EVENT_TYPE_PIN_ALARM && eRecEventType<=NVR_REC_EVENT_TYPE_DETECT_FACE) ||
        (eRecEventType>=NVR_REC_EVENT_TYPE_HOT_POINT_ALARM && eRecEventType<=NVR_REC_EVENT_HUMAN_CHECK))
    {
        *eSdkEventType = eRecEventType;        
    }
    else
    {
        //处理自定义事件 todo
        *eSdkEventType = NVR_REC_EVENT_TYPE_MANUAL;
    }
    return;
}
static void MappRecPutVidFrameDateCB(void* pData, void* pvContext)
{    
	s32 nRet = 0; 
    TMSFrame *ptFrameDate = NULL;   ///<帧数据;    
    int nChn = -1;
    int SID = 0;	
	
    
    if(NULL == pData)
    {
        return;
    }
    if(NULL != pvContext)
    {
        int n = 0;
        memcpy(&n, pvContext, sizeof(s32));

        SID = n>>16;
        nChn = n & 0xFFFF;
        MAPPFRQ("SID:%d,ChnID:%d\n",SID,nChn); 
    }
    else
    {
        ptFrameDate = pData;
        if(NULL !=ptFrameDate)
        {
            ptFrameDate->MSFreeFrame(ptFrameDate);
        }
        MAPPIMP("pvContext is null\n");        
        return;
    }
    
    
    ptFrameDate = pData;
    
   

    FRAMEINFO_t frameInfo;    
    memset(&frameInfo, 0, sizeof(FRAMEINFO_t));
    frameInfo.codec_id = MappConverPlayload(ptFrameDate->m_tFrame.m_byMediaType);
    frameInfo.flags = 0x00;
    if(ptFrameDate->m_tFrame.x.m_tVideoParam.m_bKeyFrame)

    {
        frameInfo.flags = 0x01;
    }
    else
    {
        float f = 0;
        f = avResendBufUsageRate(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex);
        if(f>0.6)
        {
            MAPPFRQ("SID:%d chn:%d,av:%d,rate:%.2f\n",SID,nChn,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex,f);
            if(NULL !=ptFrameDate)
            {
                ptFrameDate->MSFreeFrame(ptFrameDate);
            }
            return;            
        }
        
    }

    /*while(1)
    {
        if(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex == -1 )
        {            
            if(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].byDelayCount>=20)
            { 
                //延时2s
                break;
            }
            g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].byDelayCount++;            
            OsApi_TaskDelay(100);
        }
        else
        {            
            break;
        }
    }*/
    
    if(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex != -1 )
    {    
        nRet = avSendFrameData(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex, ptFrameDate->m_tFrame.m_pData, ptFrameDate->m_tFrame.m_dwDataSize, &frameInfo, sizeof(FRAMEINFO_t));
        if(0 == g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].byFisrtFrmSnd)
        {
            g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].byFisrtFrmSnd = 1;
            MAPPIMP("SID:%d,chnId:%d,avindex %d vid snd,frametime:"FORMAT_U32",I:%d,,playload:%d,W-H:%d-%d,ret:%d\n",
                SID,nChn,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex,ptFrameDate->m_tFrame.m_dwTimeStamp,frameInfo.flags,frameInfo.codec_id,ptFrameDate->m_tFrame.x.m_tVideoParam.m_wVideoWidth,ptFrameDate->m_tFrame.x.m_tVideoParam.m_wVideoHeight,nRet);
        }
        if(AV_ER_NoERROR !=nRet)
        {
            char achStr[NVR_MAX_STR256_LEN];
            mzero(achStr);
            MappAvErrToStr(nRet,achStr);
            MAPPFRQ("SID:%d,chnId:%d,avindex %d vid snd,ret:%s\n",SID,nChn,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex,achStr);
            
        }
        else
        {
            MAPPFRQ("SID:%d,chnId:%d,avindex %d vid snd,frametime:"FORMAT_U32",I:%d,,playload:%d,W-H:%d-%d,ret:%d\n",
                SID,nChn,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex,ptFrameDate->m_tFrame.m_dwTimeStamp,frameInfo.flags,frameInfo.codec_id,ptFrameDate->m_tFrame.x.m_tVideoParam.m_wVideoWidth,ptFrameDate->m_tFrame.x.m_tVideoParam.m_wVideoHeight,nRet);
        }  
    }
    
    if(NULL !=ptFrameDate)
    {
        ptFrameDate->MSFreeFrame(ptFrameDate);
    }
    
    return;
}
static void MappRecPutAudFrameDateCB(void* pData, void* pvContext)
{
    s32 nRet = 0; 
    TMSFrame *ptFrameDate = NULL;   ///<帧数据;    
    int nChn = -1;
    int SID = 0;
    ENUM_AUDIO_SAMPLERATE eSample = AUDIO_SAMPLE_8K;
    ENUM_AUDIO_DATABITS eDataBits = AUDIO_DATABITS_16;  ///<ipc都是16位
    ENUM_AUDIO_CHANNEL eChannel = AUDIO_CHANNEL_MONO;   ///<ipc都是单声道，aaclc只有在svr产品可配
	
    
    if(NULL == pData)
    {
        return;
    }
    if(NULL != pvContext)
    {
        int n = 0;
        memcpy(&n, pvContext, sizeof(s32));

        SID = n>>16;
        nChn = n & 0xFFFF;
        MAPPFRQ("SID:%d,ChnID:%d\n",SID,nChn); 
    }
    else
    {
        ptFrameDate = pData;
        if(NULL !=ptFrameDate)
        {
            ptFrameDate->MSFreeFrame(ptFrameDate);
        }
        MAPPIMP("pvContext is null\n");        
        return;
    }
    
    
    ptFrameDate = pData;    

    FRAMEINFO_t frameInfo;
    memset(&frameInfo, 0, sizeof(FRAMEINFO_t));
    
    frameInfo.codec_id = MappAudConverPlayload(ptFrameDate->m_tFrame.m_byMediaType); 
    switch (ptFrameDate->m_tFrame.x.m_tAudioParam.m_dwSample)
    {
        case 8000:
            eSample = AUDIO_SAMPLE_8K;
            break;
        case 16000:
            eSample = AUDIO_SAMPLE_16K;
            break;
        case 32000:
            eSample = AUDIO_SAMPLE_32K;
            break;
        case 44000:
            eSample = AUDIO_SAMPLE_44K;
            break;
        case 48000:
            eSample = AUDIO_SAMPLE_48K;
            break;
        default:
            break;
    }

    ///<k ipc都是固定16和单
    #if 0  
    if(16 == ptFrameDate->m_tFrame.x.m_tAudioParam.m_wBitsPerSample)
    {
        eDataBits = AUDIO_DATABITS_16;
    }
    else
    {
        eDataBits = AUDIO_DATABITS_8;
    }
    
    if (1 == ptFrameDate->m_tFrame.x.m_tAudioParam.m_wChannel)
    {
        eChannel = AUDIO_CHANNEL_MONO;        
    }
    else
    {
        eChannel = AUDIO_CHANNEL_STERO;
        
    }
    #endif

    frameInfo.dataBit = eDataBits;
    if(MEDIA_TYPE_G726_32 == ptFrameDate->m_tFrame.m_byMediaType)
    {
        frameInfo.dataBit = AUDIO_DATABITS_32;
        eDataBits = AUDIO_DATABITS_16;
        MAPPFRQ("aud databit:%u-%d,mediatye:%u\n",ptFrameDate->m_tFrame.x.m_tAudioParam.m_wBitsPerSample,eDataBits,ptFrameDate->m_tFrame.m_byMediaType);
    }
   
    frameInfo.flags = (eSample << 2) | (eDataBits << 1) | eChannel;

    if(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex != -1 )
    {    
        nRet = avSendAudioData(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex, ptFrameDate->m_tFrame.m_pData, ptFrameDate->m_tFrame.m_dwDataSize, &frameInfo, sizeof(FRAMEINFO_t));
        if(AV_ER_NoERROR !=nRet)
        {
            char achStr[NVR_MAX_STR256_LEN];
            mzero(achStr);
            MappAvErrToStr(nRet,achStr);
            MAPPFRQ("SID:%d,chnId:%d,avindex %d aud snd,re:t%s\n",SID,nChn,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex,achStr);
            
        }
        else
        {
            MAPPFRQ("SID:%d,chnId:%d,avindex %d aud snd,ret:%d\n",SID,nChn,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChn].avIndex,nRet);
        }  
    }
    
    if(NULL !=ptFrameDate)
    {
        ptFrameDate->MSFreeFrame(ptFrameDate);
    }
   
    return;
}


static void MappRecPlyProgCB(u32 dwTaskId, u16 wChnId,u64 qwSystime, u64 qwRtpTime, ENvrRecPlayCBState eStat)
{ 
    int nRet = 0;
    int i = 0;
    int j = 0;
    
    static u16 m_wSidPlyChn[MAPP_MAX_APP_NUM]={0};
    
    
    MAPPFRQ("ply taskid:%d state:%d,chnid:%d,systime:%ld,rtptime:%ld\n",dwTaskId,eStat,wChnId,qwSystime,qwRtpTime);
    
    
    if(eStat==0 || eStat==1 )
    {       
        SMsgAVIoctrlPlayRecordResp tResp; 
        
        mzero(tResp);
        tResp.command = AVIOCTRL_RECORD_PLAY_END;

        for(i=0; i<g_tMappMgr.bySupAppNum;i++)
        {
            MAPPFRQ("No,%d,chn:%d,taskid:%d,av:%d\n",i,m_wSidPlyChn[i],g_tMappRecMgr.atRecPlyInfo[i].dwTaskID,g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[m_wSidPlyChn[i]].avIndex);
            if(-1 == g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[m_wSidPlyChn[i]].avIndex)
            {
                for(j = 0;j<g_tMappMgr.nChnNum;j++)
                {
                    if (-1 != g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[j].avIndex)
                    {
                        m_wSidPlyChn[i] = j;
                        MAPPFRQ("No.%d,find chn:%d,taskid:%d,av:%d\n",i,m_wSidPlyChn[i],g_tMappRecMgr.atRecPlyInfo[i].dwTaskID,g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[m_wSidPlyChn[i]].avIndex);
                        break;
                    }
                }
            }
            if(g_tMappRecMgr.atRecPlyInfo[i].dwTaskID == dwTaskId && -1 != g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[m_wSidPlyChn[i]].avIndex)
            {
                if(eStat==0)
                {
                    tResp.command = AVIOCTRL_RECORD_PLAY_END;
                }
                else 
                {
                    tResp.command = AVIOCTRL_RECORD_PLAY_START;
                    tResp.size = qwSystime/1000;
                }
                
                nRet = avSendIOCtrl(g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[m_wSidPlyChn[i]].avIndex, IOTYPE_USER_IPCAM_RECORD_PLAYCONTROL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlPlayRecordResp));
                MAPPFRQ("[0x31B]chn:%d av:%d cmd:0x%x,time:%d,state:%d,ret:%d\n",m_wSidPlyChn[i],g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[m_wSidPlyChn[i]].avIndex,tResp.command,tResp.size,eStat,nRet);
                break;
            }
        }
        
    }
}
static int MappRecStopPlyResource(int SID, int nChnId)
{
    int nRet = 0;
    NVRSTATUS eRet = NVR_ERR__OK;
   

    MAPPERR("SID:%d,chn:%d plying:%d,rec stop ply\n",SID,nChnId,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].byPlying); 
    if(0 == g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].byPlying)
    {
        MAPPERR("SID:%d,chn:%d no plying\n",SID,nChnId); 
        return nRet;
    }

    eRet = NvrRecClearPlayTaskChnAudOutCB(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,nChnId,0);
    MAPPERR("sid:%d,chn:%d,taskid:"FORMAT_U32",NvrRecClearPlayTaskChnAudOutCB ret:%d\n",SID,nChnId,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,eRet);

    eRet = NvrRecSetPlayTaskChn(FALSE,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,nChnId,NULL);
    MAPPERR("sid:%d,chn:%d,taskid:"FORMAT_U32",NvrRecSetPlayTaskChn ret:%d\n",SID,nChnId,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,eRet);
    eRet = NvrRecStopPlay(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,nChnId);
    MAPPERR("sid:%d,chn:%d,NvrRecStopPlay taskId:"FORMAT_U32" ret:%d\n",SID,nChnId,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,eRet);
    

    

   

    MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
    if(g_tMappRecMgr.atRecPlyInfo[SID].byPlyChnNum>0)
    {
        g_tMappRecMgr.atRecPlyInfo[SID].byPlyChnNum--;
    }
    g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].byPlying = 0;
    g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].byDelayCount = 0;
    
    g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].byFisrtFrmSnd = 0;
    
    avServStop(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex);
    MAPPERR("sid:%d chn:%d avServStop avindex:%d,taskid:"FORMAT_U32",plychnnum:%u\n",SID,nChnId,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,g_tMappRecMgr.atRecPlyInfo[SID].byPlyChnNum);
    g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex = -1;
    if(0 == g_tMappRecMgr.atRecPlyInfo[SID].byPlyChnNum)
    {
        g_tMappRecMgr.atRecPlyInfo[SID].byPlying = 0;
        if(g_tMappRecMgr.byAppNum>0)
        {
            g_tMappRecMgr.byAppNum--;
        }
        
        eRet = NvrRecDestroyPlayTask(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID);       
        
        MAPPERR("sid:%d no app[%d] plying,NvrRecDestroyPlayTask taskId:"FORMAT_U32",ret:%d\n",SID,g_tMappRecMgr.byAppNum,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,eRet);

        g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID = 0;
    }
    MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
    
    
    return nRet;
}

static NVRSTATUS MappRecCreatePlyTask(int SID, int nChnId)
{
    NVRSTATUS eRet = NVR_ERR__OK;

    u32 dwTaskId = g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID;

    
    MAPPERR("sid:%d,chnid:%d,plying:%d,taskid:"FORMAT_U32"\n",SID,nChnId,g_tMappRecMgr.atRecPlyInfo[SID].byPlying,dwTaskId);
    if(0==g_tMappRecMgr.atRecPlyInfo[SID].byPlying)
    {
        eRet = NvrRecCreatePlayTask(&dwTaskId,TRUE,MappRecPlyProgCB,0);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("SID:%d chn:%d create ply task faild:%d\n",SID,nChnId,eRet);
            return eRet; 
        }
        MAPPERR("SID:%d chn:%d ply taskID:"FORMAT_U32"\n",SID,nChnId,dwTaskId);
        MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        g_tMappRecMgr.byAppNum++;
        g_tMappRecMgr.atRecPlyInfo[SID].byPlying = 1;
        g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID = dwTaskId;       
        MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
    }
   
    
    MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
    g_tMappRecMgr.atRecPlyInfo[SID].byPlyChnNum++;
    g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].byPlying = 1;
    g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].byPause = 0;
    g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].dwContext = (SID<<16)|nChnId;
    MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);

    
    MAPPERR("sid:%d,chnid:%d,plying:%d,taskid:"FORMAT_U32",appnum:%d,plychnnum:%d\n",
        SID,nChnId,g_tMappRecMgr.atRecPlyInfo[SID].byPlying,dwTaskId,g_tMappRecMgr.byAppNum,g_tMappRecMgr.atRecPlyInfo[SID].byPlyChnNum);
    return eRet;
}

static void *MappRecPlayThead(void *arg)
{
    TMappSidChIdInfo tSidChIdInfo = *(TMappSidChIdInfo *)arg;
	int SID = tSidChIdInfo.SID;
    int nChnId = tSidChIdInfo.nChId;
	
	free(arg);
    

    AVServStartInConfig avStartInCfg;
    AVServStartOutConfig avStartOutConfig;
    mzero(avStartInCfg);
    mzero(avStartOutConfig);
    
    avStartInCfg.cb               = sizeof(AVServStartInConfig);
    avStartInCfg.iotc_session_id  = SID;
    avStartInCfg.iotc_channel_id  = g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].nPlyChn;  ///<录像avindex不同浏览通道 = 设备通道数+通道号
    avStartInCfg.timeout_sec      = 30;
    avStartInCfg.password_auth    = &MappExPwdAuthCB;
    avStartInCfg.server_type      = SERVTYPE_STREAM_SERVER;
    avStartInCfg.resend           = ENABLE_RESEND;
    avStartInCfg.disable_fec      = 1;
#if ENABLE_DTLS
    avStartInCfg.security_mode = AV_SECURITY_DTLS; // Enable DTLS, otherwise use AV_SECURITY_SIMPLE
#else
    avStartInCfg.security_mode = AV_SECURITY_SIMPLE;
#endif

    avStartOutConfig.cb              = sizeof(AVServStartOutConfig);

    MAPPERR("begin to create ply avindex,SID:%d,plychn:%d-%d\n",SID,nChnId,avStartInCfg.iotc_channel_id);

    int avIndex = avServStartEx(&avStartInCfg, &avStartOutConfig);
    if(AV_ER_IOTC_CHANNEL_IN_USED == avIndex)
    {
        MAPPERR("avServStartEx failed SID:%d,chn:%d-%d,ret:%d,stop av:%d\n", SID,nChnId,avStartInCfg.iotc_channel_id, avIndex,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex);
        avServStop(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex);
        OsApi_Delay(200);
        MAPPERR("begin again to create ply avindex,SID:%d,plychn:%d-%d\n",SID,nChnId,avStartInCfg.iotc_channel_id);
        avIndex = avServStartEx(&avStartInCfg, &avStartOutConfig);        
    }

    if(avIndex < 0)
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(avIndex,achStr); 
        MAPPERR("avServStartEx failed SID:%d,chn:%d-%d,%s\n", SID,nChnId,avStartInCfg.iotc_channel_id, achStr);
        MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC); 
        if(AV_ER_IOTC_CHANNEL_IN_USED == avIndex)
        {
            avServStop(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex);
            MAPPERR("avServStartEx failed again:%d,SID:%d,plychn:%d-%d,stop av:%d\n",avIndex,SID,nChnId,avStartInCfg.iotc_channel_id,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex);
        }
        g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex = -1;
        MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        MappRecStopPlyResource(SID,nChnId);
        pthread_exit(0);
        return;
    }
    MAPPERR("avServStartEx succ SID:%d,chn:%d-%d,avidex:%d\n", SID,nChnId,avStartInCfg.iotc_channel_id, avIndex);
    MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);  
    if(0 == g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].byPlying)
    {
        avServStop(avIndex);
        g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex = -1;
    }
    else
    {
        g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].avIndex = avIndex;
    }
    MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);

    avServSetResendSize(avIndex, 1024);
    
    

	/*while(1)
	{
        MAPP_SEMTAKE(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[nChnId].hRecChnSem, MAPP_SEM_RECCHN);
        MAPPERR("SID:%d,chn:%d,avidex:%d ply thread exit\n", SID,nChnId, avIndex);
        break;
	}
    avServStop(avIndex);
	MAPPERR("SID:%d,chn:%d,avidex:%d avServStop\n", SID,nChnId, avIndex);*/
	pthread_exit(0);
}
static int MappRecPlayStart_t(int SID, int avIndex, char *buf)
{
    int nRet;    
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrRecPlayCond tPlyCond;
    pthread_t ThreadID;

    SMsgAVIoctrlPlayRecord *p = (SMsgAVIoctrlPlayRecord *)buf;
    SMsgAVIoctrlPlayRecordResp tResp;
    mzero(tResp);
    tResp.command = p->command;

    MAPPERR("type[0x31A]rec play start[0x%x] SID:%d,avindex:%d,chn:%d\n",tResp.command,SID,avIndex,p->channel);
    

    do 
    {
        if(1 == g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPlying )
        {
            tResp.result = g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].nPlyChn;
            
            MAPPERR("SID:%d,chn:%d is plying\n",SID,p->channel);
            if(1 == g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause)
            {
                TNvrRecPlayVCRCtrlCmd tPlayVCRCmd;
                mzero(tPlayVCRCmd);
                tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_RESUME;
                MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
                g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause = 0;
                MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
                NvrRecPlayVCRCtrl(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID, p->channel, &tPlayVCRCmd);
            }
            break;            
        }
        

        TMappSidChIdInfo *ptSidChIdInfo = (TMappSidChIdInfo *)malloc(sizeof(TMappSidChIdInfo));
        ptSidChIdInfo->nChId = p->channel;
        ptSidChIdInfo->SID = SID;  
        
        nRet = IOTC_Session_Get_Free_Channel(SID); //<放像需要单独创建av
        if(nRet<0)
        {
            char achStr[NVR_MAX_STR256_LEN];
            mzero(achStr);
            MappIotcErrToStr(nRet,achStr);        
            MAPPERR("sid:%d,chn:%d IOTC_Session_Get_Free_Channel %s\n",SID,p->channel,achStr);
            tResp.result = -1;
            break;
        }
        MAPPERR("sid:%d,plychn:%d \n",SID,nRet);
        tResp.result = nRet;        
        g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].nPlyChn = tResp.result;
       
        if((nRet = pthread_create(&ThreadID, NULL, &MappRecPlayThead, (void *)ptSidChIdInfo)))
        {
            if(NULL != ptSidChIdInfo)
            {
                free(ptSidChIdInfo);
                ptSidChIdInfo = NULL;
            }
            MAPPERR("MappRecPlayThead create failed:%d\n", nRet);
            break;
        }
        pthread_detach(ThreadID);

        
        
        eRet = MappRecCreatePlyTask(SID,p->channel);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("SID:%d create ply task failed\n",SID);
            break;
        }

        struct tm tStartTime;
        mzero(tStartTime);
        tStartTime.tm_year = p->stTimeDay.year-1900;
        tStartTime.tm_mon = p->stTimeDay.month-1;
        tStartTime.tm_mday = p->stTimeDay.day;
        tStartTime.tm_hour = p->stTimeDay.hour;
        tStartTime.tm_min = p->stTimeDay.minute;
        tStartTime.tm_sec = p->stTimeDay.second;

        MAPPIMP("begin:%d-%d-%dT %d:%d:%d\n",tStartTime.tm_year+1900,tStartTime.tm_mon+1,tStartTime.tm_mday,tStartTime.tm_hour,tStartTime.tm_min,tStartTime.tm_sec);

        time_t dwTime = mktime(&tStartTime);	

        tPlyCond.qwStartTime = dwTime;
        tPlyCond.qwStartTime = tPlyCond.qwStartTime*1000;

        //tStartTime.tm_hour = 24-9; ///< todo  utc时间24点
        //tStartTime.tm_min = 59;
        //tStartTime.tm_sec = 59;
        tStartTime.tm_sec = tStartTime.tm_sec+20; ///<测试放10s录像

        time_t dwEndTime = mktime(&tStartTime);	

        tPlyCond.qwEndTime = dwEndTime;
        tPlyCond.qwEndTime = tPlyCond.qwEndTime*1000+999;
        tPlyCond.qwResEndTime = tPlyCond.qwStartTime;
        tPlyCond.qwResEndTime = tPlyCond.qwEndTime;        
        MAPPERR("begin:%llu-end:%llu\n",tPlyCond.qwStartTime,tPlyCond.qwEndTime);
        tPlyCond.eGetFrameDataMode = 0;

        eRet = NvrRecSetPlayTaskChn(TRUE,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel,&tPlyCond);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("NvrRecSetPlayTaskChn sid:%d,taskid:"FORMAT_U32",chn:%d add failed:%d\n",SID,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel,eRet);
            nRet = -1;
            break;
        }
        MAPPDBG("NvrRecSetPlayTaskChn sid:%d,taskid:"FORMAT_U32",chn:%d add succ\n",SID,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel);
        TNvrRecMsFrameCBParam tCBParam; ///<帧回调参数  
        tCBParam.eDataPackFormat = E_UMP_DataPackFormat_Frame;
        tCBParam.pfDataCallBackProc = MappRecPutVidFrameDateCB;
        tCBParam.pvContext = (void *)&g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].dwContext;
        tCBParam.tTrackId.m_dwTrackIndx = 0;
        tCBParam.tTrackId.m_eTrackType = E_UMP_TrackType_Video;
        eRet = NvrRecSetPlayTaskChnOutCB(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel, &tCBParam);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("NvrRecSetPlayTaskChnOutCB sid:%d,taskid:"FORMAT_U32",chn:%d play failed:%d\n",SID,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel,eRet);
            break;
        }
        MAPPDBG("NvrRecSetPlayTaskChnOutCB sid:%d,taskid:"FORMAT_U32",chn:%d play succ\n",SID,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel);
        
        eRet = NvrRecStartPlay(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel);
        MAPPERR("NvrRecStartPlay sid:%d chn:%d taskid:"FORMAT_U32", ret:%d\n",SID,p->channel,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,eRet);

        //音频播放
        TNvrRecMsFrameCBParam tAudParam;

        mzero(tAudParam);
        
        tAudParam.eDataPackFormat = E_UMP_TrackType_Audio;
        tAudParam.tTrackId.m_dwTrackIndx = 0;
        tAudParam.tTrackId.m_eTrackType = E_UMP_DataPackFormat_Frame;
        tAudParam.pfDataCallBackProc = MappRecPutAudFrameDateCB;
        tAudParam.pvContext = (void *)&g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].dwContext;
        eRet = NvrRecSetPlayTaskChnAudOutCB(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID, p->channel, 0, &tAudParam);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("sid:%d ply chnid:%u,taskid:"FORMAT_U32" NvrRecSetPlayTaskChnAudOutCB failed ret:%d\n",SID,p->channel,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,eRet);
            eRet = NVR_ERR__OK; //音频视频继续
        }
        
    }while(0);

    if(tResp.result>= 0 && NVR_ERR__OK != eRet)
    {
        tResp.result = -1;
        MappRecStopPlyResource(SID,p->channel);
    }
    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_RECORD_PLAYCONTROL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlPlayRecordResp));
    if(nRet < 0)
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);        
        MAPPERR("sid:%d,chn:%d avSendIOCtrl %s\n",SID,p->channel,achStr);
    } 
    else
    {
        MAPPERR("sid:%d,chn:%d avSendIOCtrl succ\n",SID,p->channel);
    }
    
    return nRet;
}

static int MappRecPlayStart(int SID, int avIndex, char *buf)
{
    int nRet = 0;
    int nErr = 0; //放像资源错误
    NVRSTATUS eRet = NVR_ERR__OK;
    NVRSTATUS eAudRet = NVR_ERR__OK;
    TNvrRecPlayCond tPlyCond;
    pthread_t ThreadID;

    MappMsgAVIoctrlPlayRecord *p = (MappMsgAVIoctrlPlayRecord *)buf;
    SMsgAVIoctrlPlayRecordResp tResp;
    mzero(tResp);
    mzero(tPlyCond);
    tResp.command = p->command;

    MAPPERR("type[0x2005]rec play start[0x%x] SID:%d,avindex:%d,chn:%d\n",tResp.command,SID,avIndex,p->channel);

    MAPP_SEMTAKE(g_tMappRecMgr.ahRecPlySem[SID], MAPP_SEM_RECPLY);
    
    do 
    {
        if(1 == g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPlying )
        {
            tResp.result = g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].nPlyChn;
            
            MAPPERR("SID:%d,chn:%d is plying\n",SID,p->channel);
            if(1 == g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause)
            {
                TNvrRecPlayVCRCtrlCmd tPlayVCRCmd;
                mzero(tPlayVCRCmd);
                tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_RESUME;
                MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
                g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause = 0;
                MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
                NvrRecPlayVCRCtrl(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID, p->channel, &tPlayVCRCmd);
            }
            else
            {
                //tResp.result = -1;
            }
            
            break;            
        }
        

        TMappSidChIdInfo *ptSidChIdInfo = (TMappSidChIdInfo *)malloc(sizeof(TMappSidChIdInfo)); // free in MappRecPlayThead
        ptSidChIdInfo->nChId = p->channel;
        ptSidChIdInfo->SID = SID;  
        nRet = p->channel+MAPP_MAX_CHN_NUM;
        /*nRet = IOTC_Session_Get_Free_Channel(SID); //<放像需要单独创建av
        if(nRet<0)
        {
            char achStr[NVR_MAX_STR256_LEN];
            mzero(achStr);
            MappIotcErrToStr(nRet,achStr);        
            MAPPERR("sid:%d,chn:%d IOTC_Session_Get_Free_Channel %s\n",SID,p->channel,achStr);
            tResp.result = -1;
            break;
        }*/
        MAPPERR("sid:%d,plychn:%d-%d \n",SID,p->channel,nRet);
        tResp.result = nRet;
        //OsApi_Delay(200);
        g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].nPlyChn = tResp.result;
       
        if((nRet = pthread_create(&ThreadID, NULL, &MappRecPlayThead, (void *)ptSidChIdInfo)))
        {
            if(NULL != ptSidChIdInfo)
            {
                free(ptSidChIdInfo);
                ptSidChIdInfo = NULL;
            }
            MAPPERR("MappRecPlayThead create failed:%d\n", nRet);
            break;
        }
        pthread_detach(ThreadID);

        
        
        eRet = MappRecCreatePlyTask(SID,p->channel);
        if(NVR_ERR__OK != eRet)
        {
            if(NVR_ERR__REC_NO_IDLE_PLY_TASK == eRet || NVR_ERR__REC_PLAYER_FULL == eRet)
            {
                nErr = -2;
            }
            MAPPERR("SID:%d chn:%d create ply task failed %d,result:%d\n",SID,p->channel,eRet,nErr);            
            break;
        }

        struct tm tStartTime;
        mzero(tStartTime);
        tStartTime.tm_year = p->stStartTime.year-1900;
        tStartTime.tm_mon = p->stStartTime.month-1;
        tStartTime.tm_mday = p->stStartTime.day;
        tStartTime.tm_hour = p->stStartTime.hour;
        tStartTime.tm_min = p->stStartTime.minute;
        tStartTime.tm_sec = p->stStartTime.second;

        struct tm tEndTime;
        mzero(tEndTime);
        tEndTime.tm_year = p->stEndTime.year-1900;
        tEndTime.tm_mon = p->stEndTime.month-1;
        tEndTime.tm_mday = p->stEndTime.day;
        tEndTime.tm_hour = p->stEndTime.hour;
        tEndTime.tm_min = p->stEndTime.minute;
        tEndTime.tm_sec = p->stEndTime.second;

        MAPPIMP("chn:%d,begin:%d-%d-%dT %d:%d:%d end:%d-%d-%dT %d:%d:%d\n",p->channel,
            tStartTime.tm_year+1900,tStartTime.tm_mon+1,tStartTime.tm_mday,tStartTime.tm_hour,tStartTime.tm_min,tStartTime.tm_sec,
            tEndTime.tm_year+1900,tEndTime.tm_mon+1,tEndTime.tm_mday,tEndTime.tm_hour,tEndTime.tm_min,tEndTime.tm_sec);

        time_t dwTime = mktime(&tStartTime);	

        tPlyCond.qwStartTime = dwTime;
        tPlyCond.qwStartTime = tPlyCond.qwStartTime*1000;
        

        time_t dwEndTime = mktime(&tEndTime);	

        tPlyCond.qwEndTime = dwEndTime;
        tPlyCond.qwEndTime = tPlyCond.qwEndTime*1000+999;
        tPlyCond.qwResStartTime = 0;
        tPlyCond.qwResEndTime = tPlyCond.qwEndTime;        
        MAPPERR("begin:%llu-%llu-end:%llu-%llu\n",tPlyCond.qwStartTime,tPlyCond.qwResStartTime,tPlyCond.qwEndTime,tPlyCond.qwResEndTime);
        tPlyCond.eGetFrameDataMode = 0;

        eRet = NvrRecSetPlayTaskChn(TRUE,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel,&tPlyCond);
        if(NVR_ERR__OK != eRet)
        {
            if(NVR_ERR__REC_PLAYER_FULL == eRet)
            {
                nErr = -2;
            }
            MAPPERR("NvrRecSetPlayTaskChn sid:%d,taskid:"FORMAT_U32",chn:%d add failed:%d,result:%d\n",SID,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel,eRet,nErr);            
            break;
        }
        MAPPDBG("NvrRecSetPlayTaskChn sid:%d,taskid:"FORMAT_U32",chn:%d add succ\n",SID,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel);
        TNvrRecMsFrameCBParam tCBParam; ///<帧回调参数  
        tCBParam.eDataPackFormat = E_UMP_DataPackFormat_Frame;
        tCBParam.pfDataCallBackProc = MappRecPutVidFrameDateCB;
        tCBParam.pvContext = (void *)&g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].dwContext;
        tCBParam.tTrackId.m_dwTrackIndx = 0;
        tCBParam.tTrackId.m_eTrackType = E_UMP_TrackType_Video;
        eRet = NvrRecSetPlayTaskChnOutCB(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel, &tCBParam);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("NvrRecSetPlayTaskChnOutCB sid:%d,taskid:"FORMAT_U32",chn:%d play failed:%d\n",SID,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel,eRet);
            break;
        }
        MAPPDBG("NvrRecSetPlayTaskChnOutCB sid:%d,taskid:"FORMAT_U32",chn:%d play succ\n",SID,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel);
        
        eRet = NvrRecStartPlay(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,p->channel);
        MAPPERR("NvrRecStartPlay sid:%d chn:%d taskid:"FORMAT_U32", ret:%d\n",SID,p->channel,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,eRet);

        //音频播放
        TNvrRecMsFrameCBParam tAudParam;

        mzero(tAudParam);
        
        tAudParam.eDataPackFormat = E_UMP_TrackType_Audio;
        tAudParam.tTrackId.m_dwTrackIndx = 0;
        tAudParam.tTrackId.m_eTrackType = E_UMP_DataPackFormat_Frame;
        tAudParam.pfDataCallBackProc = MappRecPutAudFrameDateCB;
        tAudParam.pvContext = (void *)&g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].dwContext;
        eAudRet = NvrRecSetPlayTaskChnAudOutCB(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID, p->channel, 0, &tAudParam);
        if(NVR_ERR__OK != eAudRet)  //音频错误不处理
        {
            MAPPERR("sid:%d ply chn:%u,taskid:"FORMAT_U32" NvrRecSetPlayTaskChnAudOutCB failed ret:%d\n",SID,p->channel,g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID,eAudRet);            
        }
        
    }while(0);
    MAPP_SEMGIVE(g_tMappRecMgr.ahRecPlySem[SID], MAPP_SEM_RECPLY);

    if(tResp.result>= 0 && NVR_ERR__OK != eRet)
    {
        tResp.result = -1;
        if(nErr == -2 || NVR_ERR__REC_PLAYER_FULL == eRet || NVR_ERR__REC_NO_IDLE_PLY_TASK == eRet)
        {
            tResp.result = -2;
        }
        MappRecStopPlyResource(SID,p->channel);
    }
    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_RECORD_PLAYCONTROL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlPlayRecordResp));
    if(nRet < 0)
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);        
        MAPPERR("type[0x2005]sid:%d,chn:%d avSendIOCtrl %s\n",SID,p->channel,achStr);
    } 
    else
    {
        MAPPERR("type[0x2005]sid:%d,chn:%d result:%d,avSendIOCtrl succ\n",SID,p->channel,tResp.result);
    }
    
    return nRet;
}

static int MappRecStopPlay_t(int SID, int avIndex, char *buf)
{
    int nRet;
    
    
    SMsgAVIoctrlPlayRecord *p = (SMsgAVIoctrlPlayRecord *)buf;
    SMsgAVIoctrlPlayRecordResp tResp;

    MAPPERR("type[0x31A]rec play stop[0x%x] SID:%d,chn:%d\n",p->command,SID,p->channel);
    
    mzero(tResp);
    tResp.command = AVIOCTRL_RECORD_PLAY_STOP;
    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_RECORD_PLAYCONTROL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlPlayRecordResp));
    if(nRet < 0)
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);        
        MAPPERR("sid:%d,chn:%d play stop avSendIOCtrl %s\n",SID,p->channel,achStr);
    } 
    else
    {
        MAPPERR("sid:%d,chn:%d play stop avSendIOCtrl succ begin to release ply resouce\n",SID,p->channel);
    }

    

    nRet = MappRecStopPlyResource(SID,p->channel);

    
    
    return nRet;
}
static int MappRecStopPlay(int SID, int avIndex, char *buf)
{
    int nRet;
    
    
    MappMsgAVIoctrlPlayRecord *p = (MappMsgAVIoctrlPlayRecord *)buf;
    SMsgAVIoctrlPlayRecordResp tResp;

    MAPPERR("type[0x2005]rec play stop[0x%x] SID:%d,avindex:%d chn:%d\n",p->command,SID,avIndex,p->channel);
    MAPP_SEMTAKE(g_tMappRecMgr.ahRecPlySem[SID], MAPP_SEM_RECPLY);
    mzero(tResp);
    tResp.command = AVIOCTRL_RECORD_PLAY_STOP;
    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_RECORD_PLAYCONTROL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlPlayRecordResp));
    if(nRet < 0)
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);        
        MAPPERR("sid:%d,avindex:%d chn:%d play stop avSendIOCtrl %s\n",SID,avIndex,p->channel,achStr);
    } 
    else
    {
        MAPPERR("sid:%d,avindex:%d chn:%d play stop avSendIOCtrl succ begin to release ply resouce\n",SID,avIndex,p->channel);
    }

    

    nRet = MappRecStopPlyResource(SID,p->channel);

    MAPP_SEMGIVE(g_tMappRecMgr.ahRecPlySem[SID], MAPP_SEM_RECPLY);
    
    return nRet;
}


static int MappRecPlayPause(int SID, int avIndex, char *buf)
{
    int nRet;
    TNvrRecPlayVCRCtrlCmd tPlayVCRCmd;

    
    MappMsgAVIoctrlPlayRecord *p = (MappMsgAVIoctrlPlayRecord *)buf;
    SMsgAVIoctrlPlayRecordResp tResp;  


    mzero(tPlayVCRCmd);

    if (g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause == 0)
    {
        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_PAUSE;
        MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause = 1;
        MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        NvrRecPlayVCRCtrl(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID, p->channel, &tPlayVCRCmd);
    }
    else
    {
        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_RESUME;
        MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause = 0;
        MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        NvrRecPlayVCRCtrl(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID, p->channel, &tPlayVCRCmd);
    }
    MAPPDBG("type[0x2005]play pause[0x%x] SID:%d avindex:%d chn:%d,pause:%d\n",p->command,SID,avIndex,p->channel, g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause);
    
    mzero(tResp);
    tResp.command = AVIOCTRL_RECORD_PLAY_PAUSE;
    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_RECORD_PLAYCONTROL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlPlayRecordResp));
    if(nRet < 0)
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);        
        MAPPERR("type[0x2005] sid:%d,avindex:%d chn:%d play pause[0x%x-%d] avSendIOCtrl %s\n",SID,avIndex,p->channel,p->command,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause,achStr);
    } 
    else
    {
        MAPPERR("type[0x2005] sid:%d avindex:%d chn:%d play pause[0x%x-%d] avSendIOCtrl succ\n",SID,avIndex,p->channel,p->command,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause);
    }
    
    return nRet;
}
static int MappRecPlayVCR(int SID, int avIndex, char *buf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet;
    TNvrRecPlayVCRCtrlCmd tPlayVCRCmd;

    
    MappMsgAVIoctrlPlayRecord *p = (MappMsgAVIoctrlPlayRecord *)buf;
    SMsgAVIoctrlPlayRecordResp tResp;  
    mzero(tPlayVCRCmd);
    tPlayVCRCmd.eFrameMode = 1;

    MAPPERR("SID:%d,avindex:%d,chn:%d,cmd:%d,param:%d\n",SID,avIndex,p->channel,p->command,p->Param);

    do 
    {
        if(!g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPlying)
        {
            MAPPERR("SID:%d,avindex:%d chn:%d is no plying:%u\n",SID,avIndex,p->channel,g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPlying);
            break;
        }        
        
        switch(p->command)
        {       
            
            case AVIOCTRL_RECORD_PLAY_STEPFORWARD:
                tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_SKIP_FORWARD;
                tPlayVCRCmd.dwSkipTime = p->Param;
                break;
            case AVIOCTRL_RECORD_PLAY_STEPBACKWARD:
                tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_SKIP_BACKWARD;
                tPlayVCRCmd.dwSkipTime = p->Param;
                break;
            case AVIOCTRL_RECORD_PLAY_FORWARD:
                switch(p->Param)
                {
                    case 0:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_NORMAL;
                        break;
                    case 2:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_2XFAST;
                        break;
                    case 4:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_4XFAST;
                        tPlayVCRCmd.eFrameMode = NVR_REC_PLAY_FRAMEMODE_INTRA;
                        break;
                    case 8:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_8XFAST;
                        tPlayVCRCmd.eFrameMode = NVR_REC_PLAY_FRAMEMODE_INTRA;
                        break;
                    case 16:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_16XFAST;
                        tPlayVCRCmd.eFrameMode = NVR_REC_PLAY_FRAMEMODE_INTRA;
                        break;
                    default:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_NORMAL;
                    break;
                            
                }
                break;
            case AVIOCTRL_RECORD_PLAY_BACKWARD:
                switch(p->Param)
                {
                    case 0:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_NORMAL;
                        break;
                    case 2:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_2XSLOW;
                        break;
                    case 4:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_4XSLOW;
                        break;
                    case 8:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_8XSLOW;
                        break;
                    case 16:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_16XSLOW;
                        break;
                    default:
                        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_NORMAL;
                    break;
                            
                }
                break;
            case AVIOCTRL_RECORD_PLAY_SEEKTIME:
                tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_DRAG;
                tPlayVCRCmd.qwSeekTime = (u64)p->Param*1000;
                break;
            case AVIOCTRL_RECORD_PLAY_IFRAME:
                tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_FRAME;
                break;
            default:
                MAPPERR("unkonw cmd no deal\n");
            break;
        }
        eRet = NvrRecPlayVCRCtrl(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID, p->channel, &tPlayVCRCmd);
        MAPPERR("NvrRecPlayVCRCtrl chn:%d,cmd:%d,SkipTime:"FORMAT_U32",time:%llu,ret:%d\n",p->channel,tPlayVCRCmd.eVCRCmdType,tPlayVCRCmd.dwSkipTime,tPlayVCRCmd.qwSeekTime,eRet)

        
        
    }while(0);

    mzero(tResp);
    tResp.command = p->command;
    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_RECORD_PLAYCONTROL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlPlayRecordResp));
    if(nRet < 0)
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);        
        MAPPERR("type[0x2005] sid:%d,avindex:%d chn:%d play cmd:0x%x avSendIOCtrl %s\n",SID,avIndex,p->channel,p->command,achStr);
    } 
    else
    {
        MAPPERR("type[0x2005] sid:%d avindex:%d chn:%d play cmd:0x%x avSendIOCtrl succ\n",SID,avIndex,p->channel,p->command);
    }

    
    
    return nRet;
}

static int MappRecPlayPause_t(int SID, int avIndex, char *buf)
{
    int nRet;
    TNvrRecPlayVCRCtrlCmd tPlayVCRCmd;

    
    SMsgAVIoctrlPlayRecord *p = (SMsgAVIoctrlPlayRecord *)buf;
    SMsgAVIoctrlPlayRecordResp tResp;  


    mzero(tPlayVCRCmd);

    if (g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause == 0)
    {
        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_PAUSE;
        MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause = 1;
        MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        NvrRecPlayVCRCtrl(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID, p->channel, &tPlayVCRCmd);
    }
    else
    {
        tPlayVCRCmd.eVCRCmdType = NVR_REC_PLAY_VCR_CMD_TYPE_RESUME;
        MAPP_SEMTAKE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause = 0;
        MAPP_SEMGIVE(g_tMappRecMgr.hRecSem, MAPP_SEM_REC);
        NvrRecPlayVCRCtrl(g_tMappRecMgr.atRecPlyInfo[SID].dwTaskID, p->channel, &tPlayVCRCmd);
    }
    MAPPERR("type[0x31A]play pause[0x%x] SID:%d,chn:%d,pause:%d\n",p->command,SID,p->channel, g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[p->channel].byPause);
    
    mzero(tResp);
    tResp.command = AVIOCTRL_RECORD_PLAY_PAUSE;
    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_RECORD_PLAYCONTROL_RESP, (char *)&tResp, sizeof(SMsgAVIoctrlPlayRecordResp));
    if(nRet < 0)
    {
        char achStr[NVR_MAX_STR256_LEN];
        mzero(achStr);
        MappAvErrToStr(nRet,achStr);        
        MAPPERR("sid:%d,chn:%d play pause avSendIOCtrl %s\n",SID,p->channel,achStr);
    } 
    else
    {
        MAPPERR("sid:%d,chn:%d play pause avSendIOCtrl succ\n",SID,p->channel);
    }
    
    return nRet;
}



//--------------------以上为内部函数，以下为mapprec.h定义函数-------------------------------
int MappRecInit()
{
    int i = 0;
    int j = 0;

    mzero(g_tMappRecMgr);

    if( !OsApi_SemBCreate(&g_tMappRecMgr.hRecSem) )
    {
        MAPPERR("create g_tMappRecMgr.hRecSem failed\n");
    }

    for (i = 0;i < g_tMappMgr.bySupAppNum;i++)
    {
        if( !OsApi_SemBCreate(&g_tMappRecMgr.ahRecPlySem[i]) )
        {
            MAPPERR("create g_tMappRecMgr.ahRecPlySem[%d] failed\n",i);
        }
        
        for(j = 0;j<g_tMappMgr.nChnNum;j++)
        {
            g_tMappRecMgr.atRecPlyInfo[i].atRecChnInfo[j].avIndex = -1; //无效
        }
    }
    
    MAPPERR("mapprecinit succ app:%u\n",g_tMappMgr.bySupAppNum);
    return 0 ;
}
int MappRecRelease(int SID)
{
    int j = 0;

    if(0 == g_tMappRecMgr.byAppNum)
    {
        MAPPERR("no app is plying\n");
        return 0;
    }
            
    if(g_tMappRecMgr.atRecPlyInfo[SID].byPlying)
    {
        u8 byCount = 0;
        u8 byPlyNum = g_tMappRecMgr.atRecPlyInfo[SID].byPlyChnNum;
        for(j = 0;j<g_tMappMgr.nChnNum;j++)
        {
            if(byCount >= byPlyNum)
            {
                break;
            }
            if(g_tMappRecMgr.atRecPlyInfo[SID].atRecChnInfo[j].byPlying)
            {
                byCount++;
                MAPPERR("SID:%d,chn:%d is plying, begin to release\n",SID,j);
                MappRecStopPlyResource(SID,j);
            }
        }
    }
    
    return 0;
}


int MappRecQuery(int SID, int avIndex, char *buf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet = 0;
    int i = 0;
    int nSize = 0;
    int nCount = 0;
    u32 dwTaskId = -1;
    BOOL32 bFinish = FALSE;
    u32 dwBeginIndex = 0;
    TNvrRecQueryCond tQueryCond; 
    ENvrRecEventType aeEvtType[NVR_REC_EVENT_TYPE_MAX] = {0};
    char Sndbuf[AV_MAX_IOCTRL_DATA_SIZE] = {0};
    MappMsgAVIoctrlQueryRecReq *p = (MappMsgAVIoctrlQueryRecReq *)buf; 
    MappMsgAVIoctrlQueryRecResp *ptRecList = NULL;
    MappMsgAVIoctrlQueryRecResp *pResp = (MappMsgAVIoctrlQueryRecResp*)Sndbuf;

    MAPPERR("type[0x2003]SID:%d,avindex:%d get event rec chn:%d,time:%d-%d-%d %d:%d:%d---%d-%d-%d %d:%d:%d,event:%d\n",SID,avIndex,p->channel,
        p->stStartTime.year,p->stStartTime.month,p->stStartTime.day,p->stStartTime.hour,p->stStartTime.minute,p->stStartTime.second,
        p->stEndTime.year,p->stEndTime.month,p->stEndTime.day,p->stEndTime.hour,p->stEndTime.minute,p->stEndTime.second,p->event);
        

    do 
    {
        mzero(tQueryCond);
        tQueryCond.bQueryRecord = FALSE;        
        tQueryCond.wChnId = p->channel;
        tQueryCond.bSpecialEvent = 0;
        if(p->event == MAPP_EVENT_ALL)
        {
            tQueryCond.bAllEvent = TRUE;
        }
        else
        {
            tQueryCond.bAllEvent = FALSE;            
            tQueryCond.wEventNum = 1;
            aeEvtType[0] = p->event;
            tQueryCond.peRecEventList = aeEvtType;
        }         

        struct tm tStartTime;
        mzero(tStartTime);
        tStartTime.tm_year = p->stStartTime.year-1900;
        tStartTime.tm_mon = p->stStartTime.month-1;
        tStartTime.tm_mday = p->stStartTime.day;
        tStartTime.tm_hour = p->stStartTime.hour;
        tStartTime.tm_min = p->stStartTime.minute;
        tStartTime.tm_sec = p->stStartTime.second;

        MAPPDBG("begin:%d-%d-%d %d:%d:%d\n",tStartTime.tm_year,tStartTime.tm_mon,tStartTime.tm_mday,tStartTime.tm_hour,tStartTime.tm_min,tStartTime.tm_sec);

        time_t dwTime = mktime(&tStartTime);	

        tQueryCond.qwStartTime = dwTime;
        tQueryCond.qwStartTime = tQueryCond.qwStartTime*1000;
        MAPPDBG("begin:%d-%llu\n",dwTime,tQueryCond.qwStartTime);

        struct tm tEndTime;
        mzero(tEndTime);
        tEndTime.tm_year = p->stEndTime.year-1900;
        tEndTime.tm_mon = p->stEndTime.month-1;
        tEndTime.tm_mday = p->stEndTime.day;
        tEndTime.tm_hour = p->stEndTime.hour;
        tEndTime.tm_min = p->stEndTime.minute;
        tEndTime.tm_sec = p->stEndTime.second;

        MAPPDBG("end:%d-%d-%d %d:%d:%d\n",tEndTime.tm_year,tEndTime.tm_mon,tEndTime.tm_mday,tEndTime.tm_hour,tEndTime.tm_min,tEndTime.tm_sec);
         

        dwTime = mktime(&tEndTime);	

        tQueryCond.qwEndTime = dwTime;
        tQueryCond.qwEndTime = tQueryCond.qwEndTime*1000;
        
        
        eRet = NvrRecCreateQueryEventTask(&tQueryCond,&dwTaskId);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("NvrRecCreateQueryEventTask failed:%d\n",eRet);
            break;
        }
        MAPPIMP("S:%llu-E:%llu,qrecord:%d,allevent:%d,taskid:"FORMAT_U32"\n",tQueryCond.qwStartTime,tQueryCond.qwEndTime,tQueryCond.bQueryRecord,tQueryCond.bAllEvent,dwTaskId);
        TNvrRecEventQueryResult tQueryRet;
        mzero(tQueryRet);

        dwBeginIndex = 0;
        while (!bFinish)
        {
            dwBeginIndex = dwBeginIndex+nCount;
            eRet = NvrRecEventQuery(dwTaskId,dwBeginIndex,40,&tQueryRet); //40 不可放大，否则超过1024发送异常
            if(NVR_ERR__OK != eRet)
            {
                MAPPERR("NvrRecEventQuery failed:%d\n",eRet);
                break;
            }
            MAPPDBG("SID:%d,avindex:%d,chn:%d fin:%d,index:"FORMAT_U32",total:"FORMAT_U32",curnum:"FORMAT_U32"\n",SID,avIndex,p->channel,tQueryRet.bFinshed,dwBeginIndex,tQueryRet.dwTotalNum,tQueryRet.dwCurNum);
            nCount = tQueryRet.dwCurNum;
            bFinish = tQueryRet.bFinshed;
            if(tQueryRet.dwTotalNum == 0 && tQueryRet.dwCurNum == 0)
            {
                bFinish = 1;
            }

            ptRecList = (MappMsgAVIoctrlQueryRecResp*)malloc(sizeof(MappMsgAVIoctrlQueryRecResp) + nCount * sizeof(MappAvRecorder));
            if(NULL == ptRecList)
            {
                MAPPERR("malloc MappMsgAVIoctrlQueryRecResp failed\n");
                break;
            }
            memset(ptRecList, 0, sizeof(MappMsgAVIoctrlQueryRecResp));
            ptRecList->total = nCount; 
            ptRecList->index = dwBeginIndex;    
            ptRecList->endflag = bFinish; 
            ptRecList->count = nCount;

            for(i = 0;i<nCount;i++)
            {
                struct tm tTime = { 0 };
                time_t dwSec = 0;
                TNvrRecEventQueryResultItem * ptCurItem = tQueryRet.ptResultItemList + i;
                u64 qwStart = ptCurItem->qwCreateTime;

                dwSec = qwStart/1000; 
                localtime_r((time_t *)&dwSec, &tTime);  
                
                ptRecList->stEvent[i].stStartTime.year = tTime.tm_year+1900;
                ptRecList->stEvent[i].stStartTime.month = tTime.tm_mon+1;
                ptRecList->stEvent[i].stStartTime.day = tTime.tm_mday;
                ptRecList->stEvent[i].stStartTime.hour = tTime.tm_hour;
                ptRecList->stEvent[i].stStartTime.minute = tTime.tm_min;
                ptRecList->stEvent[i].stStartTime.second = tTime.tm_sec;

                struct tm tListEndTime = { 0 };
                time_t dwEndSec = 0;

                u64 qwEndTime = ptCurItem->qwCreateTime+ptCurItem->dwDuration;

                dwEndSec = qwEndTime/1000; 
                localtime_r((time_t *)&dwEndSec, &tListEndTime);

                ptRecList->stEvent[i].stEndTime.year = tListEndTime.tm_year+1900;
                ptRecList->stEvent[i].stEndTime.month = tListEndTime.tm_mon+1;
                ptRecList->stEvent[i].stEndTime.day = tListEndTime.tm_mday;
                ptRecList->stEvent[i].stEndTime.hour = tListEndTime.tm_hour;
                ptRecList->stEvent[i].stEndTime.minute = tListEndTime.tm_min;
                ptRecList->stEvent[i].stEndTime.second = tListEndTime.tm_sec;

                ptRecList->stEvent[i].audNum = (unsigned char)ptCurItem->dwAudNum;
                

                MappRecEventSwitch(ptCurItem->eEventType,&ptRecList->stEvent[i].event);
                

                MAPPDBG("No.%d rec:%llu-%llu %u-%u-%u %u:%u:%u %u-%u-%u %u:%u:%u event:%d\n",i+dwBeginIndex,qwStart,qwEndTime,ptRecList->stEvent[i].stStartTime.year,ptRecList->stEvent[i].stStartTime.month,ptRecList->stEvent[i].stStartTime.day,
                    ptRecList->stEvent[i].stStartTime.hour,ptRecList->stEvent[i].stStartTime.minute,ptRecList->stEvent[i].stStartTime.second,
                    ptRecList->stEvent[i].stEndTime.year,ptRecList->stEvent[i].stEndTime.month,ptRecList->stEvent[i].stEndTime.day,
                    ptRecList->stEvent[i].stEndTime.hour,ptRecList->stEvent[i].stEndTime.minute,ptRecList->stEvent[i].stEndTime.second,ptRecList->stEvent[i].event);
            }
            pResp->total = ptRecList->total;
            pResp->index = ptRecList->index;
            pResp->endflag = ptRecList->endflag;
            pResp->count = ptRecList->count;
            pResp->channel = p->channel;
            memcpy(&pResp->stEvent[0], &ptRecList->stEvent[0], sizeof(MappAvRecorder)*pResp->count);

            if(nCount<=1)
            {
                nCount = 1;
            }
            nSize = sizeof(MappMsgAVIoctrlQueryRecResp)+sizeof(MappAvRecorder)*(nCount-1);
            nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_QUERY_RECORD_RESP, (char *)pResp, nSize); 
            MAPPERR("type[0x2003]SID:%d chn:%d query record index:%d count:%d fin:%d avSendIOCtrl size:%d ret:%d\n",SID,pResp->channel,pResp->index,pResp->count,pResp->endflag,nSize,nRet);
            if(NULL != ptRecList)
            {
                free(ptRecList);
                ptRecList = NULL;
            }          
            
        }

    }while(0);

    if (dwTaskId != -1)
    {
        eRet = NvrRecDestroyQueryRecTask(dwTaskId);
        MAPPERR("NvrRecDestroyQueryRecTask SID:%d avindex:%d chnid:%d taskid:"FORMAT_U32" ret:%d\n",SID,avIndex,p->channel,dwTaskId,eRet);
    }  
    
    if(NVR_ERR__OK != eRet)
    {
        pResp->channel = p->channel;
        pResp->total = 0;
        pResp->index = 0;
        pResp->endflag = 1;
        pResp->count = 0;
        nSize = sizeof(SMsgAVIoctrlListEventResp);
        nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_QUERY_RECORD_RESP, (char *)pResp, nSize);
        MAPPERR("type[0x2003]SID:%d chn:%d avindex:%d query record=0 avSendIOCtrl ret:%d\n",SID,avIndex,pResp->channel,nRet);
    }
   
    if(NULL != ptRecList)
    {
        free(ptRecList);
        ptRecList = NULL;
    }
    
    return nRet;
}


int MappRecQuery_t(int SID, int avIndex, char *buf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet = 0;
    int i = 0;
    int nSize = 0;
    int nCount = 0;
    u32 dwTaskId = -1;
    BOOL32 bFinish = FALSE;
    u32 dwBeginIndex = 0;
    TNvrRecQueryCond tQueryCond; 
    SMsgAVIoctrlListEventReq *p = (SMsgAVIoctrlListEventReq *)buf; 
    SMsgAVIoctrlListEventResp *ptRecList = NULL;
    SMsgAVIoctrlListEventResp *pResp = (SMsgAVIoctrlListEventResp*)buf;

    MAPPERR("type[0x318]SID:%d,avindex:%d get event rec chn:%d,time:%d-%d-%d %d-%d-%d---%d-%d-%d %d-%d-%d,event:%d,status:%d\n",SID,avIndex,p->channel,
        p->stStartTime.year,p->stStartTime.month,p->stStartTime.day,p->stStartTime.hour,p->stStartTime.minute,p->stStartTime.second,
        p->stEndTime.year,p->stEndTime.month,p->stEndTime.day,p->stEndTime.hour,p->stEndTime.minute,p->stEndTime.second,
        p->event,p->status);
        

    do 
    {
        ENvrRecEventType aeEvtType[NVR_REC_EVENT_TYPE_MAX] = {0};
        mzero(tQueryCond);
        tQueryCond.bQueryRecord = FALSE;
        tQueryCond.bAllEvent = 0;
        tQueryCond.wChnId = p->channel;
        tQueryCond.bSpecialEvent = 0;
        tQueryCond.wEventNum = 1;
        aeEvtType[0] = 4;
        tQueryCond.peRecEventList = aeEvtType;

        struct tm tStartTime;
        mzero(tStartTime);
        tStartTime.tm_year = p->stStartTime.year-1900;
        tStartTime.tm_mon = p->stStartTime.month-1;
        tStartTime.tm_mday = p->stStartTime.day;
        tStartTime.tm_hour = p->stStartTime.hour;
        tStartTime.tm_min = p->stStartTime.minute;
        tStartTime.tm_sec = p->stStartTime.second;

        MAPPDBG("begin:%d-%d-%d %d-%d-%d\n",tStartTime.tm_year,tStartTime.tm_mon,tStartTime.tm_mday,tStartTime.tm_hour,tStartTime.tm_min,tStartTime.tm_sec);

        time_t dwTime = mktime(&tStartTime);	

        tQueryCond.qwStartTime = dwTime;
        tQueryCond.qwStartTime = tQueryCond.qwStartTime*1000;
        MAPPDBG("begin:%d-%llu\n",dwTime,tQueryCond.qwStartTime);

        struct tm tEndTime;
        mzero(tEndTime);
        tEndTime.tm_year = p->stEndTime.year-1900;
        tEndTime.tm_mon = p->stEndTime.month-1;
        tEndTime.tm_mday = p->stEndTime.day;
        tEndTime.tm_hour = p->stEndTime.hour;
        tEndTime.tm_min = p->stEndTime.minute;
        tEndTime.tm_sec = p->stEndTime.second;

        MAPPDBG("end:%d-%d-%d %d-%d-%d\n",tEndTime.tm_year,tEndTime.tm_mon,tEndTime.tm_mday,tEndTime.tm_hour,tEndTime.tm_min,tEndTime.tm_sec);
         

        dwTime = mktime(&tEndTime);	

        tQueryCond.qwEndTime = dwTime;
        tQueryCond.qwEndTime = tQueryCond.qwEndTime*1000;
        
        
        
        
        eRet = NvrRecCreateQueryEventTask(&tQueryCond,&dwTaskId);
        if(NVR_ERR__OK != eRet)
        {
            MAPPERR("NvrRecCreateQueryEventTask failed:%d\n",eRet);
            break;
        }
        MAPPIMP("S:%llu-E:%llu,qrecord:%d,allevent:%d,taskid:"FORMAT_U32"\n",tQueryCond.qwStartTime,tQueryCond.qwEndTime,tQueryCond.bQueryRecord,tQueryCond.bAllEvent,dwTaskId);
        TNvrRecEventQueryResult tQueryRet;
        mzero(tQueryRet);

        dwBeginIndex = 0;
        while (!bFinish)
        {
            dwBeginIndex = dwBeginIndex+nCount;
            eRet = NvrRecEventQuery(dwTaskId,dwBeginIndex,50,&tQueryRet);
            if(NVR_ERR__OK != eRet)
            {
                MAPPERR("NvrRecEventQuery failed:%d\n",eRet);
                break;
            }
            MAPPDBG("SID:%d,avindex:%d,chn:%d fin:%d,index:"FORMAT_U32",total:"FORMAT_U32",curnum:"FORMAT_U32"\n",SID,avIndex,p->channel,tQueryRet.bFinshed,dwBeginIndex,tQueryRet.dwTotalNum,tQueryRet.dwCurNum);
            nCount = tQueryRet.dwCurNum;
            bFinish = tQueryRet.bFinshed;
            if(tQueryRet.dwTotalNum == 0 && tQueryRet.dwCurNum == 0)
            {
                bFinish = 1;
            }

            ptRecList = (SMsgAVIoctrlListEventResp*)malloc(sizeof(SMsgAVIoctrlListEventResp) + nCount * sizeof(SAvEvent));
            if(NULL == ptRecList)
            {
                MAPPERR("malloc SMsgAVIoctrlListEventResp failed\n");
                break;
            }
            memset(ptRecList, 0, sizeof(SMsgAVIoctrlListEventResp));
            ptRecList->total = nCount; 
            ptRecList->index = dwBeginIndex;    
            ptRecList->endflag = bFinish;//tQueryRet.bFinshed;  
            ptRecList->count = nCount;

            for(i = 0;i<nCount;i++)
            {
                struct tm tTime = { 0 };
                time_t dwSec = 0;
                TNvrRecEventQueryResultItem * ptCurItem = tQueryRet.ptResultItemList + i;
                u64 qwStart = ptCurItem->qwCreateTime;

                dwSec = qwStart/1000; 
                localtime_r((time_t *)&dwSec, &tTime);  
                
                ptRecList->stEvent[i].stTime.year = tTime.tm_year+1900;
                ptRecList->stEvent[i].stTime.month = tTime.tm_mon+1;
                ptRecList->stEvent[i].stTime.day = tTime.tm_mday;
                ptRecList->stEvent[i].stTime.hour = tTime.tm_hour;
                ptRecList->stEvent[i].stTime.minute = tTime.tm_min;
                ptRecList->stEvent[i].stTime.second = tTime.tm_sec;

                ptRecList->stEvent[i].event = 0;
                ptRecList->stEvent[i].status = 0;

                MAPPDBG("No.%d Trec:%llu %u-%u-%u %u:%u:%u event:%d\n",i+dwBeginIndex,qwStart,ptRecList->stEvent[i].stTime.year,ptRecList->stEvent[i].stTime.month,ptRecList->stEvent[i].stTime.day,
                    ptRecList->stEvent[i].stTime.hour,ptRecList->stEvent[i].stTime.minute,ptRecList->stEvent[i].stTime.second,ptCurItem->eEventType);
            }
            pResp->total = ptRecList->total;
            pResp->index = ptRecList->index;
            pResp->endflag = ptRecList->endflag;
            pResp->count = ptRecList->count;
            memcpy(&pResp->stEvent[0], &ptRecList->stEvent[0], sizeof(SAvEvent)*pResp->count);

            if(nCount<=1)
            {
                nCount = 1;
            }
            nSize = sizeof(SMsgAVIoctrlListEventResp)+sizeof(SAvEvent)*(nCount-1);
            nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_LISTEVENT_RESP, (char *)pResp, nSize); 
            MAPPERR("type[0x318]SID:%d chn:%d get event reclist:%d fin:%d avSendIOCtrl ret:%d\n",SID,p->channel,pResp->count,pResp->endflag,nRet);
            if(NULL != ptRecList)
            {
                free(ptRecList);
                ptRecList = NULL;
            }          
            
        }

    }while(0);

    if (dwTaskId != -1)
    {
        eRet = NvrRecDestroyQueryRecTask(dwTaskId);
        MAPPERR("NvrRecDestroyQueryRecTask SID:%d avindex:%d taskid:"FORMAT_U32" ret:%d\n",SID,avIndex,dwTaskId,eRet);
    }  
    
    if(NVR_ERR__OK != eRet)
    {
        pResp->total = 0;
        pResp->index = 0;
        pResp->endflag = 1;
        pResp->count = 0;
        nSize = sizeof(SMsgAVIoctrlListEventResp);
        nRet = avSendIOCtrl(avIndex, IOTYPE_USER_IPCAM_LISTEVENT_RESP, (char *)pResp, nSize);
        MAPPERR("type[0x318]SID:%d chn:%d get event reclist=0 avSendIOCtrl ret:%d\n",SID,p->channel,nRet);
    }
   
    if(NULL != ptRecList)
    {
        free(ptRecList);
        ptRecList = NULL;
    }
    
    return nRet;
}


int MappRecPlayCmd_t(int SID, int avIndex, char *buf)
{
    SMsgAVIoctrlPlayRecord *p = (SMsgAVIoctrlPlayRecord *)buf;
    
    MAPPERR("type[0x31A]rec play CMD SID:%d,avindex:%d,chn:%d cmd:0x%X\n",SID,avIndex,p->channel,p->command);
    
    switch(p->command)
    {        ///<0X10 开始放像
        case AVIOCTRL_RECORD_PLAY_START:
            MappRecPlayStart_t(SID, avIndex, buf);
            break;
        case AVIOCTRL_RECORD_PLAY_STOP:
            MappRecStopPlay_t(SID, avIndex, buf);
            break;
        case AVIOCTRL_RECORD_PLAY_PAUSE:
            MappRecPlayPause_t(SID, avIndex, buf);
            break;
        case AVIOCTRL_RECORD_PLAY_STEPFORWARD:
            MAPPERR("stepforward no deal\n");
            break;
        case AVIOCTRL_RECORD_PLAY_STEPBACKWARD:
            MAPPERR("step backward no deal\n");
            break;
        case AVIOCTRL_RECORD_PLAY_FORWARD:
            MAPPERR("forward no deal\n");
            break;
        case AVIOCTRL_RECORD_PLAY_BACKWARD:
            MAPPERR("backward no deal\n");
            break;
        case AVIOCTRL_RECORD_PLAY_SEEKTIME:
            MAPPERR("seektime no deal\n");
            break;
        case AVIOCTRL_RECORD_PLAY_NEXT:
            MAPPERR("next no deal\n");
            break;
        default:
            MAPPERR("unkonw cmd no deal\n");
        break;
    }

    
    
    
}
int MappRecPlayCmd(int SID, int avIndex, char *buf)
{
    MappMsgAVIoctrlPlayRecord *p = (MappMsgAVIoctrlPlayRecord *)buf;
    MAPPERR("type[0x2005]rec play CMD SID:%d,avindex:%d,chn:%d cmd:0x%X\n",SID,avIndex,p->channel,p->command);
    
    
    switch(p->command)
    {        ///<0X10 开始放像
        case AVIOCTRL_RECORD_PLAY_START:
            MappRecPlayStart(SID, avIndex, buf);
            break;
        case AVIOCTRL_RECORD_PLAY_STOP:
            MappRecStopPlay(SID, avIndex, buf);
            break;
        case AVIOCTRL_RECORD_PLAY_PAUSE:
            MappRecPlayPause(SID, avIndex, buf);
            break;
        case AVIOCTRL_RECORD_PLAY_STEPFORWARD:
        case AVIOCTRL_RECORD_PLAY_STEPBACKWARD:
        case AVIOCTRL_RECORD_PLAY_FORWARD:
        case AVIOCTRL_RECORD_PLAY_BACKWARD:
        case AVIOCTRL_RECORD_PLAY_SEEKTIME:
        case AVIOCTRL_RECORD_PLAY_IFRAME:
            MappRecPlayVCR(SID, avIndex, buf);
            break;
        case AVIOCTRL_RECORD_PLAY_NEXT:
            MAPPERR("next no deal\n");
            break;
        default:
            MAPPERR("unkonw cmd no deal\n");
        break;
    }

    
    
    
}
/*int MappRecQueryMonthlyView1(int SID, int avIndex, char *buf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet = 0;
    int i = 0;
    TNvrRecMonthMapTab tMonthlyRec;
    
    MappMsgAVIoctrlQueryMonthlyViewReq *p = (MappMsgAVIoctrlQueryMonthlyViewReq *)buf;
    MappMsgAVIoctrlQueryMonthlyViewResp tResp;
    
    MAPPERR("type[0x2017]query rec monthly view SID:%d,avindex:%d,chn:%d year:%d,month:%d\n",SID,avIndex,p->channel,p->year,p->month);

    mzero(tMonthlyRec);
    mzero(tResp);

    tMonthlyRec.tMonth.wYear = (unsigned short)p->year;
    tMonthlyRec.tMonth.byMonth = p->month;
    tMonthlyRec.dwNum = 1;
    tMonthlyRec.atPair[0].dwChnId = p->channel;


    tResp.channel = p->channel;
    tResp.year = p->year;
    tResp.month = p->month;

    eRet = NvrRecGetMonthMap(&tMonthlyRec);
    if(eRet != NVR_ERR__OK)
    {
        tResp.result = -1;
    }
    else
    {
        tResp.recMonthlyView = tMonthlyRec.atPair[0].dwMonthMap;   

        MAPPDBG("chn:%d,monthlyview:%d\n",tResp.channel,tResp.recMonthlyView);
        for ( i = 0; i<31; i++)
        {
            if ( tResp.recMonthlyView & (1<<i) )
            {
                MAPPDBG("[%d] 1\n", i);
            }
            else
            {
                MAPPDBG("[%d] 0\n", i);
            }
        }
    }

    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_QUERY_REC_MONTHLYVIEW_RESP, (char *)&tResp, sizeof(MappMsgAVIoctrlQueryMonthlyViewResp));
    MAPPERR("type[0x2018]query rec monthly view SID:%d avindex:%d chn:%d,year:%d,month:%d,monthlyview:%d,result:%d,nret:%d\n",SID,avIndex,tResp.channel,tResp.year,tResp.month,tResp.recMonthlyView,tResp.result,nRet);
    
    return 0;
}*/
int MappRecQueryMonthlyView(int SID, int avIndex, char *buf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    int nRet = 0;
    int i = 0;
    TNvrRecMonthMapTab tMonthlyRec;
    char achBuf[128] = {0};
    
    MappMsgAVIoctrlQueryMonthViewReq *p = (MappMsgAVIoctrlQueryMonthViewReq *)buf;
    MappMsgAVIoctrlQueryMonthViewResp tResp;

    mzero(tMonthlyRec);
    mzero(tResp);

    

    for(i = 0;i<p->channelNumber;i++)
    {        
        snprintf(&achBuf[strlen(achBuf)],128,"%d-",p->channel[i]);
        
        tMonthlyRec.atPair[i].dwChnId = p->channel[i];
    }
    MAPPERR("type[0x2019]query rec monthly view SID:%d,avindex:%d,chnnum:%d,chn:%s year:%d,month:%d\n",SID,avIndex,p->channelNumber,achBuf,p->year,p->month);
    

    

    tMonthlyRec.tMonth.wYear = (unsigned short)p->year;
    tMonthlyRec.tMonth.byMonth = p->month;
    tMonthlyRec.dwNum = p->channelNumber;
    

    tResp.channelNumber = p->channelNumber;
    tResp.year = p->year;
    tResp.month = p->month;

    eRet = NvrRecGetMonthMap(&tMonthlyRec);
    if(eRet != NVR_ERR__OK)
    {
        tResp.result = -1;
    }
    else
    {
        for(i = 0;i<p->channelNumber;i++)
        {
            tResp.monthView[i].channel = tMonthlyRec.atPair[i].dwChnId;
            tResp.monthView[i].recMonthlyView = tMonthlyRec.atPair[i].dwMonthMap;
            MAPPDBG("No:%d chn:%d,monthlyview:%d\n",i,tResp.monthView[i].channel,tResp.monthView[i].recMonthlyView);
        } 
    }

    nRet = avSendIOCtrl(avIndex, IOTYPE_USER_EDGEOS_QUERY_REC_MONTHVIEW_RESP, (char *)&tResp, sizeof(MappMsgAVIoctrlQueryMonthViewResp));
    MAPPERR("type[0x2020]query rec monthly view SID:%d avindex:%d chnNum:%d,monthlyview:%d,year:%d,month:%d,result:%d,nret:%d\n",SID,avIndex,tResp.channelNumber,tResp.monthView[0].recMonthlyView,tResp.year,tResp.month,tResp.result,nRet);
    return 0;
}





