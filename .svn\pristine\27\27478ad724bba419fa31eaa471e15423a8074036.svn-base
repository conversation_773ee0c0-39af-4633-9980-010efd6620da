

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrnetwork


## Define debugging symbols
DEBUG = 0
PWLIB_SUPPORT = 0
FSANITIZE = 1
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrnetwork\
	     $(SRC_DIR)/nvrmbnet\
	     $(SRC_DIR)/nvrbluetooth\
		 $(SRC_DIR)/nvrhttps\
	     $(SRC_DIR)/nvr8021x\
		 $(SRC_DIR)/nvrddns\
		 $(SRC_DIR)/nvrwifinet
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrftp/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrrec/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/hal/netcbb \
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/hardware \
		$(CURDIR)/../../../10-common/include/hal/drvlib_64 \
		$(CURDIR)/../../../10-common/include/cbb/ddnsc \
		$(CURDIR)/../../../10-common/include/cbb/upnp \
		$(CURDIR)/../../../10-common/include/cbb/appclt \
		$(CURDIR)/../../../10-common/include/cbb/cjson \
		$(CURDIR)/../../../10-common/include/cbb/mbnet \
		$(CURDIR)/../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/cbb/802dot1x\
		$(CURDIR)/../../../10-common/include/cbb/goahead/linux\
		$(CURDIR)/../../../10-common/include/cbb/openssl
CFLAGS += -D_SKYLATE_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/debug/ubuntu_64_sanitizer
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


