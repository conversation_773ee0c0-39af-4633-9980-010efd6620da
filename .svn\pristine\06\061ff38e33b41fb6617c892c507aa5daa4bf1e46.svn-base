path="../../10-common/version/compileinfo/nvrsrv_his3531dv200.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_update_linux for his3531dv200         =
echo ==============================================

echo "============compile nvrupdate his3531dv200============">>../$path

make -e DEBUG=0 -f makefile_nvrupdate_his3531dv200 clean
make -e DEBUG=0 -f makefile_nvrupdate_his3531dv200 2>>../$path

cp -L -r -f nvrupdate ../../../10-common/version/release/his3531dv200/public/


echo "============compile nvrupproc his3531dv200============">>../$path

make -e DEBUG=0 -f makefile_nvrupproc_his3531dv200 clean
make -e DEBUG=0 -f makefile_nvrupproc_his3531dv200 2>>../$path

cp -L -r -f nvrupproc ../../../10-common/version/release/his3531dv200/public/


echo "============compile nvrupdate_cgi his3531dv200============">>../$path

make -e DEBUG=0 -f makefile_nvrupdate_cgi_his3531dv200 clean
make -e DEBUG=0 -f makefile_nvrupdate_cgi_his3531dv200 2>>../$path

cp -L -r -f nvrupdate_cgi ../../../10-common/version/release/his3531dv200/public/

cd ..
