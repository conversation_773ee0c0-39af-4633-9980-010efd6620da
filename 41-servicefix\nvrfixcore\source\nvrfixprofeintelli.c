/**
* @file     nvrfixprofeintelli
* @brief    nvr fix枪机专业智能相关业务
* <AUTHOR>
* @date     2022-05-05
* @version  1.0
* @copyright V1.0  Copyright(C) 2019 NVR All rights reserved.
*/

#include "nvrfixprofeintelli.h"
#include "basicintellialgctrl_in.h"
#include "nvrfixdev.h"
#ifdef _SSC339G_
#include "ais_in.h"
#endif

#define PROFEINTELBASIC(args, ...)  DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_BASIC, "\t[PROFEINTEL]\t%s "args, _NVRFUN_, ## __VA_ARGS__)
#define PROFEINTELERR(args, ...)    DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_ERR, "\t[PROFEINTEL]\t%s "args, _NVRFUN_, ## __VA_ARGS__)
#define PROFEINTELFILE(args, ...)   DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_FILE, "\t[PROFEINTEL]\t%s "args, _NVRFUN_, ## __VA_ARGS__)
#define PROFEINTELIMP(args, ...)    DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_IMP, "\t[PROFEINTEL]\t%s "args, _NVRFUN_, ## __VA_ARGS__)
#define PROFEINTELDBG(args, ...)    DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_DEBUG, "\t[PROFEINTELl]\t%s "args, _NVRFUN_, ## __VA_ARGS__)
#define PROFEINTELTEMP(args, ...)   DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_TEMP, "\t[PROFEINTEL]\t%s "args, _NVRFUN_, ## __VA_ARGS__)

#define PROFEINTELFLASHERR(args, ...)       LogFlash(FLASH_LOG_ERR, "PROFEINTEL", args, ##__VA_ARGS__)
#define PROFEINTELFLASHNOTICE(args, ...)    LogFlash(FLASH_LOG_NOTICE, "PROFEINTEL", args, ##__VA_ARGS__)
#define PROFEINTELMEMNOTICE(args, ...)      LogMem(MEM_LOG_NOTICE_CORE, DEBUG_LOG_MOD_BASIC_INTEL, "PROFEINTEL", args, ##__VA_ARGS__)

#define PROFEINTEL_ASSERT(p) \
if (NULL == p)          \
{   \
    PROFEINTELERR("[%s]%s assert failed(line:%d)\n", __FILE__,__FUNCTION__, __LINE__);  \
    return NVR_ERR__ASSERT;                                             \
}   \

typedef enum
{
    ALARM_STAT_ALARM,               ///< 是否告警
    ALARM_STAT_TIME,                ///< 告警时间
    ALARM_STAT_CHN,                 ///< 告警通道

    ALARM_STAT_NUM
}EAlarmStat;

static BOOL g_bAlgInited = FALSE;
static PTNvrDoubleList g_ptProIntelDealQueue = NULL;                   ///< 专业智能处理队列
static u8 g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_NUM] = {0};
static u32 g_awAlarmSta[BASICINTELLI_ADVANCED_NUM + 1][ALARM_STAT_NUM] = {{0, 0, 0}};


static s32 NvrFixProfeIntelAlarmDetectCB(s32 nCapChn, EBasicIntelliAlarmType eAlarmType, void *pCBParam);
static void* NvrFixProfeIntelDealThread();
static void NvrFixProfeIntelTest(u8 byMode, u8 param1, u8 param2);

///< --------------------------------- smoke fire alg deal ---------------------------------
static BOOL s_bSmokeFireEnable = FALSE;                         ///< 是否算法使能
static BOOL g_bNeedSecSmokeFireFlag = FALSE;                    ///< 烟火识别需要二次确认标志
static BOOL g_bSecSmokeFireDeal = FALSE;                        ///< 烟火识别二次处理标志
static BOOL g_bSecSmokeFireAlarm = FALSE;                       ///< 烟火识别二次确认标志
static TNvrIntelSmokeFireOtherInfo g_tSmokeFireOtherParam;      ///< 其他参数
static HTIMERHANDLE g_hClearTimer = NULL;              ///< 自动消警定时器
static HTIMERHANDLE g_hSmokeFireClearTimer = NULL;              ///< 烟火识别自动消警定时器
static HTIMERHANDLE g_hSecSmokeFireClearTimer = NULL;           ///< 烟火识别二次确认自动消警定时器
static s32 NvrFixProfeIntelSmokeFireDetect(s32 nCapChn, TBasicIntelliFireAlarmCBParam *pCBParam);
static s32 NvrFixProfeIntelAutoClearTimer( HTIMERHANDLE dwTimerId, void* param);
static s32 NvrFixProfeIntelSmokeFireAutoClearTimer( HTIMERHANDLE dwTimerId, void* param);
static s32 NvrFixProfeIntelSmokeFireSecAutoClearTimer( HTIMERHANDLE dwTimerId, void* param);

NVRSTATUS __BasicIntelliAlgCtrlInit(TBasicIntelliAlgCtrlInitPrm *ptInitPrm)
{
#ifdef _BASIC_INTELLI_
    return BasicIntelliAlgCtrlInit(ptInitPrm);
#else
    return NVR_ERR__OK;
#endif
}

s32 __BasicIntellilSetAlarmDetectParam(s32 l32CapChn, EBasicIntelliAlarmType eAlarmType, void *pParam)
{
    if (!g_bAlgInited)
    {
        PROFEINTELERR(" not init \n");
        return NVR_ERR__ERROR;
    }
#ifdef _BASIC_INTELLI_
    return BasicIntellilSetAlarmDetectParam(l32CapChn, eAlarmType, pParam);
#else
    return 0;
#endif
}

s32 __BasicIntelliSetAlarmDetectCallBack(pfBasicIntelliAlarmDetectCB pFun)
{
#ifdef _BASIC_INTELLI_
    return BasicIntelliSetAlarmDetectCallBack(pFun);
#else
    return 0;
#endif
}

NVRSTATUS NvrFixProfeIntelSnapshotInit()
{
#ifdef _BASIC_INTELLI_
    TBasicIntelliSnapInitParam tSnapInitParam;
    mzero(tSnapInitParam);

    //抓拍在结构化算法初始化时有初始化,调用此接口单独初始化抓拍代表无结构化算法,
    //则默认不支持采集抓拍,抓拍方式必须为2(编码)或者0(自动)

    return BasicIntelliSnapshotInit(&tSnapInitParam);
#else
    return NVR_ERR__OK;
#endif
}

///< -----------------------------     funciton start  -----------------------------
static BOOL NvrFixBasicIntelliAdvSupport(u16 wChnId, TNvrCapLcam *ptCap)
{
    BOOL bRet = FALSE;
    if (wChnId >= NVR_MAX_LCAM_CHN_NUM)
    {
        return FALSE;
    }

    if((NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamIntelCordon.atLcamCapCordon[wChnId].byIntelliAlgSupport) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamIntelInvasion.atLcamCapInvasion[wChnId].byIntelliAlgSupport) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamIntelEnter.atLcamCapEnter[wChnId].byIntelliAlgSupport) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamIntelLeave.atLcamCapLeave[wChnId].byIntelliAlgSupport) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamPeopleGather.atLcamPeoleGather[wChnId].byIntelliAlgSupport) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamIntelPick.atLcamCapPick[wChnId].byIntelliAlgSupport) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamIntelLeft.atLcamCapLeft[wChnId].byIntelliAlgSupport) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamIntelSceneChg.atLcamCapSceneChg[wChnId].byIntelliAlgSupport) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamIntelVirtual.atLcamCapVirtual[wChnId].byIntelliAlgSupport) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamMcInfo.atOverLayCap[wChnId].bSupIntelliAlg) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamMcInfo.atMDCap[wChnId].bSupIntelliAlg) ||
       (NVR_CAP_SUPPORT == ptCap->tLcamBaiscIntel.tLcamIntelFaceDetect.atLcamCapFaceDetectInfo[wChnId].byIntelliAlgSupport))
    {
        bRet =  TRUE;
    }

    return bRet;
}

static NVRSTATUS NvrFixProfeIntelAlgInit()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrCapLcam tCapLcamInfo;
    TBasicIntelliAlgCtrlInitPrm tAlgInitParam;
    s32 i = 0;

    mzero(tCapLcamInfo);
    mzero(tAlgInitParam);

    eRet = NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcamInfo);
    if (NVR_ERR__OK != eRet)
    {
        PROFEINTELMEMNOTICE("get lcam capbility failed ret:%d\n", eRet);
        return eRet;
    }

    for(i = 0; i < tCapLcamInfo.tLcamMcInfo.byLcamChnNum; i++)
    {
        if (NvrFixBasicIntelliAdvSupport(i, &tCapLcamInfo))
        {
            tAlgInitParam.au8SupIntelliAlg[i] = NVR_CAP_SUPPORT;
        }
    }

    eRet = __BasicIntelliAlgCtrlInit(&tAlgInitParam);

    return eRet;
}

///< 目标参数转换
static EBasicIntelliAlgObjectType __BasicIntelliConvertObjectParam(TNvrBasicIntelliObjParam tObjectParam)
{
    EBasicIntelliAlgObjectType eObjType = 0;

    if(tObjectParam.bObjPed == TRUE)
    {
        eObjType |= BASICINTELLI_OBJTYPE_PED;
    }
    if(tObjectParam.bObjVeh == TRUE)
    {
        eObjType |= BASICINTELLI_OBJTYPE_VEH;
    }
    if(tObjectParam.bObjNonMotor == TRUE)
    {
        eObjType |= BASICINTELLI_OBJTYPE_NONMOTOR;
    }
    if(tObjectParam.bObjShip == TRUE)
    {
        eObjType |= BASICINTELLI_OBJTYPE_SHIP;
    }

    BASICINTELMEMNOTICE("%s eObjType:%u\n",__FUNCTION__, eObjType);

    return eObjType;
}

NVRSTATUS NvrFixProfeIntelAlgSetParam(const u16 wChnId, EBasicIntelliAlarmType eType, void *pParamIn, void *pContent)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    s32 nRet = 0;
    u32 i = 0, j = 0;

    PROFEINTEL_ASSERT(pParamIn);
    PROFEINTEL_ASSERT(pContent);

    switch (eType)
    {
        case BASICINTELLI_ADVANCED_FIREALARM:
        {
            s32 nPresetId = 0;
            TNvrIntelSmokeFireCfg *ptSmokeFireParam = (TNvrIntelSmokeFireCfg *)pParamIn;
            TBasicIntelliFireAlarmParam tBasicIntelliParam;

            memcpy(&nPresetId, pContent, sizeof(nPresetId));
            PROFEINTELDBG("BASICINTELLI_ADVANCED_FIREALARM wChnId:%d nPresetId:%d(%p)(%x) enable:%d\n", wChnId, nPresetId, pContent, nPresetId, ptSmokeFireParam->bEnable);
            if (nPresetId <= NVR_PUI_PRESET_MAX_NUM && nPresetId >= 0)
            {
                tBasicIntelliParam.bEnable = ptSmokeFireParam->bEnable;
                tBasicIntelliParam.u32Sensitivity = ptSmokeFireParam->tOtherInfo.wSensity;
                tBasicIntelliParam.tFilterInfo.u32HeightMin = ptSmokeFireParam->tOtherInfo.wHight;
                tBasicIntelliParam.tFilterInfo.u32WidthMin = ptSmokeFireParam->tOtherInfo.wWidth;

                tBasicIntelliParam.u32RegionNum = ptSmokeFireParam->atPreset[nPresetId].wCheckRegion;
                for (i = 0; i < BASICINTELLI_MAX_FIREALARM_REGION_NUM; i++)
                {
                    if (i <  tBasicIntelliParam.u32RegionNum)
                    {
                        tBasicIntelliParam.atFireAlarmInfo[i].u32PointNum = ptSmokeFireParam->atPreset[nPresetId].tCheckRegion[i].dwPointNum;
                        for (j = 0; j <  tBasicIntelliParam.atFireAlarmInfo[i].u32PointNum; j++)
                        {
                            tBasicIntelliParam.atFireAlarmInfo[i].atPoint[j].u16PosX = ptSmokeFireParam->atPreset[nPresetId].tCheckRegion[i].atPolygonPoint[j].wPosX;
                            tBasicIntelliParam.atFireAlarmInfo[i].atPoint[j].u16PosY = ptSmokeFireParam->atPreset[nPresetId].tCheckRegion[i].atPolygonPoint[j].wPosY;
                        }
                    }
                    else
                    {
                        ///< 未设置，坐标点数需要设置为0，防止异常数值传入
                        tBasicIntelliParam.atFireAlarmInfo[i].u32PointNum = 0;
                    }

                }
                tBasicIntelliParam.u32ShieldRegionNum = ptSmokeFireParam->atPreset[nPresetId].wShieldRegion;
                for (i = 0; i < BASICINTELLI_MAX_FIREALARM_REGION_NUM; i++)
                {
                    if (i <  tBasicIntelliParam.u32ShieldRegionNum)
                    {
                        tBasicIntelliParam.atShieldFireAlarmInfo[i].u32PointNum = ptSmokeFireParam->atPreset[nPresetId].tShieldRegion[i].dwPointNum;
                        for (j = 0; j <  tBasicIntelliParam.atShieldFireAlarmInfo[i].u32PointNum; j++)
                        {
                            tBasicIntelliParam.atShieldFireAlarmInfo[i].atPoint[j].u16PosX = ptSmokeFireParam->atPreset[nPresetId].tShieldRegion[i].atPolygonPoint[j].wPosX;
                            tBasicIntelliParam.atShieldFireAlarmInfo[i].atPoint[j].u16PosY = ptSmokeFireParam->atPreset[nPresetId].tShieldRegion[i].atPolygonPoint[j].wPosY;
                        }
                    }
                    else
                    {
                        ///< 未设置，坐标点数需要设置为0，防止异常数值传入
                        tBasicIntelliParam.atShieldFireAlarmInfo[i].u32PointNum = 0;
                    }
                }

                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_FIREALARM, &tBasicIntelliParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set BASICINTELLI_ADVANCED_FIREALARM failed ret %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("open  BASICINTELLI_ADVANCED_FIREALARM nRet:%d\n",nRet);
                }
            }
            else
            {
                PROFEINTELMEMNOTICE("set  BASICINTELLI_ADVANCED_FIREALARM failed nPresetId err %d\n", nPresetId);
            }
        }
        break;

        default:
        {
            PROFEINTELMEMNOTICE("no such basic intel type %d!\n", eType);
        }
        break;
    }

    return eRet;
}

NVRSTATUS NvrFixProfeIntelBasicIntelAlgSetParam(const u16 wChnId,ENvrLcamBasicIntelType eType, TLcamIntelCfg *ptLcamIntelCfg)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    s32 nRet = 0;
    PROFEINTEL_ASSERT(ptLcamIntelCfg);
    u32 byPointId = 0;
    u32 i = 0;
	u32 u32Index = 0;
	u32 u32Count = 0;
	u32 u32CurSel = 0;
    switch (eType)
    {
        case NVR_INTEL_TYPE_MD:
        {
            TBasicIntelliMdParam tBasicIntelliMDParam;
            mzero(tBasicIntelliMDParam);

            if(TRUE == ptLcamIntelCfg->atMDParam[wChnId].bEnable)
            {
                tBasicIntelliMDParam.u32RegionNum = ptLcamIntelCfg->atMDParam[wChnId].dwRegionNum;

                if(ptLcamIntelCfg->atMDParam[wChnId].dwRegionNum != 0)//区域数非零
                {
                    for(i = 0;i < NVR_MAX_MD_REGION_NUM;i++)
                    {
                        ///<灵敏度 需从1-7转换成0~100
                        tBasicIntelliMDParam.atMdInfo[i].u32Sensitivity = ptLcamIntelCfg->atMDParam[wChnId].dwSensitivity*14;
                        if(7 == ptLcamIntelCfg->atMDParam[wChnId].dwSensitivity)
                        {
                            tBasicIntelliMDParam.atMdInfo[i].u32Sensitivity = 100;
                        }

                        ///<赋值给底层x-y-width-height
                        tBasicIntelliMDParam.atMdInfo[i].tMdRegion.u16StartX = ptLcamIntelCfg->atMDParam[wChnId].atZoneInfo[i].wStartX;
                        tBasicIntelliMDParam.atMdInfo[i].tMdRegion.u16StartY = ptLcamIntelCfg->atMDParam[wChnId].atZoneInfo[i].wStartY;
                        tBasicIntelliMDParam.atMdInfo[i].tMdRegion.u16Width  = ptLcamIntelCfg->atMDParam[wChnId].atZoneInfo[i].wWidth;
                        tBasicIntelliMDParam.atMdInfo[i].tMdRegion.u16Height = ptLcamIntelCfg->atMDParam[wChnId].atZoneInfo[i].wHeight;

                        tBasicIntelliMDParam.atMdInfo[i].eObjType = __BasicIntelliConvertObjectParam(ptLcamIntelCfg->atMDParam[wChnId].tObjectParam);

                        PROFEINTELDBG("set MD param chnid:%d [%d] Sensitivity:%lu  StartX:%u StartY:%u Width:%u Height:%u dwRegionNum:%lu \n",
                            wChnId,
                            i,
                            tBasicIntelliMDParam.atMdInfo[i].u32Sensitivity,
                            tBasicIntelliMDParam.atMdInfo[i].tMdRegion.u16StartX,
                            tBasicIntelliMDParam.atMdInfo[i].tMdRegion.u16StartY,
                            tBasicIntelliMDParam.atMdInfo[i].tMdRegion.u16Width,
                            tBasicIntelliMDParam.atMdInfo[i].tMdRegion.u16Height,
                            tBasicIntelliMDParam.u32RegionNum);
                    }

                    nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_MOTION, &tBasicIntelliMDParam);
                    if (0 != nRet)
                    {
                        PROFEINTELMEMNOTICE("set __BasicIntellilSetAlarmDetectParam MD failed ret %d\n", nRet);
                        return NVR_ERR__ERROR;
                    }
                    else
                    {
                        PROFEINTELDBG("md open __BasicIntellilSetAlarmDetectParam nRet:%d\n",nRet);
                    }
                }
                else
                {
                    nRet = __BasicIntellilSetAlarmDetectParam(wChnId,BASICINTELLI_ADVANCED_MOTION,&tBasicIntelliMDParam);
                    if (0 != nRet)
                    {
                        PROFEINTELMEMNOTICE("set __BasicIntellilSetAlarmDetectParam MD failed ret %d\n", nRet);
                        return NVR_ERR__ERROR;
                    }
                    else
                    {
                        PROFEINTELDBG("md open chn = %d __BasicIntellilSetAlarmDetectParam eret:%d\n", wChnId,nRet);
                    }
                }
            }
            else
            {
                PROFEINTELDBG("chn:%d md not enable!\n", wChnId);
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId,BASICINTELLI_ADVANCED_MOTION,&tBasicIntelliMDParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set mediactrl MD failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("md close __BasicIntellilSetAlarmDetectParam nRet:%d\n",nRet);
                }
            }

        }
        break;

        case NVR_INTEL_TYPE_CORDON:
        {
            TBasicIntelliTripLineParam tBasicIntelliTripLineParam;
            mzero(tBasicIntelliTripLineParam);

            if(TRUE !=ptLcamIntelCfg->atCordon[wChnId].bEnable)
            {
                PROFEINTELDBG("chn:%d cordon not enable!\n", wChnId);
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_TRIP_LINE, &tBasicIntelliTripLineParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set type cordon failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("set type cordon!!!!!!!!!!!!!!nRet:%d\n",nRet);
                }
            }
            else
            {
            	u32Count = 0;
				u32CurSel = ptLcamIntelCfg->atCordon[wChnId].dwCurSel;
                tBasicIntelliTripLineParam.u32Sensitivity = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].dwSensitity;
                
				for(u32Index = 0; u32Index < NVR_MAX_CORDON_NUM; u32Index++)
				{
					///<如果没有绘制线，不赋值
					if(ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tStartPoint.wPosX ==0
						&& ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tStartPoint.wPosY == 0
						&& ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tEndPoint.wPosX == 0
						&& ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tEndPoint.wPosY == 0)
					{
						continue;
					}
	                if(NVR_CAP_TRIP_DIR_A_TO_B == ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].eCordonType)
	                {
	                    tBasicIntelliTripLineParam.atTripLineInfo[u32Count].eTripLineType = MEDIACTRL_TRIP_DIR_A_TO_B;
	                }
	                else if(NVR_CAP_TRIP_DIR_B_TO_A == ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].eCordonType)
	                {
	                    tBasicIntelliTripLineParam.atTripLineInfo[u32Count].eTripLineType = MEDIACTRL_TRIP_DIR_B_TO_A;
	                }
	                else
	                {
	                    tBasicIntelliTripLineParam.atTripLineInfo[u32Count].eTripLineType = MEDIACTRL_TRIP_DIR_A_AND_B;
	                }
					
	                tBasicIntelliTripLineParam.atTripLineInfo[u32Count].tStartPoint.u16PosX = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tStartPoint.wPosX;
	                tBasicIntelliTripLineParam.atTripLineInfo[u32Count].tStartPoint.u16PosY = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tStartPoint.wPosY;
	                tBasicIntelliTripLineParam.atTripLineInfo[u32Count].tEndPoint.u16PosX = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tEndPoint.wPosX;
	                tBasicIntelliTripLineParam.atTripLineInfo[u32Count].tEndPoint.u16PosY = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tEndPoint.wPosY;
	                tBasicIntelliTripLineParam.atTripLineInfo[u32Count].tRefAPoint.u16PosX = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tRefAPoint.wPosX;
	                tBasicIntelliTripLineParam.atTripLineInfo[u32Count].tRefAPoint.u16PosY = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tRefAPoint.wPosY;
	                tBasicIntelliTripLineParam.atTripLineInfo[u32Count].tRefBPoint.u16PosX = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tRefBPoint.wPosX;
	                tBasicIntelliTripLineParam.atTripLineInfo[u32Count].tRefBPoint.u16PosY = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].atNvrIntelCordon[u32Index].tRefBPoint.wPosY;
	                tBasicIntelliTripLineParam.atTripLineInfo[u32Count].eObjType = __BasicIntelliConvertObjectParam(ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[u32CurSel].tObjectParam);
					u32Count ++;

	                PROFEINTELDBG("cordon sen:%d,linenum:%d,triplnetype:%d,startX:%d,startY:%d,endX:%d,endY:%d,refAX:%d,refAY:%d,refBX:%d,refBY:%d,filterWidthMin:%d,filterHeightMin:%d,filterWidthMax:%d,filterHeightMax:%d\n",
						   tBasicIntelliTripLineParam.u32Sensitivity,
						   tBasicIntelliTripLineParam.u32LineNum,
						   tBasicIntelliTripLineParam.atTripLineInfo[u32Index].eTripLineType,
						   tBasicIntelliTripLineParam.atTripLineInfo[u32Index].tStartPoint.u16PosX,
						   tBasicIntelliTripLineParam.atTripLineInfo[u32Index].tStartPoint.u16PosY,
						   tBasicIntelliTripLineParam.atTripLineInfo[u32Index].tEndPoint.u16PosX,
						   tBasicIntelliTripLineParam.atTripLineInfo[u32Index].tEndPoint.u16PosY,
						   tBasicIntelliTripLineParam.atTripLineInfo[u32Index].tRefAPoint.u16PosX,
						   tBasicIntelliTripLineParam.atTripLineInfo[u32Index].tRefAPoint.u16PosY,
						   tBasicIntelliTripLineParam.atTripLineInfo[u32Index].tRefBPoint.u16PosX,
						   tBasicIntelliTripLineParam.atTripLineInfo[u32Index].tRefBPoint.u16PosY,
						   tBasicIntelliTripLineParam.tFilterInfo.u32WidthMin,
						   tBasicIntelliTripLineParam.tFilterInfo.u32HeightMin,
						   tBasicIntelliTripLineParam.tFilterInfo.u32WidthMax,
						   tBasicIntelliTripLineParam.tFilterInfo.u32HeightMax
						   );
				}
				tBasicIntelliTripLineParam.u32LineNum = u32Count;	
        		tBasicIntelliTripLineParam.tFilterInfo.u32WidthMin = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[ptLcamIntelCfg->atCordon[wChnId].dwCurSel].tMaxFilterSize.wWidth*ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[ptLcamIntelCfg->atCordon[wChnId].dwCurSel].dwFilterScale/100;
	            tBasicIntelliTripLineParam.tFilterInfo.u32HeightMin = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[ptLcamIntelCfg->atCordon[wChnId].dwCurSel].tMaxFilterSize.wHeight*ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[ptLcamIntelCfg->atCordon[wChnId].dwCurSel].dwFilterScale/100;
	            tBasicIntelliTripLineParam.tFilterInfo.u32WidthMax = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[ptLcamIntelCfg->atCordon[wChnId].dwCurSel].tMaxFilterSize.wWidth;
	            tBasicIntelliTripLineParam.tFilterInfo.u32HeightMax = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[ptLcamIntelCfg->atCordon[wChnId].dwCurSel].tMaxFilterSize.wHeight;
				tBasicIntelliTripLineParam.eDetectMode = ptLcamIntelCfg->atCordon[wChnId].atCardonInfo[ptLcamIntelCfg->atCordon[wChnId].dwCurSel].tObjectParam.eDetectMode;

				PROFEINTELDBG("TRIP_LINE LineNum %d\n",u32Count );	
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_TRIP_LINE, &tBasicIntelliTripLineParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set type cordon failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("set type cordon!!!!!!!!!!!!!!nRet:%d\n",nRet);
                }
            }
        }
        break;

        case NVR_INTEL_TYPE_AREA_INVASION:
        {
            TBasicIntelliRegionInvasionParam tBasicIntelliInvasionParam;
            mzero(tBasicIntelliInvasionParam);

            if(TRUE !=ptLcamIntelCfg->atAreaInvasion[wChnId].bEnable)
            {
                PROFEINTELDBG("chn:%d area invasion not enable!\n", wChnId);
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_REGION_INVASION, &tBasicIntelliInvasionParam);
                if (0 != eRet)
                {
                    PROFEINTELMEMNOTICE("set area invasion failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("set area invasion!!!!!!!!!!!!!!nRet:%d\n",nRet);
                }
            }
            else
            {
            	u32Count = 0;
            	for(u32Index = 0; u32Index < BASICINTELLI_MAX_INVASION_REGION_NUM; u32Index ++)
            	{
                	///<则直接关闭区域入侵
	                if(0 != ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum)
	                {
                    	tBasicIntelliInvasionParam.atInvasionInfo[u32Count].u32PointNum = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum;
	                    for(byPointId = 0; byPointId < ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum; byPointId ++)
	                    {
	                        tBasicIntelliInvasionParam.atInvasionInfo[u32Count].atPoint[byPointId].u16PosX = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosX;
	                        tBasicIntelliInvasionParam.atInvasionInfo[u32Count].atPoint[byPointId].u16PosY = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosY;
	                        tBasicIntelliInvasionParam.atInvasionInfo[u32Count].eObjType = __BasicIntelliConvertObjectParam(ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[u32Index].tObjectParam);
	                    }

	                    PROFEINTELDBG("area invasion dwTimeThreshold:%d,dwSensitivity:%d,dwPointNum:%d\n",
	                    ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[u32Index].dwTimeThreshold,
	                    ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[u32Index].dwSensitivity,
	                    ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum);
						u32Count++;

                	}
            	}
				if(u32Count == 0)
				{
				 	PROFEINTELMEMNOTICE("set area invasion failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
				}
				tBasicIntelliInvasionParam.u32RegionNum = u32Count;
				tBasicIntelliInvasionParam.u8TrigTimeThr = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaInvasion[wChnId].dwCurSel].dwTimeThreshold;
	            tBasicIntelliInvasionParam.u32Sensitivity = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaInvasion[wChnId].dwCurSel].dwSensitivity;
	            
				tBasicIntelliInvasionParam.tFilterInfo.u32WidthMin = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaInvasion[wChnId].dwCurSel].tMaxFilterSize.wWidth*ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaInvasion[wChnId].dwCurSel].dwFilterSale/100;
	            tBasicIntelliInvasionParam.tFilterInfo.u32HeightMin = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaInvasion[wChnId].dwCurSel].tMaxFilterSize.wHeight*ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaInvasion[wChnId].dwCurSel].dwFilterSale/100;
	            tBasicIntelliInvasionParam.tFilterInfo.u32WidthMax = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaInvasion[wChnId].dwCurSel].tMaxFilterSize.wWidth;
	            tBasicIntelliInvasionParam.tFilterInfo.u32HeightMax = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaInvasion[wChnId].dwCurSel].tMaxFilterSize.wHeight;
				tBasicIntelliInvasionParam.eDetectMode = ptLcamIntelCfg->atAreaInvasion[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaInvasion[wChnId].dwCurSel].tObjectParam.eDetectMode;

	            nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_REGION_INVASION, &tBasicIntelliInvasionParam);
	            if (0 != nRet)
	            {
	                PROFEINTELMEMNOTICE("set area invasion failed nRet %d\n", nRet);
	                return NVR_ERR__ERROR;
	            }
	            else
	            {
	                 PROFEINTELDBG("set area invasion!!!!!!!!!!!!!!nRet:%d\n",nRet);
	            }
			}

        }
        break;

        case NVR_INTEL_TYPE_AREA_LEAVE:
        {
            TBasicIntelliRegionLeaveParam tBasicIntelliLeaveParam;
            mzero(tBasicIntelliLeaveParam);

            if(TRUE != ptLcamIntelCfg->atAreaLeave[wChnId].bEnable)
            {
                PROFEINTELDBG("chn:%d area leave not enable!\n", wChnId);
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_REGION_LEAVE, &tBasicIntelliLeaveParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set area leave failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("set area leave!!!!!!!!!!!!!!nRet:%d\n",nRet);
                }
            }
            else
            {
            	u32Count = 0;
            	for(u32Index = 0; u32Index < BASICINTELLI_MAX_LEAVE_REGION_NUM; u32Index ++)
            	{
	                ///<没有绘制区域，则直接关闭区域离开
	                if(0 != ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].tRegion.dwPointNum)
	                {
	                    tBasicIntelliLeaveParam.atLeaveInfo[u32Count].u32PointNum = ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum;
	                    for(byPointId = 0; byPointId < ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].tRegion.dwPointNum; byPointId ++)
	                    {
	                        tBasicIntelliLeaveParam.atLeaveInfo[u32Count].atPoint[byPointId].u16PosX = ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosX;
	                        tBasicIntelliLeaveParam.atLeaveInfo[u32Count].atPoint[byPointId].u16PosY = ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosY;
	                        tBasicIntelliLeaveParam.atLeaveInfo[u32Count].eObjType = __BasicIntelliConvertObjectParam(ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[u32Index].tObjectParam);
	                    }
						u32Count++;

	                    PROFEINTELDBG("area leave dwSensitivity:%d,dwPointNum:%d\n",
	                    ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[u32Index].dwSensitivity,
	                    ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum);

	                }
	            }
				if(u32Count == 0)
				{
				 	PROFEINTELMEMNOTICE("set area leave failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
				}
				tBasicIntelliLeaveParam.u32RegionNum = u32Count;
				tBasicIntelliLeaveParam.u32Sensitivity = ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].dwSensitivity;
	            
				tBasicIntelliLeaveParam.tFilterInfo.u32WidthMin = ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].tMaxFilterSize.wWidth*ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].dwFilterSale/100;
	            tBasicIntelliLeaveParam.tFilterInfo.u32HeightMin = ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].tMaxFilterSize.wHeight*ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].dwFilterSale/100;
	            tBasicIntelliLeaveParam.tFilterInfo.u32WidthMax = ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].tMaxFilterSize.wWidth;
	            tBasicIntelliLeaveParam.tFilterInfo.u32HeightMax = ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].tMaxFilterSize.wHeight;
				tBasicIntelliLeaveParam.eDetectMode = ptLcamIntelCfg->atAreaLeave[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaLeave[wChnId].dwCurSel].tObjectParam.eDetectMode;

	            nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_REGION_LEAVE, &tBasicIntelliLeaveParam);
	            if (0 != nRet)
	            {
	            	PROFEINTELMEMNOTICE("set area leave failed nRet %d\n", nRet);
	                return NVR_ERR__ERROR;
	            }
	            else
	            {
	              PROFEINTELDBG("set area leave!!!!!!!!!!!!!!nRet:%d\n",nRet);
	             
            	}
            }
        }
        break;

        case NVR_INTEL_TYPE_AREA_ENTER:
        {
            TBasicIntelliRegionEnterParam tBasicIntelliEnterParam;
            mzero(tBasicIntelliEnterParam);

            if(TRUE != ptLcamIntelCfg->atAreaEnter[wChnId].bEnable)
            {
                PROFEINTELDBG("chn:%d area enter not enable!\n", wChnId);
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_REGION_ENTER, &tBasicIntelliEnterParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set area enter failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("set area enter!!!!!!!!!!!!!!nRet:%d\n",nRet);
                }
            }
            else
            {
            	u32Count = 0;
            	for(u32Index = 0; u32Index < BASICINTELLI_MAX_ENTER_REGION_NUM; u32Index ++)
            	{
                	///<没有绘制区域，则直接关闭区域进入
	                if(0 != ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaEnter[wChnId].dwCurSel].tRegion.dwPointNum)
	                {
	                    tBasicIntelliEnterParam.atEnterInfo[u32Count].u32PointNum = ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum;
	                    for(byPointId = 0; byPointId < ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum; byPointId ++)
	                    {
	                        tBasicIntelliEnterParam.atEnterInfo[u32Count].atPoint[byPointId].u16PosX = ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosX;
	                        tBasicIntelliEnterParam.atEnterInfo[u32Count].atPoint[byPointId].u16PosY = ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosY;
	                        tBasicIntelliEnterParam.atEnterInfo[u32Count].eObjType = __BasicIntelliConvertObjectParam(ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[u32Index].tObjectParam);
	                    }

	                    PROFEINTELDBG("area enter ,dwSensitivity:%d,dwPointNum:%d\n",
	                    ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[u32Index].dwSensitivity,
	                    ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum);
						u32Count++;

                	}
                }
				if(u32Count == 0)
				{
				 	PROFEINTELMEMNOTICE("set area enter failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
				}
				tBasicIntelliEnterParam.u32RegionNum = u32Count;
				tBasicIntelliEnterParam.u32Sensitivity = ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaEnter[wChnId].dwCurSel].dwSensitivity;
                
                tBasicIntelliEnterParam.tFilterInfo.u32WidthMin = ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaEnter[wChnId].dwCurSel].tMaxFilterSize.wWidth*ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaEnter[wChnId].dwCurSel].dwFilterSale/100;
                tBasicIntelliEnterParam.tFilterInfo.u32HeightMin = ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaEnter[wChnId].dwCurSel].tMaxFilterSize.wHeight*ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaEnter[wChnId].dwCurSel].dwFilterSale/100;
                tBasicIntelliEnterParam.tFilterInfo.u32WidthMax = ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaEnter[wChnId].dwCurSel].tMaxFilterSize.wWidth;
                tBasicIntelliEnterParam.tFilterInfo.u32HeightMax = ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaEnter[wChnId].dwCurSel].tMaxFilterSize.wHeight;
				tBasicIntelliEnterParam.eDetectMode = ptLcamIntelCfg->atAreaEnter[wChnId].atRegionInfo[ptLcamIntelCfg->atAreaEnter[wChnId].dwCurSel].tObjectParam.eDetectMode;
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_REGION_ENTER, &tBasicIntelliEnterParam);
                if (0 != nRet)
                {
                     PROFEINTELMEMNOTICE("set area enter failed nRet %d\n", nRet);
                     return NVR_ERR__ERROR;
                }
                else
                {
                   PROFEINTELDBG("set area enter!!!!!!!!!!!!!!nRet:%d\n",nRet);
                }
                    
            }
        }
        break;

        case NVR_INEL_TYPE_PEOPLE_GATHER:
        {
            TBasicIntelliGatherParam tBasicIntelliGatherParam;
            mzero(tBasicIntelliGatherParam);

            ///< 关闭人员聚集检测
            if(TRUE != ptLcamIntelCfg->atPeopleGather[wChnId].bEnable)
            {
                PROFEINTELDBG("chn:%d people gather not enable!\n", wChnId);
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_GATHER_DET, &tBasicIntelliGatherParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set people gather failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("set people gather!!!!!!!!!!!!!!nRet:%d\n",nRet);
                }
            }
            ///< 开启人员聚集检测
            else
            {
                // 没有绘制区域，关闭人员聚集检测
                if(0 == ptLcamIntelCfg->atPeopleGather[wChnId].atRegionInfo[ptLcamIntelCfg->atPeopleGather[wChnId].dwCurSel].tRegion.dwPointNum)
                {
                    PROFEINTELDBG("people gather dwPointNum = 0!\n");

                    nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_GATHER_DET, &tBasicIntelliGatherParam);
                    if (0 != nRet)
                    {
                        PROFEINTELMEMNOTICE("set people gather failed nRet %d\n", nRet);
                        return NVR_ERR__ERROR;
                    }
                    else
                    {
                        PROFEINTELDBG("set people gather!!!!!!!!!!!!!!nRet:%d\n",nRet);
                    }
                }
                else
                {
                    tBasicIntelliGatherParam.u32RegionNum = 1; // 尽管web界面可设置多个区域，但只设置一个区域生效
                    tBasicIntelliGatherParam.atGatherInfo[0].u32Prop = ptLcamIntelCfg->atPeopleGather[wChnId].atRegionInfo[ptLcamIntelCfg->atPeopleGather[wChnId].dwCurSel].dwAccounting;
                    tBasicIntelliGatherParam.atGatherInfo[0].u32PointNum = ptLcamIntelCfg->atPeopleGather[wChnId].atRegionInfo[ptLcamIntelCfg->atPeopleGather[wChnId].dwCurSel].tRegion.dwPointNum; // 区域顶点数
                    for(byPointId = 0; byPointId < tBasicIntelliGatherParam.atGatherInfo[0].u32PointNum; byPointId ++)
                    {
                        tBasicIntelliGatherParam.atGatherInfo[0].atPoint[byPointId].u16PosX = ptLcamIntelCfg->atPeopleGather[wChnId].atRegionInfo[ptLcamIntelCfg->atPeopleGather[wChnId].dwCurSel].tRegion.atPolygonPoint[byPointId].wPosX;
                        tBasicIntelliGatherParam.atGatherInfo[0].atPoint[byPointId].u16PosY = ptLcamIntelCfg->atPeopleGather[wChnId].atRegionInfo[ptLcamIntelCfg->atPeopleGather[wChnId].dwCurSel].tRegion.atPolygonPoint[byPointId].wPosY;
                        tBasicIntelliGatherParam.atGatherInfo[0].eObjType = __BasicIntelliConvertObjectParam(ptLcamIntelCfg->atPeopleGather[wChnId].atRegionInfo[ptLcamIntelCfg->atPeopleGather[wChnId].dwCurSel].tObjectParam);
                    }
                    PROFEINTELDBG("people gather Accounting:%d,dwPointNum:%d\n",
                    ptLcamIntelCfg->atPeopleGather[wChnId].atRegionInfo[ptLcamIntelCfg->atPeopleGather[wChnId].dwCurSel].dwAccounting,
                    ptLcamIntelCfg->atPeopleGather[wChnId].atRegionInfo[ptLcamIntelCfg->atPeopleGather[wChnId].dwCurSel].tRegion.dwPointNum);

                    nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_GATHER_DET, &tBasicIntelliGatherParam);
                    if (0 != nRet)
                    {
                        PROFEINTELMEMNOTICE("set people gather failed nRet %d\n", nRet);
                        return NVR_ERR__ERROR;
                    }
                    else
                    {
                        PROFEINTELDBG("set people gather!!!!!!!!!!!!!!nRet:%d\n",nRet);
                    }
                }
            }
        }
        break;

        case NVR_INTEL_TYPE_OBJECT_PICK:
        {
            TBasicIntelliRemoveObjectParam tBIRemoveObjParam;
            mzero(tBIRemoveObjParam);
            if (TRUE == ptLcamIntelCfg->atObjectPick[wChnId].bEnable)
            {
                // web设置的结构体转换成baiscintelli结构体
                u32Count = 0;
                for (u32Index = 0; u32Index < BASICINTELLI_MAX_OBJECT_REGION_NUM; u32Index++)
                {
                    if (ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum)
                    {
                        tBIRemoveObjParam.atRemoveObjInfo[u32Index].u32PointNum = ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum;
                        for (byPointId = 0; byPointId < ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum; byPointId++)
                        {
                            tBIRemoveObjParam.atRemoveObjInfo[u32Count].atPoint[byPointId].u16PosX = ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosX;
                            tBIRemoveObjParam.atRemoveObjInfo[u32Count].atPoint[byPointId].u16PosY = ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosY;
                        }

                        u32Count++;
                    }
                }

                tBIRemoveObjParam.u32RegionNum = u32Count;
                tBIRemoveObjParam.u32Sensitivity = ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectPick[wChnId].dwCurSel].dwSensitivity;
                tBIRemoveObjParam.dwTimeThreshold = ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectPick[wChnId].dwCurSel].dwTimeThreshold;

                tBIRemoveObjParam.tFilterInfo.u32WidthMin = ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectPick[wChnId].dwCurSel].tMaxFilterSize.wWidth * ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectPick[wChnId].dwCurSel].dwFilterSale / 100;
                tBIRemoveObjParam.tFilterInfo.u32HeightMin = ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectPick[wChnId].dwCurSel].tMaxFilterSize.wHeight * ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectPick[wChnId].dwCurSel].dwFilterSale / 100;
                tBIRemoveObjParam.tFilterInfo.u32WidthMax = ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectPick[wChnId].dwCurSel].tMaxFilterSize.wWidth;
                tBIRemoveObjParam.tFilterInfo.u32HeightMax = ptLcamIntelCfg->atObjectPick[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectPick[wChnId].dwCurSel].tMaxFilterSize.wHeight;

                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_REMOVEOBJECT, &tBIRemoveObjParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set obejct pick failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("chn:%d set obejct pick!!!!!!!!!!!!!!nRet:%d\n", wChnId, nRet);
                }
            }
            else
            {
                PROFEINTELDBG("chn:%d pick not enable!\n", wChnId);
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_REMOVEOBJECT, &tBIRemoveObjParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set mediactrl pick failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("pick close __BasicIntellilSetAlarmDetectParam nRet:%d\n", nRet);
                }
            }

            break;
        }

        case NVR_INTEL_TYPE_OBJECT_LEFT:
        {
            TBasicIntelliAbandonObjectParam tBIAbandonObjParam;
            mzero(tBIAbandonObjParam);
            if (TRUE == ptLcamIntelCfg->atObjectLeft[wChnId].bEnable)
            {
                // web设置的结构体转换成baiscintelli结构体
                u32Count = 0;
                for (u32Index = 0; u32Index < BASICINTELLI_MAX_OBJECT_REGION_NUM; u32Index++)
                {
                    if (ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum)
                    {
                        tBIAbandonObjParam.atAbandomObjInfo[u32Index].u32PointNum = ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum;
                        for (byPointId = 0; byPointId < ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[u32Index].tRegion.dwPointNum; byPointId++)
                        {
                            tBIAbandonObjParam.atAbandomObjInfo[u32Count].atPoint[byPointId].u16PosX = ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosX;
                            tBIAbandonObjParam.atAbandomObjInfo[u32Count].atPoint[byPointId].u16PosY = ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[u32Index].tRegion.atPolygonPoint[byPointId].wPosY;
                        }

                        u32Count++;
                    }
                }

                tBIAbandonObjParam.u32RegionNum = u32Count;
                tBIAbandonObjParam.u32Sensitivity = ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectLeft[wChnId].dwCurSel].dwSensitivity;
                tBIAbandonObjParam.dwTimeThreshold = ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectLeft[wChnId].dwCurSel].dwTimeThreshold;

                tBIAbandonObjParam.tFilterInfo.u32WidthMin = ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectLeft[wChnId].dwCurSel].tMaxFilterSize.wWidth * ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectLeft[wChnId].dwCurSel].dwFilterSale / 100;
                tBIAbandonObjParam.tFilterInfo.u32HeightMin = ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectLeft[wChnId].dwCurSel].tMaxFilterSize.wHeight * ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectLeft[wChnId].dwCurSel].dwFilterSale / 100;
                tBIAbandonObjParam.tFilterInfo.u32WidthMax = ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectLeft[wChnId].dwCurSel].tMaxFilterSize.wWidth;
                tBIAbandonObjParam.tFilterInfo.u32HeightMax = ptLcamIntelCfg->atObjectLeft[wChnId].atRegionInfo[ptLcamIntelCfg->atObjectLeft[wChnId].dwCurSel].tMaxFilterSize.wHeight;

                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_ABANDONOBJECT, &tBIAbandonObjParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set obejct left failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("chn:%d set obejct left!!!!!!!!!!!!!!nRet:%d\n", wChnId, nRet);
                }
            }
            else
            {
                PROFEINTELDBG("chn:%d left not enable!\n", wChnId);
                nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_ABANDONOBJECT, &tBIAbandonObjParam);
                if (0 != nRet)
                {
                    PROFEINTELMEMNOTICE("set mediactrl left failed nRet %d\n", nRet);
                    return NVR_ERR__ERROR;
                }
                else
                {
                    PROFEINTELDBG("left close __BasicIntellilSetAlarmDetectParam nRet:%d\n", nRet);
                }
            }

            break;
        }

        case NVR_INTEL_TYPE_VIRTUAL_FOCUS:
        {
            TBasicIntelliDefocusParam tBIDefocusParam;

            tBIDefocusParam.bEnable = ptLcamIntelCfg->atDeFocus[wChnId].tDeFocus.bEnable;
            tBIDefocusParam.u32Sensitivity = ptLcamIntelCfg->atDeFocus[wChnId].tDeFocus.dwSensitivity;

            nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_DEFOCUS, &tBIDefocusParam);
            if (0 != nRet)
            {
                PROFEINTELMEMNOTICE("set VIRTUAL_FOCUS failed nRet %d\n", nRet);
                return NVR_ERR__ERROR;
            }
            else
            {
                PROFEINTELDBG("chn:%d set VIRTUAL_FOCUS!!!!!!!!!!!!!!nRet:%d\n", wChnId, nRet);
            }

            break;
        }
        case NVR_INTEL_TYPE_SCENE_CHANGE:
        {
            TBasicIntelliSceneChangeParam tBISceneChangeParam;

            tBISceneChangeParam.bEnable = ptLcamIntelCfg->atSceneChg[wChnId].tSceneChg.bEnable;
            tBISceneChangeParam.u32Sensitivity = ptLcamIntelCfg->atSceneChg[wChnId].tSceneChg.dwSensitivity;

            nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_SCENECHANGE, &tBISceneChangeParam);
            if (0 != nRet)
            {
                PROFEINTELMEMNOTICE("set SCENE_CHANGE failed nRet %d\n", nRet);
                return NVR_ERR__ERROR;
            }
            else
            {
                PROFEINTELDBG("chn:%d set SCENE_CHANGE!!!!!!!!!!!!!!nRet:%d\n", wChnId, nRet);
            }

            break;
        }
        case NVR_INTEL_TYPE_OVERLAY:
        {
            TBasicIntelliOcclusionParam tBIOcclusionParam;
            mzero(tBIOcclusionParam);
            tBIOcclusionParam.bEnable = ptLcamIntelCfg->atOverlayParam[wChnId].bEnable;
            tBIOcclusionParam.u32Sensitivity = ptLcamIntelCfg->atOverlayParam[wChnId].dwSensitivity;

            tBIOcclusionParam.tODRegion.u16StartX =  ptLcamIntelCfg->atOverlayParam[wChnId].atZoneInfo[0].wStartX;
            tBIOcclusionParam.tODRegion.u16StartY =  ptLcamIntelCfg->atOverlayParam[wChnId].atZoneInfo[0].wStartY;
            tBIOcclusionParam.tODRegion.u16Width =  ptLcamIntelCfg->atOverlayParam[wChnId].atZoneInfo[0].wWidth;
            tBIOcclusionParam.tODRegion.u16Height =  ptLcamIntelCfg->atOverlayParam[wChnId].atZoneInfo[0].wHeight;

            nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_OCCLUSION, &tBIOcclusionParam);
            if (0 != nRet)
            {
                PROFEINTELMEMNOTICE("set OVERLAY failed nRet %d\n", nRet);
                return NVR_ERR__ERROR;
            }
            else
            {
                PROFEINTELDBG("chn:%d set OVERLAY!!!!!!!!!!!!!!nRet:%d\n", wChnId, nRet);
            }

            break;
        }

        case NVR_INTEL_TYPE_FACE_DETECT:
        {
            TBasicIntelliFaceDeteceParam tFaceDetectParam;
            mzero(tFaceDetectParam);
            tFaceDetectParam.bEnable = ptLcamIntelCfg->atFaceDetect[wChnId].bEnable;
            tFaceDetectParam.u32Sensitivity = ptLcamIntelCfg->atFaceDetect[wChnId].dwSensitivity;

            nRet = __BasicIntellilSetAlarmDetectParam(wChnId, BASICINTELLI_ADVANCED_FACEDETECT, &tFaceDetectParam);
            if (0 != nRet)
            {
                PROFEINTELMEMNOTICE("set FaceDetect failed nRet %d\n", nRet);
                return NVR_ERR__ERROR;
            }
            else
            {
                PROFEINTELDBG("chn:%d set FaceDetect!!!!!!!!!!!!!!nRet:%d\n", wChnId, nRet);
            }

            break;
        }
        default:
        {
            PROFEINTELMEMNOTICE("no such basic intel type!\n");
        }
        break;
    }

    return eRet;
}

static NVRSTATUS NvrFixProfeIntelSetAllParam()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrCapLcam tCapLcamInfo;
    TLcamIntelCfg tLcamIntelCfg;
    s32 i = 0, j = 0;;

    mzero(tLcamIntelCfg);
    mzero(tCapLcamInfo);

    eRet = LcamIntelGetParam(&tLcamIntelCfg);
    if (NVR_ERR__OK != eRet)
    {
        PROFEINTELMEMNOTICE("get tLcamIntelCfg failed ret:%d\n", eRet);
        return eRet;
    }

    eRet = NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcamInfo);
    if (NVR_ERR__OK != eRet)
    {
        PROFEINTELMEMNOTICE("get lcam capbility failed ret:%d\n", eRet);
        return eRet;
    }

    for(i = 0; i < tCapLcamInfo.tLcamMcInfo.byLcamChnNum; i++)
    {
        for(j = 0; j < NVR_INTEL_TYPE_MAX; j++)
        {

            if( ((NVR_INTEL_TYPE_CORDON == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelCordon.atLcamCapCordon[i].byIntelliAlgSupport))
                || ((NVR_INTEL_TYPE_AREA_INVASION == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelInvasion.atLcamCapInvasion[i].byIntelliAlgSupport))
                || ((NVR_INTEL_TYPE_AREA_ENTER == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelEnter.atLcamCapEnter[i].byIntelliAlgSupport))
                || ((NVR_INTEL_TYPE_AREA_LEAVE == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelLeave.atLcamCapLeave[i].byIntelliAlgSupport))
                || ((NVR_INEL_TYPE_PEOPLE_GATHER == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamPeopleGather.atLcamPeoleGather[i].byIntelliAlgSupport))
                || ((NVR_INTEL_TYPE_OBJECT_PICK == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelPick.atLcamCapPick[i].byIntelliAlgSupport))
                || ((NVR_INTEL_TYPE_OBJECT_LEFT == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelLeft.atLcamCapLeft[i].byIntelliAlgSupport))
                || ((NVR_INTEL_TYPE_VIRTUAL_FOCUS == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelVirtual.atLcamCapVirtual[i].byIntelliAlgSupport))
                || ((NVR_INTEL_TYPE_SCENE_CHANGE == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelSceneChg.atLcamCapSceneChg[i].byIntelliAlgSupport))
                || ((NVR_INTEL_TYPE_FACE_DETECT == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelFaceDetect.atLcamCapFaceDetectInfo[i].byIntelliAlgSupport))
                || ((NVR_INTEL_TYPE_MD == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamMcInfo.atMDCap[i].bSupIntelliAlg))
                || ((NVR_INTEL_TYPE_OVERLAY == j) && (NVR_CAP_SUPPORT == tCapLcamInfo.tLcamMcInfo.atOverLayCap[i].bSupIntelliAlg)))
            {
                //结构化算法
                eRet = NvrFixProfeIntelBasicIntelAlgSetParam(i, j, &tLcamIntelCfg);
                if(NVR_ERR__OK != eRet)
                {
                    PROFEINTELMEMNOTICE("chn:%d, NvrFixProfeIntelBasicIntelAlgSetParam failed eRet:%d, basic intel:%d\n", i, eRet, j);
                }

                PROFEINTELDBG("chn:%d,NvrFixProfeIntelBasicIntelAlgSetParam :%d\n", i, j);
            }
        }
    }

    return eRet;
}

NVRSTATUS NvrFixProfeIntelInit()
{
    NVRSTATUS eRet = NVR_ERR__ERROR;

    do
    {
        TNvrCapSortwareCapInfo tCapSoftware;

        mzero(tCapSoftware);

        NvrCapGetCapParam(NVR_CAP_ID_SOFTWARE, &tCapSoftware);

        mzero(g_buAlarmDebugPrint);

        ///<初始化结构化算法
        eRet = NvrFixProfeIntelAlgInit();
        if (NVR_ERR__OK != eRet)
        {
            PROFEINTELERR("to init alg fail\n");
            break;
        }

        g_bAlgInited = TRUE;

        ///<结构化算法设置参数
        NvrFixProfeIntelSetAllParam();

        ///< set cb
        __BasicIntelliSetAlarmDetectCallBack(NvrFixProfeIntelAlarmDetectCB);

        eRet = NvrQueueCreate(&g_ptProIntelDealQueue);
        if (NVR_ERR__OK != eRet)
        {
            PROFEINTELERR("g_ptProIntelDealQueue create failed ret:%d\n", eRet);
            break;
        }

        if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrFixProfeIntelDealThread, "FixproIntelDeal", NVR_TASK_COMMON_PRIORITY, 256<<10, 0, 0, NULL))
        {
            PROFEINTELERR("FixproIntelDeal create failed\n");
            break;
        }

        if(0 != OsApi_TimerNew( &g_hClearTimer ))
        {
            PROFEINTELERR("g_hClearTimer create failed\n");
            break;
        }

        OsApi_TimerSet( g_hClearTimer, 1000, NvrFixProfeIntelAutoClearTimer, NULL);

        if (NVR_CAP_SUPPORT == tCapSoftware.tCapProfeIntelInfo.bySupSmokeFire)
        {
            if(0 != OsApi_TimerNew( &g_hSmokeFireClearTimer ))
            {
                PROFEINTELERR("g_hSmokeFireClearTimer create failed\n");
                break;
            }

            if(0 != OsApi_TimerNew( &g_hSecSmokeFireClearTimer ))
            {
                PROFEINTELERR("g_hSecSmokeFireClearTimer create failed\n");
                break;
            }
        }

        OsApi_RegCommand( "falg", (void *)NvrFixProfeIntelTest, "show pro intel status" );
		
		eRet = NVR_ERR__OK;
    } while (0);

    return eRet;
}

s32 NvrFixProfeIntelAlarmDetectCB(s32 nCapChn, EBasicIntelliAlarmType eAlarmType, void *pCBParam)
{
    u32 i = 0, j = 0;
    BOOL32 bAlarm = FALSE;
    TNvrAlarmSrc tAlmNftType;
    TNvrAlarmLinkPattern tLinkPattern;
    TAlarmEventInfo tEventInfoCbData;
	NVRSTATUS eRet = NVR_ERR__ERROR;
    u8 byReportCoiSup = 0;
    u64 dwCurMsTime;
    static u64 dwLastMsTime[NVR_MAX_LCAM_CHN_NUM][BASICINTELLI_ADVANCED_NUM];

	TLcamIntelCfg tLcamIntelCfg;
    TNvrSysAdvanceSysParam tAdvParam;
    TBasicIntelliFaceDeteceCBParam *ptFaceDetect  = NULL;

    mzero(tLcamIntelCfg);
    mzero(tAdvParam);
	
    eRet = LcamIntelGetParam(&tLcamIntelCfg);
    if (NVR_ERR__OK != eRet)
    {
        PROFEINTELMEMNOTICE("get tLcamIntelCfg failed ret:%d\n", eRet);
        return eRet;
    }

    mzero(tLinkPattern);
    mzero(tEventInfoCbData);

    PROFEINTELTEMP("chnid:%d, type:%d\n", nCapChn, eAlarmType);

    dwCurMsTime = NvrSysGetCurTimeMSec();

    NvrSysGetAdvanceSysParam(&tAdvParam);

    ///< 警戒线，区域进入，区域离开，区域入侵，人员聚集，移动侦测, 烟火检测
    switch (eAlarmType)
    {
        ///< 移动侦测
        case BASICINTELLI_ADVANCED_MOTION:
        {
            TBasicIntelliMdCBParam *ptMdCBParam = NULL;

            mzero(tAlmNftType);

            ///< 移动侦测类型转换为APPCLT对应类型
            ptMdCBParam = (TBasicIntelliMdCBParam*) pCBParam;
            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_MD;
            tAlmNftType.byAlarmNum = 0;

            for (i = 0; i < ptMdCBParam->u32RegionNum; i++)
            {
                if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_MOTION])
                {
                    PROFEINTELDBG("[%d]Alarm = %d AreaIndex = %lu\n", i, ptMdCBParam->atMdState[i].bAlarm, ptMdCBParam->atMdState[i].u32AreaIndex);
                }

                ///< 侦测区域有一个告警则bAlarm为TRUE，所有区域均没告警时bAlarm为FALSE.
                if (ptMdCBParam->atMdState[i].bAlarm)
                {
                    bAlarm = TRUE;
                    if(ptMdCBParam->atMdState[i].u32ObjType == 1)
                    {
                        tEventInfoCbData.atAbnormTargets[0].eObjType = BASICINTELLI_OBJTYPE_PED;
                    }
                    if(ptMdCBParam->atMdState[i].u32ObjType == 2)
                    {
                        tEventInfoCbData.atAbnormTargets[0].eObjType = BASICINTELLI_OBJTYPE_VEH;
                    }
                    if(ptMdCBParam->atMdState[i].u32ObjType == 3)
                    {
                        tEventInfoCbData.atAbnormTargets[0].eObjType = BASICINTELLI_OBJTYPE_NONMOTOR;
                    }
                    if(ptMdCBParam->atMdState[i].u32ObjType == 4)
                    {
                        tEventInfoCbData.atAbnormTargets[0].eObjType = BASICINTELLI_OBJTYPE_SHIP;
                    }
                    break;
                }
            }

            byReportCoiSup = 1;
            break;
        }

            ///< 警戒线
        case BASICINTELLI_ADVANCED_TRIP_LINE:
        {
            TBasicIntelliTripLineCBParam *ptCordonCBParam = NULL;

            mzero(tAlmNftType);

            ptCordonCBParam = (TBasicIntelliTripLineCBParam*) pCBParam;
            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_WARNINGLINE;

            for (i = 0; i < ptCordonCBParam->u32RegionNum; i++)
            {
                if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_TRIP_LINE])
                {
                    PROFEINTELDBG("[%d]Alarm : %d,AreaIndex : %lu\n", i, ptCordonCBParam->atTripLineStat[i].bAlarm, ptCordonCBParam->atTripLineStat[i].u32LineIndex);
                }
                ///< 区域有一个告警则bAlarm为TRUE，所有区域均没告警时bAlarm为FALSE.
                if (ptCordonCBParam->atTripLineStat[i].bAlarm)
                {
                    bAlarm = TRUE;
                    tEventInfoCbData.wTargetNum = ptCordonCBParam->u16TargetNum;
                    for(j = 0; j < ptCordonCBParam->u16TargetNum;j++)
                    {
                        tEventInfoCbData.atAbnormTargets[j].eObjType = ptCordonCBParam->atAbnormTargets[j].eObjType;
                        tEventInfoCbData.atAbnormTargets[j].wBottom = ptCordonCBParam->atAbnormTargets[j].u16Bottom;
                        tEventInfoCbData.atAbnormTargets[j].wTop = ptCordonCBParam->atAbnormTargets[j].u16Top;
                        tEventInfoCbData.atAbnormTargets[j].wRight = ptCordonCBParam->atAbnormTargets[j].u16Right;
                        tEventInfoCbData.atAbnormTargets[j].wLeft = ptCordonCBParam->atAbnormTargets[j].u16Left;
                    }

                    break;
                }
            }
                
            byReportCoiSup = 1;
			memcpy(tEventInfoCbData.atNvrIntelCordon, tLcamIntelCfg.atCordon[nCapChn].atCardonInfo[tLcamIntelCfg.atCordon[nCapChn].dwCurSel].atNvrIntelCordon,
				NVR_MAX_CORDON_NUM* sizeof(TNvrIntelCordon));
			break;
        }

            ///< 区域进入
        case BASICINTELLI_ADVANCED_REGION_ENTER:
        {
            TBasicIntelliRegionEnterCBParam *ptEnterCBParam = NULL;

            mzero(tAlmNftType);

            ptEnterCBParam = (TBasicIntelliRegionEnterCBParam*) pCBParam;
            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_ENTER_AREA;

            for (i = 0; i < ptEnterCBParam->u32RegionNum; i++)
            {
                if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_REGION_ENTER])
                {
                    PROFEINTELDBG("Region enter [%d]Alarm : %d  index:%d\n", i, ptEnterCBParam->atEnterStat[i].bAlarm, ptEnterCBParam->atEnterStat[i].u32AreaIndex);
                }
                ///< 区域有一个告警则bAlarm为TRUE，所有区域均没告警时bAlarm为FALSE.
                if (ptEnterCBParam->atEnterStat[i].bAlarm)
                {
                    bAlarm = TRUE;
                    tEventInfoCbData.wTargetNum = ptEnterCBParam->u16TargetNum;
                    for(j = 0; j < ptEnterCBParam->u16TargetNum;j++)
                    {
                        tEventInfoCbData.atAbnormTargets[j].eObjType = ptEnterCBParam->atAbnormTargets[j].eObjType;
                        tEventInfoCbData.atAbnormTargets[j].wBottom = ptEnterCBParam->atAbnormTargets[j].u16Bottom;
                        tEventInfoCbData.atAbnormTargets[j].wTop = ptEnterCBParam->atAbnormTargets[j].u16Top;
                        tEventInfoCbData.atAbnormTargets[j].wRight = ptEnterCBParam->atAbnormTargets[j].u16Right;
                        tEventInfoCbData.atAbnormTargets[j].wLeft = ptEnterCBParam->atAbnormTargets[j].u16Left;
                    }
                    break;
                }
            }

            tEventInfoCbData.dwRegionNum = ptEnterCBParam->u32RegionNum;
            byReportCoiSup = 1;
			tEventInfoCbData.tRegion = tLcamIntelCfg.atAreaEnter[nCapChn].atRegionInfo[tLcamIntelCfg.atAreaEnter[nCapChn].dwCurSel].tRegion;
            break;
        }

            ///< 区域入侵
        case BASICINTELLI_ADVANCED_REGION_INVASION:
        {
            TBasicIntelliRegionInvasionCBParam *ptInvasionCBParam = NULL;

            mzero(tAlmNftType);

            ptInvasionCBParam = (TBasicIntelliRegionInvasionCBParam*) pCBParam;
            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_AREA_INVADE;

            for (i = 0; i < ptInvasionCBParam->u32RegionNum; i++)
            {
                if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_REGION_INVASION])
                {
                    PROFEINTELDBG("Region invasion [%d]Alarm : %d index：%d\n", i, ptInvasionCBParam->atInvasionStat[i].bAlarm, ptInvasionCBParam->atInvasionStat[i].u32AreaIndex);
                }
                ///< 区域有一个告警则bAlarm为TRUE，所有区域均没告警时bAlarm为FALSE.
                if (ptInvasionCBParam->atInvasionStat[i].bAlarm)
                {
                    bAlarm = TRUE;
                    tEventInfoCbData.wTargetNum = ptInvasionCBParam->u16TargetNum;
                    for(j = 0; j < ptInvasionCBParam->u16TargetNum;j++)
                    {
                        tEventInfoCbData.atAbnormTargets[j].eObjType = ptInvasionCBParam->atAbnormTargets[j].eObjType;
                        tEventInfoCbData.atAbnormTargets[j].wBottom = ptInvasionCBParam->atAbnormTargets[j].u16Bottom;
                        tEventInfoCbData.atAbnormTargets[j].wTop = ptInvasionCBParam->atAbnormTargets[j].u16Top;
                        tEventInfoCbData.atAbnormTargets[j].wRight = ptInvasionCBParam->atAbnormTargets[j].u16Right;
                        tEventInfoCbData.atAbnormTargets[j].wLeft = ptInvasionCBParam->atAbnormTargets[j].u16Left;
                    }
                    break;
                }
            }
            tEventInfoCbData.wTargetNum = ptInvasionCBParam->u32RegionNum;

            byReportCoiSup = 1;
			tEventInfoCbData.tRegion = tLcamIntelCfg.atAreaInvasion[nCapChn].atRegionInfo[tLcamIntelCfg.atAreaInvasion[nCapChn].dwCurSel].tRegion;
            break;
        }

            ///< 区域离开
        case BASICINTELLI_ADVANCED_REGION_LEAVE:
        {
            TBasicIntelliRegionLeaveCBParam *ptLeaveCBParam = NULL;

            mzero(tAlmNftType);

            ptLeaveCBParam = (TBasicIntelliRegionLeaveCBParam*) pCBParam;
            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_LEAVE_AREA;

            for (i = 0; i < ptLeaveCBParam->u32RegionNum; i++)
            {
                if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_REGION_LEAVE])
                {
                    PROFEINTELDBG("Region leave [%d]Alarm : %d  index:%d\n", i, ptLeaveCBParam->atLeaveStat[i].bAlarm, ptLeaveCBParam->atLeaveStat[i].u32AreaIndex);
                }
                ///< 区域有一个告警则bAlarm为TRUE，所有区域均没告警时bAlarm为FALSE.
                if (ptLeaveCBParam->atLeaveStat[i].bAlarm)
                {
                    bAlarm = TRUE;
                    tEventInfoCbData.wTargetNum = ptLeaveCBParam->u16TargetNum;
                    for(j = 0; j < ptLeaveCBParam->u16TargetNum;j++)
                    {
                        tEventInfoCbData.atAbnormTargets[j].eObjType = ptLeaveCBParam->atAbnormTargets[j].eObjType;
                        tEventInfoCbData.atAbnormTargets[j].wBottom = ptLeaveCBParam->atAbnormTargets[j].u16Bottom;
                        tEventInfoCbData.atAbnormTargets[j].wTop = ptLeaveCBParam->atAbnormTargets[j].u16Top;
                        tEventInfoCbData.atAbnormTargets[j].wRight = ptLeaveCBParam->atAbnormTargets[j].u16Right;
                        tEventInfoCbData.atAbnormTargets[j].wLeft = ptLeaveCBParam->atAbnormTargets[j].u16Left;
                    }
                    break;
                }
            }
            tEventInfoCbData.wTargetNum = ptLeaveCBParam->u32RegionNum;


            byReportCoiSup = 1;
			tEventInfoCbData.tRegion = tLcamIntelCfg.atAreaLeave[nCapChn].atRegionInfo[tLcamIntelCfg.atAreaLeave[nCapChn].dwCurSel].tRegion;
            break;
        }

            ///< 人员聚集
        case BASICINTELLI_ADVANCED_GATHER_DET:
        {
            TBasicIntelliGatherCBParam *ptGatherCBParam = NULL;

            mzero(tAlmNftType);

            ptGatherCBParam = (TBasicIntelliGatherCBParam*) pCBParam;
            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_GATHER;

            if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_GATHER_DET])
            {
                PROFEINTELDBG("People gather.dwRegionNum = %u\n", ptGatherCBParam->u32RegionNum);
                for (i = 0; i < ptGatherCBParam->u32RegionNum; i++)
                {
                    PROFEINTELDBG("People gather [%d]region[%u].bAlarm = %d;\n", i, ptGatherCBParam->atGatherStat[i].u32AreaIndex, ptGatherCBParam->atGatherStat[i].bAlarm);
                }

            }
            for (i = 0; i < ptGatherCBParam->u32RegionNum; i++)
            {
                if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_GATHER_DET])
                {
                    PROFEINTELDBG("People gather [%d]Alarm : %d  index:%d\n", i, ptGatherCBParam->atGatherStat[i].bAlarm, ptGatherCBParam->atGatherStat[i].u32AreaIndex);
                }
                ///< 区域有一个告警则bAlarm为TRUE，所有区域均没告警时bAlarm为FALSE.
                if (ptGatherCBParam->atGatherStat[i].bAlarm)
                {
                    bAlarm = TRUE;
                    tEventInfoCbData.atAbnormTargets[0].eObjType = BASICINTELLI_OBJTYPE_PED;
                    break;
                }
            }

            byReportCoiSup = 1;
			tEventInfoCbData.tRegion = tLcamIntelCfg.atPeopleGather[nCapChn].atRegionInfo[tLcamIntelCfg.atPeopleGather[nCapChn].dwCurSel].tRegion;
            break;
        }

            ///< 烟火检测
        case BASICINTELLI_ADVANCED_FIREALARM:
        {
            NvrFixProfeIntelSmokeFireDetect(nCapChn, (TBasicIntelliFireAlarmCBParam*) pCBParam);
            break;
        }

        case BASICINTELLI_ADVANCED_OCCLUSION:
        {
            TBasicIntelliOcclusionCBParam *ptOcclusionCBParam = (TBasicIntelliOcclusionCBParam*) pCBParam;

            mzero(tAlmNftType);

            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_SHIELD_DETECDT;

            if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_OCCLUSION])
            {
                PROFEINTELDBG("overlay ALARM = %D\n", ptOcclusionCBParam->dwAlarm);
            }

            bAlarm = ptOcclusionCBParam->dwAlarm;
            byReportCoiSup = 1;
            break;
        }

        case BASICINTELLI_ADVANCED_DEFOCUS:
        {
            TBasicIntelliDefocusCBParam *ptDefocusCBParam = (TBasicIntelliDefocusCBParam*) pCBParam;

            mzero(tAlmNftType);

            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_VITURAL_FOCUS;

            if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_DEFOCUS])
            {
                PROFEINTELDBG("defocus ALARM = %u\n", ptDefocusCBParam->dwAlarm);
            }

            bAlarm = ptDefocusCBParam->dwAlarm;
            byReportCoiSup = 1;
            break;
        }

        case BASICINTELLI_ADVANCED_SCENECHANGE:
        {
            TBasicIntelliSceneChangeCBParam *ptSceneChangeCBParam = (TBasicIntelliSceneChangeCBParam*) pCBParam;

            mzero(tAlmNftType);

            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_SCENE_CHANGE;

            if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_SCENECHANGE])
            {
                PROFEINTELDBG("scene change ALARM = %u\n", ptSceneChangeCBParam->dwAlarm);
            }

            bAlarm = ptSceneChangeCBParam->dwAlarm;
            byReportCoiSup = 1;
            break;
        }

        case BASICINTELLI_ADVANCED_REMOVEOBJECT:
        {
            TBasicIntelliRemoveObjectCBParam *ptRODCBParam = (TBasicIntelliRemoveObjectCBParam*) pCBParam;

            mzero(tAlmNftType);

            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_TAKEAWAY;


            for (i = 0; i < ptRODCBParam->u32RegionNum; i++)
            {
                if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_REMOVEOBJECT])
                {
                    PROFEINTELDBG("obj pick  [%d]Alarm : %d index :%d\n", i, ptRODCBParam->atRemoveObject[i].bAlarm, ptRODCBParam->atRemoveObject[i].u32AreaIndex);
                }
                ///< 区域有一个告警则bAlarm为TRUE，所有区域均没告警时bAlarm为FALSE.
                if (ptRODCBParam->atRemoveObject[i].bAlarm)
                {
                    bAlarm = TRUE;
                    for (j = 0; j < ptRODCBParam->u16TargetNum; j++)
                    {
                        tEventInfoCbData.atAbnormTargets[j].eObjType = ptRODCBParam->atAbnormTargets[i].eObjType;
                        tEventInfoCbData.atAbnormTargets[j].wBottom = ptRODCBParam->atAbnormTargets[i].u16Bottom;
                        tEventInfoCbData.atAbnormTargets[j].wTop = ptRODCBParam->atAbnormTargets[i].u16Top;
                        tEventInfoCbData.atAbnormTargets[j].wRight = ptRODCBParam->atAbnormTargets[i].u16Right;
                        tEventInfoCbData.atAbnormTargets[j].wLeft = ptRODCBParam->atAbnormTargets[i].u16Left;
                    }
                    break;
                }
            }

            byReportCoiSup = 1;
            tEventInfoCbData.tRegion = tLcamIntelCfg.atObjectPick[nCapChn].atRegionInfo[tLcamIntelCfg.atObjectPick[nCapChn].dwCurSel].tRegion;

            break;
        }

        case BASICINTELLI_ADVANCED_ABANDONOBJECT:
        {
            TBasicIntelliAbandonObjectCBParam *ptRODCBParam = (TBasicIntelliAbandonObjectCBParam*) pCBParam;

            mzero(tAlmNftType);

            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_LEAVEBEHIND;


            for (i = 0; i < ptRODCBParam->u32RegionNum; i++)
            {
                if (g_buAlarmDebugPrint[BASICINTELLI_ADVANCED_ABANDONOBJECT])
                {
                    PROFEINTELDBG("obj left  [%d]Alarm : %d  index :%d\n", i, ptRODCBParam->atAbandonObject[i].bAlarm, ptRODCBParam->atAbandonObject[i].u32AreaIndex);
                }
                ///< 区域有一个告警则bAlarm为TRUE，所有区域均没告警时bAlarm为FALSE.
                if (ptRODCBParam->atAbandonObject[i].bAlarm)
                {
                    bAlarm = TRUE;
                    for (j = 0; j < ptRODCBParam->u16TargetNum; j++)
                    {
                        tEventInfoCbData.atAbnormTargets[j].eObjType = ptRODCBParam->atAbnormTargets[i].eObjType;
                        tEventInfoCbData.atAbnormTargets[j].wBottom = ptRODCBParam->atAbnormTargets[i].u16Bottom;
                        tEventInfoCbData.atAbnormTargets[j].wTop = ptRODCBParam->atAbnormTargets[i].u16Top;
                        tEventInfoCbData.atAbnormTargets[j].wRight = ptRODCBParam->atAbnormTargets[i].u16Right;
                        tEventInfoCbData.atAbnormTargets[j].wLeft = ptRODCBParam->atAbnormTargets[i].u16Left;
                    }
                    break;
                }
            }

            byReportCoiSup = 1;
            tEventInfoCbData.tRegion = tLcamIntelCfg.atObjectLeft[nCapChn].atRegionInfo[tLcamIntelCfg.atObjectLeft[nCapChn].dwCurSel].tRegion;
            break;
        }

        case BASICINTELLI_ADVANCED_FACEDETECT:
        {
            ptFaceDetect = (TBasicIntelliFaceDeteceCBParam *)pCBParam;

            mzero(tAlmNftType);

            tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
            tAlmNftType.byAlarmNum = NVR_ALARM_SD_FACE_DETECT;
            if(ptFaceDetect->u32BestNum > 0)
            {
                bAlarm = TRUE;
            }         
            PROFEINTELDBG("BASICINTELLI_ADVANCED_FACEDETECT :%d \n", bAlarm);
            break;
        }

        default:
        {
            PROFEINTELDBG("LcamBasicIntelliAlarmDetectCB chnid:%d, type:%d is not supported.\n", nCapChn, eAlarmType);
            break;
        }

    }

    g_awAlarmSta[eAlarmType][ALARM_STAT_ALARM] = bAlarm;
    g_awAlarmSta[eAlarmType][ALARM_STAT_TIME] = dwCurMsTime;
    g_awAlarmSta[eAlarmType][ALARM_STAT_CHN] = nCapChn;

    tAlmNftType.wDevId = nCapChn + 1;
    NvrAlarmGetAlarmLinkPattern(tAlmNftType, &tLinkPattern);

    tEventInfoCbData.nCapChn = nCapChn;
    tEventInfoCbData.eEventType = tAlmNftType.byAlarmType;
    tEventInfoCbData.eAlmSDType = tAlmNftType.byAlarmNum;
    
    //确保每个人脸都上报,这里上报coi，下面只触发告警
    if(tAlmNftType.byAlarmNum == NVR_ALARM_SD_FACE_DETECT && ptFaceDetect)
    {
        u8 byPubsecId = 0;
        BOOL32 bPubsecStat = 0;
		#ifdef _SSC339G_
        AisUpdQueryPubsecappStat(byPubsecId, &bPubsecStat);
		#endif
        for ( i = 0; i < ptFaceDetect->u32BestNum; i++)
        {
            TSpedvehBestInfo *ptBestInfo = &ptFaceDetect->atBestInfo[i];
            tEventInfoCbData.s64TimeStamp = ptBestInfo->s64TimeStamp;
            if (tLinkPattern.tRegularLink.byPostCoi || bPubsecStat)
            {
                // 根据配置，设置上报类型
                tEventInfoCbData.dwPostType = tLinkPattern.tRegularLink.byPostCoi ? 1 : 0;
                tEventInfoCbData.dwPostType |= bPubsecStat ? 2 : 0;
                // 联动上报COI
                NvrFixEventNotify(&tEventInfoCbData);
            }
        }
    }

    if (NVR_ALARM_STATUS_ALARM == bAlarm && ((dwCurMsTime - dwLastMsTime[nCapChn][eAlarmType]) > (tAdvParam.wAlarmDelayTime * 1000)))
    {
        PROFEINTELDBG("alarm  type:%d, chn:%d  time:%u.\n", eAlarmType, nCapChn, g_awAlarmSta[eAlarmType][ALARM_STAT_TIME]);
        ///< 上报告警
        LcamCltAlmNotify(tAlmNftType, (u16) nCapChn, bAlarm, NULL);
        //启动消警定时器
        if (g_hClearTimer)
        {
            OsApi_TimerSet( g_hClearTimer, 1000, NvrFixProfeIntelAutoClearTimer, FIX_VAL_TO_PONINT(nCapChn));
        }

        if(tLinkPattern.tRegularLink.byPostCoi && byReportCoiSup && (tAlmNftType.byAlarmNum != NVR_ALARM_SD_FACE_DETECT))
        {
            tEventInfoCbData.dwPostType = 1;
            //联动上报COI
            NvrFixEventNotify(&tEventInfoCbData);
        }
        dwLastMsTime[nCapChn][eAlarmType] = dwCurMsTime;
    }

    return 0;
}

NVRSTATUS NvrFixProfeIntelSnapshot(TBasicIntelliSnapInput *ptInput, TBasicIntelliSnapOutput *ptOutput)
{
#ifdef _BASIC_INTELLI_
    return BasicIntelliSnapshot(ptInput, ptOutput);
#else
    return NVR_ERR__OK;
#endif
}

static NVRSTATUS NvrFixProfeIntelPushEvent(ENvrFixProIntelEvent eEvent, void *pData, u32 dwDataSize)
{
    s32 nRet = 0;
    TNvrDoubleListPushAttr tCmdAttr;
    mzero(tCmdAttr);

    PROFEINTELIMP( "[PUSH] pro inetl event:%d size:"FORMAT_U32"\n", eEvent, dwDataSize);

    tCmdAttr.byPriority = NVR_QUEUE_NODE_PRIORITY_HIGH;
    tCmdAttr.byMergerType = NVR_QUEUE_NODE_MERGER_TYPE;
    tCmdAttr.dwType = (u32)eEvent;
    tCmdAttr.dwDataLen = dwDataSize;
    tCmdAttr.pchDataBuf = (s8*)pData;
    nRet = NvrQueuePush(g_ptProIntelDealQueue, &tCmdAttr);
    if (NVR_ERR__OK != nRet)
    {
        PROFEINTELERR( "g_ptProIntelDealQueue queue push failed, ret:%d\n",nRet);
        return NVR_ERR__ERROR;
    }

    return NVR_ERR__OK;
}

static void* NvrFixProfeIntelDealThread()
{
    s32 nRet = 0;
    TThreadInfoRecord tThreadInfo;
    TNvrDoubleListPopAttr tPopAttr;
    u8 byTmpDataBuf[512] = {0};

    mzero(tThreadInfo);
    mzero(tPopAttr);

    //添加线程信息
    tThreadInfo.m_dwThreadId = getpid();
    mcopy(tThreadInfo.m_strThreadName, "FixPrIntelDeal");
    OsApi_AddThreadInfo( &tThreadInfo );

    PROFEINTELDBG("NvrFixProfeIntelDealThread Thread %d created!\n", getpid());

#ifndef WIN32
    prctl(PR_SET_NAME, "FixPrIntelDeal", 0, 0, 0);
#endif


    tPopAttr.byBlockMode = NVR_QUEUE_POP_BLOCK;
    tPopAttr.dwDataLen = sizeof(byTmpDataBuf);
    tPopAttr.pchDataBuf = (s8*)byTmpDataBuf;

    while (1)
    {
//        PROFEINTELDBG("to get event from g_ptProIntelDealQueue ... \n");
        nRet = NvrQueuePop(g_ptProIntelDealQueue, &tPopAttr);
        if (NVR_ERR__OK != nRet)
        {
//            PROFEINTELERR( "g_ptProIntelDealQueue queue pop failed ret:%d\n", nRet);
            usleep(100 * 1000);
            continue;
        }

        PROFEINTELDBG("get event %d from g_ptProIntelDealQueue\n", tPopAttr.dwType);
        switch (tPopAttr.dwType)
        {
            case FIX_PROINTEL_EVENT_SMOKEFIRE_CONFIRM:
            {
                TNvrFixSmokeFireConfirmParam tSmokeFireObj;
                s32 x = 0, y = 0, w = 0, h = 0;
                if (sizeof(tSmokeFireObj) != tPopAttr.dwRealDataLen)
                {
                    PROFEINTELERR( "FIX_PROINTEL_EVENT_SMOKEFIRE_CONFIRM queue pop data size err:%d -- %d\n", sizeof(tSmokeFireObj), tPopAttr.dwRealDataLen);
                    break;
                }

                memcpy(&tSmokeFireObj, byTmpDataBuf, tPopAttr.dwDataLen);

                x = tSmokeFireObj.tObj.u16Left;
                y = tSmokeFireObj.tObj.u16Top;
                w = tSmokeFireObj.tObj.u16Right - tSmokeFireObj.tObj.u16Left;
                h = tSmokeFireObj.tObj.u16Bottom - tSmokeFireObj.tObj.u16Top;

                PROFEINTELIMP("smokefire to 3d to ptz ctrl  wChnId:%d x:%d y:%d w:%d h:%d \n", tSmokeFireObj.wChnid, x, y, w, h);

                NvrFixDevSmokeFireSencondConfirm(TRUE, tSmokeFireObj.wChnid, x, y, w, h);
                break;
            }

            case FIX_PROINTEL_EVENT_SMOKEFIRE_CONFIRM_FAIL:
            {
                TNvrFixSmokeFireConfirmParam tSmokeFireObj;
                if (sizeof(tSmokeFireObj) != tPopAttr.dwRealDataLen)
                {
                    PROFEINTELERR( "FIX_PROINTEL_EVENT_SMOKEFIRE_CONFIRM_FAIL queue pop data size err:%d -- %d\n", sizeof(tSmokeFireObj), tPopAttr.dwRealDataLen);
                    break;
                }

                memcpy(&tSmokeFireObj, byTmpDataBuf, tPopAttr.dwDataLen);
                NvrFixDevSmokeFireSencondConfirm(FALSE, tSmokeFireObj.wChnid, 0 ,0 ,0 ,0);

                NvrFixDevStartAllPtzTask();
                break;
            }

            default:
            {
                PROFEINTELERR( "========== event:"FORMAT_U32" not deal ==========\n",tPopAttr.dwType);
                break;
            }
        }

    }

    PROFEINTELDBG( "NvrFixProfeIntelDealThread Task Quit...\n" );
    OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
    OsApi_TaskExit();
    return NULL;

}

///< --------------------------------- smoke fire alg deal ---------------------------------

s32 NvrFixProfeIntelSmokeFireDetect(s32 nCapChn,  TBasicIntelliFireAlarmCBParam *ptFireAlarmCBParam)
{
    s32 i = 0;
    s32 w = 0, h = 0;
    BOOL bAlarm = FALSE;
    TNvrAlarmSrc tAlmNftType;
    u32 dwMaxAreaSize = 0;
    u8 byMaxAreaIndex = 0;

    mzero(tAlmNftType);

    tAlmNftType.byAlarmType = NVR_ALARM_TYPE_PROSMART;
    tAlmNftType.byAlarmNum = NVR_PROSD_SMOKE_FIRE;
    tAlmNftType.wDevId = nCapChn + 1;

    if (ptFireAlarmCBParam && s_bSmokeFireEnable)
    {
        for(i = 0; i < ptFireAlarmCBParam->u32RegionNum; i++)
        {
            PROFEINTELDBG ("smokefire [%d]Alarm:%d, AreaIndex:%lu\n", i, ptFireAlarmCBParam->atFireAlarmStat[i].bAlarm, ptFireAlarmCBParam->atFireAlarmStat[i].u32AreaIndex);
        }

        for(i = 0; i < ptFireAlarmCBParam->u16FireNum; i++)
        {
            PROFEINTELDBG ("smokefire [%d] fireType:%d(1:f 2:s), AreaIndex :%d pos:%d-%d-%d-%d\n", i, ptFireAlarmCBParam->atFireObjs[i].eObjType, ptFireAlarmCBParam->atFireObjs[i].u16AreaIndex,
                ptFireAlarmCBParam->atFireObjs[i].u16Left, ptFireAlarmCBParam->atFireObjs[i].u16Right, ptFireAlarmCBParam->atFireObjs[i].u16Top, ptFireAlarmCBParam->atFireObjs[i].u16Bottom);

            if (ptFireAlarmCBParam->atFireObjs[i].eObjType > 0)
            {
                w = ptFireAlarmCBParam->atFireObjs[i].u16Right - ptFireAlarmCBParam->atFireObjs[i].u16Left;
                h = ptFireAlarmCBParam->atFireObjs[i].u16Top - ptFireAlarmCBParam->atFireObjs[i].u16Bottom;
                bAlarm = TRUE;

                if ((w * h) > dwMaxAreaSize)
                {
                    byMaxAreaIndex = i;
                    dwMaxAreaSize = (w * h);
                }
            }
        }

        do
        {
            PROFEINTELDBG ("smokefire other param duconfim:%d(0:c 1:o) waitmode:%d(0:m 1:a) time:%d\n",
                    g_tSmokeFireOtherParam.eDuCnfirm, g_tSmokeFireOtherParam.eTargetWaitMode, g_tSmokeFireOtherParam.wTargetWaitTime);
            s32 nAutoClearTime = g_tSmokeFireOtherParam.wTargetWaitTime ? g_tSmokeFireOtherParam.wTargetWaitTime : 15;

            if (bAlarm)
            {
                ///< stop recv alg detect
                s_bSmokeFireEnable = FALSE;

                ///< 停止所有任务
                NvrFixDevStopAllPtzTask();
            }
            else
            {
                PROFEINTELDBG ("smokefire no alarm \n");
            }


            if (NVR_CAP_SMOKE_FIRE_MODE_OPEN == g_tSmokeFireOtherParam.eDuCnfirm)
            {
                if (g_bSecSmokeFireDeal)
                {
                    ///< 停止定时器
                    if (g_hSecSmokeFireClearTimer)
                    {
                        OsApi_TimerStop(g_hSecSmokeFireClearTimer);
                    }
                    ///< 二次确认告警
                    PROFEINTELIMP("smokefire second confirm succ\n");
                    g_bSecSmokeFireDeal = FALSE;
                    g_bSecSmokeFireAlarm = TRUE;
                }
                else
                {
                    ///< 等待二次确认 转动 ptz

                    ///< 屏蔽接下来的告警，直接重新设置算法参数
                    g_bSecSmokeFireDeal = TRUE;
                    g_bSecSmokeFireAlarm = FALSE;

                    TNvrFixSmokeFireConfirmParam tObjParam;
                    mzero(tObjParam);

                    tObjParam.wChnid = (u16)nCapChn;
                    memcpy(&tObjParam.tObj, &ptFireAlarmCBParam->atFireObjs[byMaxAreaIndex], sizeof(tObjParam.tObj));

                    NvrFixProfeIntelPushEvent(FIX_PROINTEL_EVENT_SMOKEFIRE_CONFIRM, (void *)&tObjParam, sizeof(tObjParam));

#if 0
                    s32 x = 0, y = 0, w = 0, h = 0;
                    w = tObjParam.tObj.u16Right - tObjParam.tObj.u16Left;
                    h =  tObjParam.tObj.u16Bottom - tObjParam.tObj.u16Top;
                    x = tObjParam.tObj.u16Left + w / 2;
                    y = tObjParam.tObj.u16Top + h / 2;

                    PROFEINTELIMP("smokefire to 3d to ptz ctrl  wChnId:%d x:%d y:%d w:%d h:%d \n", tObjParam.wChnid, x, y, w, h);

                    NvrFixDevSmokeFireSencondConfirm(tObjParam.wChnid, x, y, w, h);
#endif

                    break;
                }
            }

            if (NVR_CAP_SMOKE_FIRE_TAGET_WAIT_NANUAL == g_tSmokeFireOtherParam.eTargetWaitMode)
            {
                PROFEINTELIMP("===== smokefire wait manual clear alarm =====\n");
            }
            else
            {
                PROFEINTELIMP ("smokefire set timer to clear alarm\n");
                if (g_hSmokeFireClearTimer)
                {
                    OsApi_TimerSet( g_hSmokeFireClearTimer, nAutoClearTime * 1000, NvrFixProfeIntelSmokeFireAutoClearTimer, FIX_VAL_TO_PONINT(nCapChn));
                }
            }

            if (bAlarm)
            {
                LcamCltAlmNotify(tAlmNftType, (u16)nCapChn, bAlarm, NULL);
            }

        } while(0);

    }

    return 0;
}

NVRSTATUS NvrFixProfeIntelSmokefireAlgOperation(u16 wChnId, TNvrIntelSmokeFireCfg *pAlgParam, s32 nPrestIndex)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    s32 nPresetId;

    PROFEINTEL_ASSERT(pAlgParam);

    if (!g_bAlgInited)
    {
        PROFEINTELERR("alg  is not inited .\n");
        return NVR_ERR__ERROR;
    }

    nPresetId = nPrestIndex;
    if (pAlgParam->bEnable)
    {
        if (g_bSecSmokeFireDeal)
        {
            ///< 二次确认设置算法使能，开始二次确认失败的定时器
            if (g_hSecSmokeFireClearTimer)
            {
                OsApi_TimerSet( g_hSecSmokeFireClearTimer, 20 * 1000, NvrFixProfeIntelSmokeFireSecAutoClearTimer, FIX_VAL_TO_PONINT(wChnId));
            }
        }

        PROFEINTELDBG("chnid:%d presetId:%d start smoke fire\n", wChnId, nPresetId);
        NvrFixProfeIntelAlgSetParam(wChnId, BASICINTELLI_ADVANCED_FIREALARM, pAlgParam, &nPresetId);
        s_bSmokeFireEnable = TRUE;
        memcpy(&g_tSmokeFireOtherParam, &pAlgParam->tOtherInfo, sizeof(g_tSmokeFireOtherParam));

        g_bNeedSecSmokeFireFlag = NVR_CAP_SMOKE_FIRE_MODE_OPEN == pAlgParam->tOtherInfo.eDuCnfirm;
    }
    else
    {
        s_bSmokeFireEnable = FALSE;
        PROFEINTELDBG("chnid:%d presetId:%d stop smoke fire\n", wChnId, nPresetId);
    }

    return eRet;
}

NVRSTATUS NvrFixProfeIntelSmokeFireManualClear(u16 wChnId)
{
    NVRSTATUS eRet = NVR_ERR__OK;

    if (NVR_CAP_SMOKE_FIRE_TAGET_WAIT_NANUAL == g_tSmokeFireOtherParam.eTargetWaitMode)
    {
        PROFEINTELDBG("smoke fire manual clear alarm %d\n", wChnId);
        NvrFixDevStartAllPtzTask();
    }

    return eRet;
}

///< 自动消警定时器
static s32 NvrFixProfeIntelAutoClearTimer( HTIMERHANDLE dwTimerId, void* param)
{
    TNvrAlarmSrc tAlmNftType;
    u8 i;
    s32 nChnId = 0;
    s32 nDiffSec = 0;
    TNvrSysAdvanceSysParam tAdvParam;

    mzero(tAdvParam);
    mzero(tAlmNftType);

    for(i = 0; i < BASICINTELLI_ADVANCED_NUM; i++)
    {
        switch (i)
        {
            ///< 移动侦测
            case BASICINTELLI_ADVANCED_MOTION:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_MD;
                tAlmNftType.byAlarmNum = 0;
                break;
            }
            ///< 警戒线
            case BASICINTELLI_ADVANCED_TRIP_LINE:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_WARNINGLINE;
                break;
            }
            ///< 区域进入
            case BASICINTELLI_ADVANCED_REGION_ENTER:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_ENTER_AREA;
                break;
            }
            ///< 区域入侵
            case BASICINTELLI_ADVANCED_REGION_INVASION:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_AREA_INVADE;
                break;
            }
            ///< 区域离开
            case BASICINTELLI_ADVANCED_REGION_LEAVE:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_LEAVE_AREA;
                break;
            }
            ///< 人员聚集
            case BASICINTELLI_ADVANCED_GATHER_DET:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_GATHER;
                break;
            }

            ///< 遮挡
            case BASICINTELLI_ADVANCED_OCCLUSION:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_SHIELD_DETECDT;
                break;
            }
            ///< 虚焦
            case BASICINTELLI_ADVANCED_DEFOCUS:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_VITURAL_FOCUS;
                break;
            }
            ///< 场景变换
            case BASICINTELLI_ADVANCED_SCENECHANGE:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_SCENE_CHANGE;
                break;
            }
            ///< 物品拿取
            case BASICINTELLI_ADVANCED_REMOVEOBJECT:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_TAKEAWAY;
                break;
            }
            ///< 物品遗留
            case  BASICINTELLI_ADVANCED_ABANDONOBJECT:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_LEAVEBEHIND;
                break;
            }
            ///< 人脸检测
            case  BASICINTELLI_ADVANCED_FACEDETECT:
            {
                tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                tAlmNftType.byAlarmNum = NVR_ALARM_SD_FACE_DETECT;
                break;
            }
            default:
                break;
        }

        if(TRUE == g_awAlarmSta[i][ALARM_STAT_ALARM])
        {
            nChnId = g_awAlarmSta[i][ALARM_STAT_CHN];

            nDiffSec = NvrSysGetCurTimeMSec() - g_awAlarmSta[i][ALARM_STAT_TIME];

            NvrSysGetAdvanceSysParam(&tAdvParam);

            if (nDiffSec >= (tAdvParam.wAlarmDelayTime * 1000))
            {
                PROFEINTELDBG("alarm clear type:%d chn:%d time:%u diff:%d alarm:%d delay:%d\n", i, nChnId, g_awAlarmSta[i][ALARM_STAT_TIME], nDiffSec, g_awAlarmSta[i][ALARM_STAT_ALARM], tAdvParam.wAlarmDelayTime);
                g_awAlarmSta[i][ALARM_STAT_ALARM] = FALSE;
                g_awAlarmSta[i][ALARM_STAT_TIME] = 0;
                LcamCltAlmNotify(tAlmNftType, (u16)nChnId, 0, NULL);
            }
        }
    }

    OsApi_TimerSet( g_hClearTimer, 1000, NvrFixProfeIntelAutoClearTimer, NULL);

    return 0;
}

///< 烟火识别自动消警定时器
static s32 NvrFixProfeIntelSmokeFireAutoClearTimer( HTIMERHANDLE dwTimerId, void* param)
{
    s32 nChnId = FIX_POINT_TO_VAL32(param);
    PROFEINTELDBG("smoke fire auto clear alarm %d\n", nChnId);
    NvrFixDevStartAllPtzTask();

    return 0;
}

///< 烟火识别自动消警定时器
static s32 NvrFixProfeIntelSmokeFireSecAutoClearTimer( HTIMERHANDLE dwTimerId, void* param)
{
    s32 nChnId = FIX_POINT_TO_VAL32(param);

    if (g_bSecSmokeFireDeal)
    {
        ///< 二次确认告警
        PROFEINTELIMP("smokefire second confirm\n");
        g_bSecSmokeFireDeal = FALSE;
        g_bSecSmokeFireAlarm = FALSE;
    }

    TNvrFixSmokeFireConfirmParam tObjParam;
    mzero(tObjParam);


    NvrFixProfeIntelPushEvent(FIX_PROINTEL_EVENT_SMOKEFIRE_CONFIRM_FAIL, (void *)&tObjParam, sizeof(tObjParam));

    PROFEINTELDBG("timer smoke fire auto clear alarm %d\n", nChnId);
    return 0;
}

static void NvrFixProfeIntelTest(u8 byMode, u8 param1, u8 param2)
{
    PROFEINTELDBG(  "** prointell ver: %s %s **\n",__DATE__,__TIME__);

    switch (byMode)
    {
        case 0:
        {
            PROFEINTELIMP("************************************************************ pro inteli help ***************************************************************\n");
            PROFEINTELIMP("*   case 0                      param1                           param2                                                                    *\n");
            PROFEINTELIMP("*   debug print                 0---MOTION                       0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               1---TRIP_LINE                    0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               2---REGION_ENTER                 0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               3---REGION_LEAVE                 0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               4---REGION_INVASION              0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               5---GATHER_DET                   0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               6---FIREALARM                    0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               7---OCCLUSION                    0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               8-- DEFOUSE                      0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               9---SCENECHANGE                  0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               10--REMOVEOBJECT                 0:close 1:open                                                            *\n");
            PROFEINTELIMP("*                               11--ABANDONOBJECT                0:close 1:open                                                            *\n");
            PROFEINTELIMP("********************************************************************************************************************************************\n");
            break;
        }

        case 1:
        {
            if (param1 < BASICINTELLI_ADVANCED_NUM)
            {
                g_buAlarmDebugPrint[param1] = param2;
            }
            break;
        }

        case 2:
        {
            PROFEINTELDBG("s_bSmokeFireEnable:%d g_bSecSmokeFireDeal:%d g_bSecSmokeFireAlarm:%d stop smoke fire\n", s_bSmokeFireEnable, g_bSecSmokeFireDeal, g_bSecSmokeFireAlarm);
            break;
        }
    }

    return;
}

